{"rustc": 1842507548689473721, "features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"bytes\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"default\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"tauri-runtime-wry\", \"tracing\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 1305593064144852035, "profile": 15657897354478470176, "path": 8458121070050212388, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [533142347765177280, "anyhow", false, 15134541861625046067], [588322964515033593, "sys_locale", false, 13821564033639428898], [1076501750996383263, "once_cell", false, 11895857675154832601], [1144798529435568546, "tauri_utils", false, 7903705833100539925], [1389103965215294897, "flate2", false, 7649787231140060511], [2999978091831213404, "serde_repr", false, 17170892377572136723], [3100833517036967723, "uuid", false, 3269919355752741088], [3353796506826114049, "thiserror", false, 6358471964182336296], [3597337515006351837, "tauri_macros", false, 14818312803357529365], [3851720982022213547, "semver", false, 829930568886643676], [3988549704697787137, "open", false, 17181283447184401890], [4381063397040571828, "webview2_com", false, 17950373685392314976], [4405182208873388884, "http", false, 7948823251931111416], [4450062412064442726, "dirs_next", false, 17057292645121069208], [4704408070981680310, "serde_json", false, 11199486250301317617], [4788400838143230126, "tauri_runtime_wry", false, 16037192672092647275], [4919829919303820331, "serialize_to_javascript", false, 11060394384835345133], [5099504066399492044, "rfd", false, 7729655232951601710], [5335596306115456489, "os_info", false, 1655580454347936676], [6235911559405361224, "tar", false, 6594206178580573623], [7244058819997729774, "reqwest", false, 777594524521948958], [7653476968652377684, "windows", false, 6375946999918238853], [7670211519503158651, "getrandom", false, 14916295844813859781], [8145704335312028338, "glob", false, 8611815146833412150], [8800316697867969272, "regex", false, 11804285240242442798], [8858041990736880586, "tokio", false, 5176513659765035562], [9177686306454747611, "ignore", false, 4384249271612008841], [9351479248917069247, "encoding_rs", false, 162077215968353066], [10642096736294159704, "shared_child", false, 18228841833602550536], [11269248247346606853, "url", false, 12915646722162745668], [11637111059468842078, "tempfile", false, 8779970043238303641], [11693073011723388840, "raw_window_handle", false, 5676378102289126459], [11732963870180628222, "os_pipe", false, 12188025386110588223], [11913130400938634928, "futures_util", false, 1357397610844759036], [13208667028893622512, "rand", false, 18054765264797586153], [14626413149905853098, "tracing", false, 2674625738167854746], [14854634077503860916, "build_script_build", false, 383934425637100979], [14923790796823607459, "indexmap", false, 7286516472664897815], [15005373913467947101, "notify_rust", false, 1038884957423362780], [16227728351758841112, "bytes", false, 13084351430820299866], [16534286206067577747, "tauri_runtime", false, 15310161457339588962], [17174345729924723953, "serde", false, 2591028312899638256], [17278893514130263345, "state", false, 13158404719299013538], [18053436020821374870, "dunce", false, 8109531313742568891]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-95af9dc0e43b1007\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}