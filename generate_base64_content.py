#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base64内容生成器
为Gitee页面生成加密的更新信息
"""

import json
import base64
from datetime import datetime

def generate_update_info():
    """生成更新信息JSON"""
    update_info = {
        "version": "2.1.5",
        "release_date": "2024-06-29",
        "full_download_urls": [
            "https://gitee.com/mango_mu/tera-files/releases/download/v2.1.5/tera-client-full.zip",
            "https://backup1.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
            "https://backup2.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
            "https://backup3.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
            "https://backup4.teralauncher.com/downloads/v2.1.5/tera-client-full.zip"
        ],
        "incremental_server_url": "https://update.teralauncher.com/incremental/v2.1.5",
        "file_list_url": "https://update.teralauncher.com/api/v2.1.5/files.json",
        "checksum": "sha256:a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
        "download_info": {
            "total_size": 2147483648,  # 2GB
            "compressed_size": 1073741824,  # 1GB
            "file_count": 15420,
            "estimated_download_time": "15-30 minutes"
        },
        "changelog": [
            "修复了游戏启动时的崩溃问题",
            "优化了下载速度和稳定性",
            "增加了新的角色创建选项",
            "修复了部分UI显示问题",
            "提升了整体游戏性能"
        ],
        "system_requirements": {
            "min_os": "Windows 10",
            "min_ram": "4GB",
            "min_storage": "50GB",
            "recommended_ram": "8GB",
            "recommended_storage": "100GB"
        },
        "security": {
            "signature": "RSA-2048:MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
            "certificate_thumbprint": "1A2B3C4D5E6F7890ABCDEF1234567890ABCDEF12",
            "verification_url": "https://verify.teralauncher.com/signature/v2.1.5"
        }
    }
    return update_info

def create_base64_content():
    """创建Base64编码的内容"""
    print("🔐 生成Tera Launcher更新信息...")
    
    # 生成更新信息
    update_info = generate_update_info()
    
    # 转换为JSON字符串
    json_str = json.dumps(update_info, ensure_ascii=False, separators=(',', ':'))
    
    # Base64编码
    encoded_bytes = base64.b64encode(json_str.encode('utf-8'))
    encoded_str = encoded_bytes.decode('ascii')
    
    print(f"✅ 原始JSON长度: {len(json_str)} 字符")
    print(f"✅ Base64编码长度: {len(encoded_str)} 字符")
    
    return {
        'original_json': json_str,
        'base64_encoded': encoded_str,
        'update_info': update_info
    }

def create_gitee_page_content(base64_content):
    """创建完整的Gitee页面内容"""
    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>Tera Launcher 官方更新页面 - Gitee</title>
    <meta name="description" content="Tera Launcher 最新版本下载和更新信息">
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .version-info {{
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .changelog {{
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .download-section {{
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }}
        .hidden-data {{
            display: none;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Tera Launcher</h1>
            <h2>官方更新页面</h2>
            <p>最后更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="version-info">
            <h3>📋 当前版本信息</h3>
            <p><strong>版本号:</strong> v2.1.5</p>
            <p><strong>发布日期:</strong> 2024-06-29</p>
            <p><strong>文件大小:</strong> 约 1GB (压缩后)</p>
            <p><strong>预计下载时间:</strong> 15-30分钟</p>
        </div>
        
        <div class="changelog">
            <h3>📝 更新日志</h3>
            <ul>
                <li>修复了游戏启动时的崩溃问题</li>
                <li>优化了下载速度和稳定性</li>
                <li>增加了新的角色创建选项</li>
                <li>修复了部分UI显示问题</li>
                <li>提升了整体游戏性能</li>
            </ul>
        </div>
        
        <div class="download-section">
            <h3>⚠️ 重要提示</h3>
            <p>请使用官方启动器进行下载和更新，确保游戏文件的完整性和安全性。</p>
            <p>如遇到下载问题，启动器会自动切换到备用下载源。</p>
        </div>
        
        <!-- 隐藏的更新数据区域 -->
        <div class="hidden-data">
            <p>以下是加密的更新信息，仅供启动器程序读取：</p>
            [[ {base64_content} ]]
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>© 2024 Tera Launcher Team | 官方更新页面</p>
            <p>如有问题请联系技术支持</p>
        </div>
    </div>
</body>
</html>"""
    
    return html_template

def main():
    print("🔐 Tera Launcher Base64内容生成器")
    print("=" * 50)
    
    # 生成Base64内容
    content_data = create_base64_content()
    
    print("\n📄 生成的内容信息:")
    print(f"版本: {content_data['update_info']['version']}")
    print(f"下载URL数量: {len(content_data['update_info']['full_download_urls'])}")
    print(f"文件总数: {content_data['update_info']['download_info']['file_count']}")
    
    print("\n🔐 Base64编码内容:")
    print("-" * 50)
    print(content_data['base64_encoded'])
    print("-" * 50)
    
    # 创建完整的Gitee页面
    gitee_page = create_gitee_page_content(content_data['base64_encoded'])
    
    # 保存文件
    try:
        # 保存Base64内容到文件
        with open("base64_content.txt", "w", encoding="utf-8") as f:
            f.write(content_data['base64_encoded'])
        
        # 保存完整的Gitee页面
        with open("gitee_test_page.html", "w", encoding="utf-8") as f:
            f.write(gitee_page)
        
        # 保存原始JSON（用于验证）
        with open("original_update_info.json", "w", encoding="utf-8") as f:
            json.dump(content_data['update_info'], f, ensure_ascii=False, indent=2)
        
        print("\n✅ 文件生成成功:")
        print("📄 base64_content.txt - Base64编码内容")
        print("🌐 gitee_test_page.html - 完整的Gitee测试页面")
        print("📋 original_update_info.json - 原始JSON数据")
        
        print("\n🔍 使用方法:")
        print("1. 将 base64_content.txt 中的内容复制到您的Gitee页面")
        print("2. 使用格式: [[ 这里粘贴Base64内容 ]]")
        print("3. 或者直接使用生成的 gitee_test_page.html 进行测试")
        
        print("\n🧪 测试步骤:")
        print("1. 打开 test_gitee_content.html")
        print("2. 输入测试页面URL或使用生成的页面")
        print("3. 点击'测试Gitee内容获取'按钮")
        print("4. 查看解密结果")
        
    except Exception as e:
        print(f"❌ 文件保存失败: {e}")

if __name__ == "__main__":
    main()
