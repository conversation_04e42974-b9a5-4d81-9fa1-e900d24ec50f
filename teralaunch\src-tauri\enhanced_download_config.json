{"enhanced_download_system": {"version": "1.0.0", "description": "Tera Launcher Enhanced Download System Configuration", "enabled": true, "fallback_to_original": true, "config": {"gitee_url": "https://gitee.com/mango_mu/test", "weibo_url": "", "retry_interval_seconds": 30, "address_switch_interval_seconds": 30, "max_retry_attempts": 5, "download_timeout_seconds": 300, "chunk_size": 1048576, "max_concurrent_downloads": 4, "enable_resume": true, "enable_chunked_download": true, "speed_limit": null}, "network_settings": {"user_agent": "Tera-Launcher/1.0 Enhanced", "connection_pool_size": 10, "connection_timeout_seconds": 30, "read_timeout_seconds": 300, "max_redirects": 5, "enable_compression": true, "enable_http2": true}, "security_settings": {"verify_ssl_certificates": true, "enable_checksum_verification": true, "hash_algorithm": "SHA256", "enable_signature_verification": false}, "performance_settings": {"adaptive_chunk_size": true, "min_chunk_size": 65536, "max_chunk_size": 10485760, "bandwidth_detection": true, "auto_adjust_concurrent_downloads": true, "memory_limit_mb": 256}, "logging": {"enable_detailed_logging": true, "log_level": "info", "log_download_progress": true, "log_network_errors": true, "log_performance_metrics": true}, "ui_settings": {"progress_update_interval_ms": 100, "show_detailed_progress": true, "show_speed_graph": true, "show_eta": true, "show_current_url": false}, "advanced_features": {"enable_p2p_acceleration": false, "enable_cdn_optimization": true, "enable_mirror_selection": true, "enable_bandwidth_throttling": false, "enable_download_scheduling": false}}, "url_sources": {"primary": {"name": "<PERSON><PERSON><PERSON>", "url": "https://gitee.com/mango_mu/test", "priority": 1, "enabled": true, "timeout_seconds": 30}, "secondary": {"name": "Weibo", "url": "", "priority": 2, "enabled": false, "timeout_seconds": 30}, "backup": {"name": "GitHub", "url": "", "priority": 3, "enabled": false, "timeout_seconds": 30}}, "download_sources": {"netdisk_services": [{"name": "LanZou", "parser_url": "https://parse.lxown.com/Netdisk_directlink", "supported_domains": ["lanzou.com", "ilanzou.com"], "enabled": true, "timeout_seconds": 30}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parser_url": "", "supported_domains": ["pan.baidu.com"], "enabled": false, "timeout_seconds": 30}], "direct_download": {"max_parallel_connections": 8, "enable_range_requests": true, "enable_connection_reuse": true, "connection_keep_alive_seconds": 300}}, "error_handling": {"retry_strategies": {"network_error": {"max_retries": 3, "backoff_strategy": "exponential", "initial_delay_seconds": 1, "max_delay_seconds": 60}, "server_error": {"max_retries": 5, "backoff_strategy": "linear", "initial_delay_seconds": 5, "max_delay_seconds": 30}, "checksum_error": {"max_retries": 2, "backoff_strategy": "immediate", "initial_delay_seconds": 0, "max_delay_seconds": 0}}, "fallback_behavior": {"switch_to_next_url_on_failure": true, "switch_to_original_system_on_critical_failure": true, "notify_user_on_repeated_failures": true}}, "monitoring": {"enable_performance_monitoring": true, "enable_error_reporting": true, "enable_usage_analytics": false, "metrics_collection_interval_seconds": 60, "performance_thresholds": {"min_download_speed_kbps": 100, "max_acceptable_failure_rate": 0.1, "max_acceptable_retry_rate": 0.2}}, "compatibility": {"minimum_rust_version": "1.70.0", "supported_platforms": ["windows", "linux", "macos"], "required_features": ["tokio", "reqwest", "serde"], "optional_features": ["tls", "compression", "http2"]}, "migration": {"from_original_system": {"preserve_download_history": true, "migrate_configuration": true, "backup_original_config": true}, "rollback_options": {"enable_quick_rollback": true, "preserve_enhanced_logs": true, "restore_original_behavior": true}}}