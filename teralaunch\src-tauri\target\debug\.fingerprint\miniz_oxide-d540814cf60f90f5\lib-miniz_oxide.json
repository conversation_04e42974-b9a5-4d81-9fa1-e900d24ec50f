{"rustc": 1842507548689473721, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 15017174250221450766, "profile": 15657897354478470176, "path": 14018247784239495431, "deps": [[4593240720617309995, "<PERSON><PERSON>", false, 5658939366005646097]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-d540814cf60f90f5\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}