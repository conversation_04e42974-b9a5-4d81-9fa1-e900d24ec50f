[package]
name = "teralib"
version = "0.1.0"
edition = "2021"

[lib]
name = "teralib"
path = "src/lib.rs"
crate-type = ["cdylib", "rlib"]



[[bin]]
name = "tera_launcher"
path = "src/main.rs"



[dependencies]
prost = "0.12.4"
prost-types = "0.12.4"
winapi = { version = "0.3.9", features = ["processthreadsapi", "winnt", "winuser", "libloaderapi", "windef", "minwindef", "handleapi", "synchapi", "errhandlingapi", "winbase"] }
protobuf = "3.4.0"
lazy_static = "1.4.0"
tokio = { version = "1.37.0", features = ["full"] }
tokio-macros = "2.2.0"
log = "0.4.22"
env_logger = "0.11.3"
parking_lot = "0.12.1"
reqwest = { version = "0.12.4", features = ["json"] }
serde_json = "1.0.120"
once_cell = "1.18.0"
dotenv = "0.15.0"



[build-dependencies]
prost-build = "0.12.4"