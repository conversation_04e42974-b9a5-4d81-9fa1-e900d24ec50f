{"rustc": 1842507548689473721, "features": "[\"arboard\", \"clipboard\", \"global-shortcut\", \"objc-exception\", \"tracing\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 12286328633679254747, "profile": 2040997289075261528, "path": 7448810318923470451, "deps": [[1144798529435568546, "tauri_utils", false, 10955185112020874079], [3100833517036967723, "uuid", false, 3552296484931498028], [4381063397040571828, "webview2_com", false, 16178044120070079241], [4788400838143230126, "build_script_build", false, 12677880548147754034], [7483013550296171226, "arboard", false, 1895265798192902918], [7653476968652377684, "windows", false, 14235264620752131749], [11693073011723388840, "raw_window_handle", false, 17436084257692388002], [13208667028893622512, "rand", false, 9325621976732195567], [14626413149905853098, "tracing", false, 15162072962576219282], [16534286206067577747, "tauri_runtime", false, 12487180336084401378], [17068110978337472778, "wry", false, 4363535769931983588]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-9b95f5b149f3c150\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}