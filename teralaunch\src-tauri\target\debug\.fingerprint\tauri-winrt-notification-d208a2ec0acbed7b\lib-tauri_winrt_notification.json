{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 16434929074262014398, "profile": 15657897354478470176, "path": 7533879900041199067, "deps": [[9619741961126177475, "windows", false, 13569091775065145492], [10985752566541544495, "quick_xml", false, 13083311370799649026]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-d208a2ec0acbed7b\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}