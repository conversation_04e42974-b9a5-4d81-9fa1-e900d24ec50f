# 简单的 Tera Rust Launcher 测试脚本

Write-Host "🚀 Tera Rust Launcher 简单测试" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green
Write-Host ""

$ServerUrl = "http://localhost:8080"
$TestsPassed = 0
$TestsTotal = 0

function Test-Simple {
    param([string]$Name, [scriptblock]$TestBlock)
    
    $script:TestsTotal++
    Write-Host "🔍 测试: $Name" -ForegroundColor Yellow
    
    try {
        $result = & $TestBlock
        if ($result) {
            Write-Host "   ✅ 通过" -ForegroundColor Green
            $script:TestsPassed++
        } else {
            Write-Host "   ❌ 失败" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试 1: 服务器健康检查
Test-Simple "服务器健康检查" {
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/health" -TimeoutSec 5
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 测试 2: 哈希文件 API
Test-Simple "哈希文件获取" {
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/tera/launcher/hash-file.json" -TimeoutSec 5
        return $response.StatusCode -eq 200 -and $response.Content -like "*files*"
    }
    catch {
        return $false
    }
}

# 测试 3: 登录 API
Test-Simple "用户登录测试" {
    try {
        $response = Invoke-WebRequest -Uri "$ServerUrl/tera/LauncherLoginAction" -Method POST -Body "login=testuser&password=testpass" -ContentType "application/x-www-form-urlencoded" -TimeoutSec 5
        return $response.StatusCode -eq 200 -and $response.Content -like "*OK*"
    }
    catch {
        return $false
    }
}

# 测试 4: 启动器文件存在
Test-Simple "启动器可执行文件" {
    return Test-Path "teralaunch\src-tauri\target\release\teralaunch.exe"
}

# 测试 5: 配置文件
Test-Simple "配置文件存在" {
    return Test-Path "teralib\src\config\config.json"
}

# 测试 6: 游戏目录
Test-Simple "游戏目录存在" {
    return Test-Path "C:\tera"
}

# 输出结果
Write-Host ""
Write-Host "📊 测试结果" -ForegroundColor Magenta
Write-Host "==========" -ForegroundColor Magenta
Write-Host "通过: $TestsPassed / $TestsTotal" -ForegroundColor White

$successRate = [math]::Round(($TestsPassed / $TestsTotal) * 100, 2)
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($TestsPassed -eq $TestsTotal) {
    Write-Host ""
    Write-Host "🎉 所有测试通过！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 启动 Tera Launcher:" -ForegroundColor Cyan
    Write-Host "   .\teralaunch\src-tauri\target\release\teralaunch.exe" -ForegroundColor White
    exit 0
} else {
    Write-Host ""
    Write-Host "⚠️  部分测试失败，请检查配置。" -ForegroundColor Yellow
    exit 1
}
