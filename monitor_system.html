<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tera Launcher 增强下载系统监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .metric-label {
            font-weight: 500;
        }

        .metric-value {
            font-weight: bold;
            color: #667eea;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .log-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 12px;
            margin-bottom: 5px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        .log-success {
            background: #d4edda;
            color: #155724;
        }

        .log-warning {
            background: #fff3cd;
            color: #856404;
        }

        .log-error {
            background: #f8d7da;
            color: #721c24;
        }

        .controls {
            text-align: center;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Tera Launcher 增强下载系统</h1>
            <p>实时监控面板 - 生产环境</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="initializeSystem()">初始化系统</button>
            <button class="btn" onclick="testConnection()">测试连接</button>
            <button class="btn" onclick="refreshStats()">刷新统计</button>
            <button class="btn" onclick="clearLogs()">清空日志</button>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>🔧 系统状态</h3>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator" id="systemStatus"></span>
                        系统状态
                    </span>
                    <span class="metric-value" id="systemStatusText">检查中...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">运行时间</span>
                    <span class="metric-value" id="uptime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">活动下载</span>
                    <span class="metric-value" id="activeDownloads">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">系统版本</span>
                    <span class="metric-value">v1.0.0</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 性能指标</h3>
                <div class="metric">
                    <span class="metric-label">下载速度</span>
                    <span class="metric-value" id="downloadSpeed">-- MB/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value" id="successRate">--%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">故障切换次数</span>
                    <span class="metric-value" id="failoverCount">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">内存使用</span>
                    <span class="metric-value" id="memoryUsage">-- MB</span>
                </div>
            </div>

            <div class="card">
                <h3>🌐 网络状态</h3>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator" id="giteeStatus"></span>
                        Gitee 连接
                    </span>
                    <span class="metric-value" id="giteeStatusText">检查中...</span>
                </div>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator" id="weiboStatus"></span>
                        微博 连接
                    </span>
                    <span class="metric-value" id="weiboStatusText">未配置</span>
                </div>
                <div class="metric">
                    <span class="metric-label">延迟</span>
                    <span class="metric-value" id="networkLatency">-- ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">带宽</span>
                    <span class="metric-value" id="bandwidth">-- Mbps</span>
                </div>
            </div>

            <div class="card">
                <h3>📈 下载统计</h3>
                <div class="metric">
                    <span class="metric-label">总下载量</span>
                    <span class="metric-value" id="totalDownloaded">0 MB</span>
                </div>
                <div class="metric">
                    <span class="metric-label">成功下载</span>
                    <span class="metric-value" id="successfulDownloads">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">失败下载</span>
                    <span class="metric-value" id="failedDownloads">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">平均速度</span>
                    <span class="metric-value" id="averageSpeed">-- MB/s</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>📋 实时日志</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry log-info">
                    [系统] 监控面板已启动，等待系统初始化...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟监控数据（在实际环境中，这些数据会来自Tauri后端）
        let systemStartTime = Date.now();
        let isSystemInitialized = false;
        let monitoringInterval;

        // 添加日志条目
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条目数量
            const logEntries = logContainer.children;
            if (logEntries.length > 100) {
                logContainer.removeChild(logEntries[0]);
            }
        }

        // 更新系统状态
        function updateSystemStatus(status, text) {
            const statusIndicator = document.getElementById('systemStatus');
            const statusText = document.getElementById('systemStatusText');
            
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // 更新网络状态
        function updateNetworkStatus(service, status, text) {
            const statusIndicator = document.getElementById(`${service}Status`);
            const statusText = document.getElementById(`${service}StatusText`);
            
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        // 初始化系统
        async function initializeSystem() {
            addLog('正在初始化增强下载系统...', 'info');
            updateSystemStatus('warning', '初始化中...');
            
            try {
                // 在实际环境中，这里会调用Tauri命令
                // const result = await invoke('initialize_enhanced_download_system');
                
                // 模拟初始化过程
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                isSystemInitialized = true;
                updateSystemStatus('online', '运行中');
                addLog('增强下载系统初始化成功', 'success');
                
                // 开始监控
                startMonitoring();
                
            } catch (error) {
                updateSystemStatus('offline', '初始化失败');
                addLog(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试连接
        async function testConnection() {
            addLog('测试网络连接...', 'info');
            
            try {
                // 测试Gitee连接
                updateNetworkStatus('gitee', 'warning', '测试中...');
                await new Promise(resolve => setTimeout(resolve, 1000));
                updateNetworkStatus('gitee', 'online', '连接正常');
                addLog('Gitee连接测试成功', 'success');
                
                // 更新延迟信息
                document.getElementById('networkLatency').textContent = '45 ms';
                document.getElementById('bandwidth').textContent = '100 Mbps';
                
            } catch (error) {
                updateNetworkStatus('gitee', 'offline', '连接失败');
                addLog(`连接测试失败: ${error.message}`, 'error');
            }
        }

        // 刷新统计信息
        async function refreshStats() {
            if (!isSystemInitialized) {
                addLog('请先初始化系统', 'warning');
                return;
            }
            
            addLog('刷新统计信息...', 'info');
            
            try {
                // 在实际环境中，这里会调用Tauri命令
                // const stats = await invoke('get_enhanced_download_statistics');
                
                // 模拟统计数据
                const stats = {
                    totalDownloaded: Math.floor(Math.random() * 1000) + 500,
                    successfulDownloads: Math.floor(Math.random() * 50) + 20,
                    failedDownloads: Math.floor(Math.random() * 5),
                    averageSpeed: (Math.random() * 10 + 5).toFixed(1),
                    activeDownloads: Math.floor(Math.random() * 3),
                    memoryUsage: (Math.random() * 50 + 20).toFixed(1)
                };
                
                // 更新UI
                document.getElementById('totalDownloaded').textContent = `${stats.totalDownloaded} MB`;
                document.getElementById('successfulDownloads').textContent = stats.successfulDownloads;
                document.getElementById('failedDownloads').textContent = stats.failedDownloads;
                document.getElementById('averageSpeed').textContent = `${stats.averageSpeed} MB/s`;
                document.getElementById('activeDownloads').textContent = stats.activeDownloads;
                document.getElementById('memoryUsage').textContent = `${stats.memoryUsage} MB`;
                
                // 计算成功率
                const total = stats.successfulDownloads + stats.failedDownloads;
                const successRate = total > 0 ? ((stats.successfulDownloads / total) * 100).toFixed(1) : 0;
                document.getElementById('successRate').textContent = `${successRate}%`;
                
                addLog('统计信息已更新', 'success');
                
            } catch (error) {
                addLog(`刷新统计失败: ${error.message}`, 'error');
            }
        }

        // 清空日志
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry log-info">[系统] 日志已清空</div>';
        }

        // 开始监控
        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }
            
            monitoringInterval = setInterval(() => {
                // 更新运行时间
                const uptime = Date.now() - systemStartTime;
                const hours = Math.floor(uptime / (1000 * 60 * 60));
                const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m ${seconds}s`;
                
                // 模拟实时下载速度
                const currentSpeed = (Math.random() * 20 + 5).toFixed(1);
                document.getElementById('downloadSpeed').textContent = `${currentSpeed} MB/s`;
                
                // 随机生成一些监控日志
                if (Math.random() < 0.1) { // 10%概率生成日志
                    const messages = [
                        '下载任务完成',
                        '故障切换到备用源',
                        '网络连接检查正常',
                        '内存使用优化完成',
                        '缓存清理完成'
                    ];
                    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                    addLog(randomMessage, 'info');
                }
                
            }, 1000);
            
            addLog('实时监控已启动', 'success');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('监控面板加载完成', 'info');
            addLog('点击"初始化系统"开始使用增强下载功能', 'info');
            
            // 设置初始状态
            updateSystemStatus('offline', '未初始化');
            updateNetworkStatus('gitee', 'offline', '未测试');
            updateNetworkStatus('weibo', 'offline', '未配置');
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }
        });
    </script>
</body>
</html>
