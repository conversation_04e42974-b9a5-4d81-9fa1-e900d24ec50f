// 增强下载系统的 Tauri 命令集成
// 将增强下载功能集成到现有的 Tauri 应用中

use crate::enhanced_download::*;
use crate::{FileInfo, get_game_path};
use futures_util::StreamExt;
use tauri::{Window, State};
use std::sync::Arc;
use tokio::sync::Mutex;
use serde_json::json;
use uuid::Uuid;

// ============================================================================
// 1. 全局状态管理
// ============================================================================

pub struct EnhancedDownloadState {
    pub manager: Arc<Mutex<Option<EnhancedDownloadManager>>>,
    pub config: Arc<Mutex<EnhancedDownloadConfig>>,
}

impl EnhancedDownloadState {
    pub fn new() -> Self {
        Self {
            manager: Arc::new(Mutex::new(None)),
            config: Arc::new(Mutex::new(EnhancedDownloadConfig::default())),
        }
    }
}

// ============================================================================
// 2. Tauri 命令实现
// ============================================================================

#[tauri::command]
pub async fn initialize_enhanced_download_system(
    state: State<'_, EnhancedDownloadState>,
    gitee_url: Option<String>,
    weibo_url: Option<String>,
    max_concurrent: Option<usize>,
    enable_resume: Option<bool>,
) -> Result<String, String> {
    let mut config = state.config.lock().await;
    
    if let Some(url) = gitee_url {
        config.gitee_url = url;
    }
    if let Some(url) = weibo_url {
        config.weibo_url = url;
    }
    if let Some(concurrent) = max_concurrent {
        config.max_concurrent_downloads = concurrent;
    }
    if let Some(resume) = enable_resume {
        config.enable_resume = resume;
    }

    let manager = EnhancedDownloadManager::new(config.clone());
    let mut manager_guard = state.manager.lock().await;
    *manager_guard = Some(manager);

    log::info!("Enhanced download system initialized successfully");
    Ok("Enhanced download system initialized successfully".to_string())
}

#[tauri::command]
pub async fn fetch_enhanced_update_info(
    state: State<'_, EnhancedDownloadState>,
) -> Result<serde_json::Value, String> {
    let manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_ref()
        .ok_or("Enhanced download system not initialized")?;

    let web_fetcher = WebContentFetcher::new(state.config.lock().await.clone());
    
    match web_fetcher.fetch_encrypted_update_info().await {
        Ok(encrypted_info) => {
            match web_fetcher.decrypt_update_info(&encrypted_info) {
                Ok(decrypted_info) => {
                    Ok(json!({
                        "success": true,
                        "version": decrypted_info.version,
                        "full_download_urls_count": decrypted_info.full_download_urls.len(),
                        "incremental_server_url": decrypted_info.incremental_server_url,
                        "checksum": decrypted_info.checksum
                    }))
                }
                Err(e) => {
                    Ok(json!({
                        "success": false,
                        "error": format!("Decryption failed: {}", e)
                    }))
                }
            }
        }
        Err(e) => {
            Ok(json!({
                "success": false,
                "error": format!("Failed to fetch update info: {}", e)
            }))
        }
    }
}

#[tauri::command]
pub async fn enhanced_download_all_files(
    state: State<'_, EnhancedDownloadState>,
    files_to_update: Vec<FileInfo>,
    window: Window,
) -> Result<String, String> {
    let mut manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_mut()
        .ok_or("Enhanced download system not initialized")?;

    if files_to_update.is_empty() {
        log::info!("No files to download");
        if let Err(e) = window.emit("enhanced_download_complete", json!({"success": true, "message": "No files to download"})) {
            log::error!("Failed to emit enhanced_download_complete event: {}", e);
        }
        return Ok("No files to download".to_string());
    }

    let total_files = files_to_update.len();
    let total_size: u64 = files_to_update.iter().map(|f| f.size).sum();
    
    log::info!("Starting enhanced download of {} files, total size: {} bytes", total_files, total_size);

    // 创建进度回调
    let window_clone = window.clone();
    let mut downloaded_files = 0;
    let mut total_downloaded = 0u64;
    
    let progress_callback = move |progress: EnhancedDownloadProgress| {
        let payload = json!({
            "type": "enhanced_download_progress",
            "task_id": progress.task_id,
            "downloaded_bytes": progress.downloaded_bytes,
            "total_bytes": progress.total_bytes,
            "speed_bytes_per_sec": progress.speed_bytes_per_sec,
            "eta_seconds": progress.eta_seconds,
            "current_url": progress.current_url,
            "status": progress.status,
            "progress_percentage": if progress.total_bytes > 0 {
                (progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0
            } else {
                0.0
            },
            "total_files": total_files,
            "completed_files": downloaded_files,
            "total_downloaded": total_downloaded,
            "total_size": total_size
        });

        if let Err(e) = window_clone.emit("enhanced_download_progress", payload) {
            log::error!("Failed to emit enhanced progress event: {}", e);
        }
    };

    // 启动增量下载任务
    let window_for_task = window.clone();
    tokio::spawn(async move {
        match manager.download_incremental_updates(files_to_update, progress_callback).await {
            Ok(results) => {
                let successful_downloads = results.iter().filter(|r| r.success).count();
                let failed_downloads = results.len() - successful_downloads;
                let total_downloaded_bytes: u64 = results.iter().map(|r| r.downloaded_bytes).sum();
                
                let completion_payload = json!({
                    "type": "enhanced_download_complete",
                    "success": failed_downloads == 0,
                    "total_files": results.len(),
                    "successful_downloads": successful_downloads,
                    "failed_downloads": failed_downloads,
                    "total_downloaded_bytes": total_downloaded_bytes,
                    "results": results
                });
                
                if let Err(e) = window_for_task.emit("enhanced_download_complete", completion_payload) {
                    log::error!("Failed to emit enhanced completion event: {}", e);
                }
            }
            Err(e) => {
                let error_payload = json!({
                    "type": "enhanced_download_error",
                    "success": false,
                    "error": e
                });
                if let Err(e) = window_for_task.emit("enhanced_download_complete", error_payload) {
                    log::error!("Failed to emit enhanced error event: {}", e);
                }
            }
        }
    });

    Ok("Enhanced download started".to_string())
}

#[tauri::command]
pub async fn enhanced_update_single_file(
    state: State<'_, EnhancedDownloadState>,
    file_info: FileInfo,
    window: Window,
) -> Result<serde_json::Value, String> {
    let mut manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_mut()
        .ok_or("Enhanced download system not initialized")?;

    let game_path = get_game_path()?;
    let output_path = game_path.join(&file_info.path);

    // 创建进度回调
    let window_clone = window.clone();
    let file_name = file_info.path.clone();
    
    let progress_callback = move |progress: EnhancedDownloadProgress| {
        let payload = json!({
            "type": "enhanced_single_file_progress",
            "file_name": file_name,
            "task_id": progress.task_id,
            "downloaded_bytes": progress.downloaded_bytes,
            "total_bytes": progress.total_bytes,
            "speed_bytes_per_sec": progress.speed_bytes_per_sec,
            "eta_seconds": progress.eta_seconds,
            "current_url": progress.current_url,
            "progress_percentage": if progress.total_bytes > 0 {
                (progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0
            } else {
                0.0
            }
        });

        if let Err(e) = window_clone.emit("enhanced_single_file_progress", payload) {
            log::error!("Failed to emit single file progress event: {}", e);
        }
    };

    // 创建单文件下载任务
    let task = EnhancedDownloadTask {
        task_id: format!("single_file_{}", Uuid::new_v4()),
        update_type: UpdateType::Incremental,
        urls: vec![file_info.url.clone()],
        output_path,
        expected_checksum: Some(file_info.hash.clone()),
        current_url_index: 0,
        retry_count: 0,
        last_attempt: None,
    };

    match manager.execute_download_with_fallback(task, progress_callback).await {
        Ok(result) => {
            Ok(json!({
                "success": result.success,
                "task_id": result.task_id,
                "downloaded_bytes": result.downloaded_bytes,
                "duration_seconds": result.duration_seconds,
                "average_speed": result.average_speed,
                "error": result.error
            }))
        }
        Err(e) => {
            Err(format!("Enhanced single file download failed: {}", e))
        }
    }
}

#[tauri::command]
pub async fn test_enhanced_direct_download_url(
    url: String,
) -> Result<serde_json::Value, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    match client.head(&url).send().await {
        Ok(response) => {
            Ok(json!({
                "success": true,
                "status_code": response.status().as_u16(),
                "content_length": response.content_length(),
                "headers": {
                    "content_type": response.headers().get("content-type")
                        .and_then(|v| v.to_str().ok()),
                    "last_modified": response.headers().get("last-modified")
                        .and_then(|v| v.to_str().ok()),
                    "accept_ranges": response.headers().get("accept-ranges")
                        .and_then(|v| v.to_str().ok()),
                }
            }))
        }
        Err(e) => {
            Ok(json!({
                "success": false,
                "error": e.to_string()
            }))
        }
    }
}

#[tauri::command]
pub async fn parse_enhanced_netdisk_direct_link(
    share_link: String,
    card: String,
    netdisk: String,
) -> Result<String, String> {
    let parse_url = format!(
        "https://parse.lxown.com/Netdisk_directlink?Card={}&Netdisk={}&ShareLink={}",
        card, netdisk, share_link
    );

    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .map_err(|e| e.to_string())?;

    let response = client.get(&parse_url).send().await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("HTTP error: {}", response.status()));
    }

    let direct_url = response.text().await
        .map_err(|e| format!("Failed to read response: {}", e))?;

    // 验证返回的是否是有效的下载链接
    if direct_url.starts_with("http") {
        Ok(direct_url)
    } else {
        Err(format!("Invalid direct link returned: {}", direct_url))
    }
}

#[tauri::command]
pub async fn get_enhanced_download_config(
    state: State<'_, EnhancedDownloadState>,
) -> Result<serde_json::Value, String> {
    let config = state.config.lock().await;
    
    Ok(json!({
        "gitee_url": config.gitee_url,
        "weibo_url": config.weibo_url,
        "retry_interval_seconds": config.retry_interval_seconds,
        "address_switch_interval_seconds": config.address_switch_interval_seconds,
        "max_retry_attempts": config.max_retry_attempts,
        "download_timeout_seconds": config.download_timeout_seconds,
        "chunk_size": config.chunk_size,
        "max_concurrent_downloads": config.max_concurrent_downloads,
        "enable_resume": config.enable_resume,
        "enable_chunked_download": config.enable_chunked_download,
        "speed_limit": config.speed_limit
    }))
}

#[tauri::command]
pub async fn update_enhanced_download_config(
    state: State<'_, EnhancedDownloadState>,
    new_config: serde_json::Value,
) -> Result<String, String> {
    let mut config = state.config.lock().await;
    
    if let Some(gitee_url) = new_config["gitee_url"].as_str() {
        config.gitee_url = gitee_url.to_string();
    }
    if let Some(weibo_url) = new_config["weibo_url"].as_str() {
        config.weibo_url = weibo_url.to_string();
    }
    if let Some(retry_interval) = new_config["retry_interval_seconds"].as_u64() {
        config.retry_interval_seconds = retry_interval;
    }
    if let Some(switch_interval) = new_config["address_switch_interval_seconds"].as_u64() {
        config.address_switch_interval_seconds = switch_interval;
    }
    if let Some(max_retries) = new_config["max_retry_attempts"].as_u64() {
        config.max_retry_attempts = max_retries as u32;
    }
    if let Some(timeout) = new_config["download_timeout_seconds"].as_u64() {
        config.download_timeout_seconds = timeout;
    }
    if let Some(chunk_size) = new_config["chunk_size"].as_u64() {
        config.chunk_size = chunk_size as usize;
    }
    if let Some(max_concurrent) = new_config["max_concurrent_downloads"].as_u64() {
        config.max_concurrent_downloads = max_concurrent as usize;
    }
    if let Some(enable_resume) = new_config["enable_resume"].as_bool() {
        config.enable_resume = enable_resume;
    }
    if let Some(enable_chunked) = new_config["enable_chunked_download"].as_bool() {
        config.enable_chunked_download = enable_chunked;
    }
    if let Some(speed_limit) = new_config["speed_limit"].as_u64() {
        config.speed_limit = Some(speed_limit);
    }

    // 重新初始化下载管理器
    let manager = EnhancedDownloadManager::new(config.clone());
    let mut manager_guard = state.manager.lock().await;
    *manager_guard = Some(manager);

    log::info!("Enhanced download configuration updated successfully");
    Ok("Enhanced download configuration updated successfully".to_string())
}

#[tauri::command]
pub async fn get_enhanced_download_statistics(
    state: State<'_, EnhancedDownloadState>,
) -> Result<serde_json::Value, String> {
    let manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_ref()
        .ok_or("Enhanced download system not initialized")?;

    // 获取活动任务统计
    let active_tasks_count = manager.active_tasks.len();

    Ok(json!({
        "active_tasks": active_tasks_count,
        "system_initialized": true,
        "timestamp": chrono::Utc::now().timestamp()
    }))
}

#[tauri::command]
pub async fn test_gitee_content_fetch(
    gitee_url: String,
) -> Result<serde_json::Value, String> {
    log::info!("Testing Gitee content fetch from: {}", gitee_url);

    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        .build()
        .map_err(|e| format!("Failed to create client: {}", e))?;

    match client.get(&gitee_url).send().await {
        Ok(response) => {
            let status = response.status();
            let headers = response.headers().clone();

            if !status.is_success() {
                return Ok(json!({
                    "success": false,
                    "error": format!("HTTP error: {}", status),
                    "status_code": status.as_u16()
                }));
            }

            match response.text().await {
                Ok(content) => {
                    log::info!("Received content length: {}", content.len());

                    // 查找 [[ ]] 标志
                    let has_gitee_markers = content.contains("[[") && content.contains("]]");
                    let has_html_markers = content.contains("<!-- UPDATE_INFO_START -->") && content.contains("<!-- UPDATE_INFO_END -->");

                    let mut extracted_content = None;
                    let mut content_type = "none";

                    // 尝试提取 [[ ]] 格式的内容
                    if let Some(start) = content.find("[[") {
                        if let Some(end) = content.find("]]") {
                            if end > start + 2 {
                                extracted_content = Some(content[start + 2..end].trim().to_string());
                                content_type = "gitee_brackets";
                            }
                        }
                    }

                    // 如果没有找到 [[ ]] 格式，尝试HTML注释格式
                    if extracted_content.is_none() {
                        if let Some(start) = content.find("<!-- UPDATE_INFO_START -->") {
                            if let Some(end) = content.find("<!-- UPDATE_INFO_END -->") {
                                extracted_content = Some(content[start + 26..end].trim().to_string());
                                content_type = "html_comments";
                            }
                        }
                    }

                    Ok(json!({
                        "success": true,
                        "status_code": status.as_u16(),
                        "content_length": content.len(),
                        "has_gitee_markers": has_gitee_markers,
                        "has_html_markers": has_html_markers,
                        "content_type": content_type,
                        "extracted_content": extracted_content,
                        "extracted_length": extracted_content.as_ref().map(|c| c.len()).unwrap_or(0),
                        "content_preview": if content.len() > 500 {
                            format!("{}...", &content[..500])
                        } else {
                            content.clone()
                        },
                        "headers": {
                            "content_type": headers.get("content-type")
                                .and_then(|v| v.to_str().ok()),
                            "content_encoding": headers.get("content-encoding")
                                .and_then(|v| v.to_str().ok()),
                        }
                    }))
                }
                Err(e) => {
                    Ok(json!({
                        "success": false,
                        "error": format!("Failed to read response text: {}", e),
                        "status_code": status.as_u16()
                    }))
                }
            }
        }
        Err(e) => {
            Ok(json!({
                "success": false,
                "error": format!("Request failed: {}", e)
            }))
        }
    }
}
