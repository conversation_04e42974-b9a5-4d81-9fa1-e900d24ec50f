{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\", \"tracing\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 17874989004314119331, "profile": 2040997289075261528, "path": 8697551644951991076, "deps": [[1076501750996383263, "once_cell", false, 3317331631347182004], [3353796506826114049, "thiserror", false, 6822031306346486717], [3540822385484940109, "windows_implement", false, 15698303252346516462], [4381063397040571828, "webview2_com", false, 16178044120070079241], [4405182208873388884, "http", false, 10162976284368013428], [4704408070981680310, "serde_json", false, 7911045850117905746], [7314894124883917868, "log", false, 17595646964979077647], [7653476968652377684, "windows", false, 14235264620752131749], [9181830537994570299, "tao", false, 17286237374190320161], [10229761508789367700, "libc", false, 11495349861372431640], [11269248247346606853, "url", false, 15232645414707203371], [14626413149905853098, "tracing", false, 15162072962576219282], [17068110978337472778, "build_script_build", false, 6849972314078836401], [17174345729924723953, "serde", false, 10483066152181423585], [18053436020821374870, "dunce", false, 15069243742515159317]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\wry-79472fefbd4d954d\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}