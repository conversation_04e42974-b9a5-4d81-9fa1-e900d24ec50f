{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 7913717744677543082, "profile": 15657897354478470176, "path": 8291859966711128186, "deps": [[1009387600818341822, "matchers", false, 11688915716789758552], [1017461770342116999, "sharded_slab", false, 2615967847273895791], [1076501750996383263, "once_cell", false, 11895857675154832601], [6831611227313043439, "smallvec", false, 10855031104694760827], [8614575489689151157, "nu_ansi_term", false, 11558192921746921633], [8800316697867969272, "regex", false, 11804285240242442798], [10806489435541507125, "tracing_log", false, 3093755301862033206], [12427285511609802057, "thread_local", false, 17152995622647481104], [12756065109715636409, "tracing_core", false, 8941350482900633565], [14626413149905853098, "tracing", false, 2674625738167854746]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-044711adcf61adea\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}