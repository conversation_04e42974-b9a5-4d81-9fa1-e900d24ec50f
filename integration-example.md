# 全量/增量更新系统集成示例

## 🎯 集成步骤

### 1. 后端集成 (Rust)

#### 1.1 修改 `main.rs` 添加新的命令

```rust
// 在现有的 main.rs 中添加新的 Tauri 命令

#[tauri::command]
async fn get_version_info() -> Result<VersionInfo, String> {
    // 获取当前版本信息
    let local_version = VersionInfo::new(1, 0, 0, 1);
    Ok(local_version)
}

#[tauri::command]
async fn get_server_version_info() -> Result<VersionInfo, String> {
    let client = reqwest::Client::new();
    let url = get_config_value("VERSION_INFO_URL");
    
    let res = client.get(url).send().await.map_err(|e| e.to_string())?;
    let version: VersionInfo = res.json().await.map_err(|e| e.to_string())?;
    
    Ok(version)
}

#[tauri::command]
async fn analyze_files_status(window: tauri::Window) -> Result<FilesStatus, String> {
    let game_path = get_game_path()?;
    let server_hash_file = get_server_hash_file().await?;
    
    // 分析文件状态
    let mut total_files = 0;
    let mut missing_files = 0;
    let mut corrupted_files = 0;
    let mut total_size = 0u64;
    
    // ... 实现文件状态分析逻辑
    
    Ok(FilesStatus {
        total_files,
        missing_files,
        corrupted_files,
        outdated_files: 0,
        total_size,
        missing_size: 0,
        corrupted_size: 0,
    })
}

#[tauri::command]
async fn execute_enhanced_update(
    local_version: VersionInfo,
    server_version: VersionInfo,
    update_method: String,
    window: tauri::Window,
) -> Result<UpdateResult, String> {
    let config = UpdateConfig {
        strategy: match update_method.as_str() {
            "full" => UpdateStrategy::ForceFull,
            "incremental" => UpdateStrategy::ForceIncremental,
            "auto" => UpdateStrategy::Auto,
            _ => UpdateStrategy::Auto,
        },
        auto_update: true,
        bandwidth_limit: None,
        retry_count: 3,
        timeout_seconds: 30,
        verify_integrity: true,
        backup_enabled: true,
        parallel_downloads: 4,
        delta_enabled: true,
    };
    
    let executor = UpdateExecutor::new(config);
    let files_status = analyze_files_status(window.clone()).await?;
    let files_to_update = get_enhanced_files_to_update(window.clone()).await?;
    
    executor.execute_update(
        &local_version,
        &server_version,
        &files_status,
        files_to_update,
        window,
    ).await
}

// 在 main 函数中注册新命令
fn main() {
    tauri::Builder::default()
        .manage(GameState {
            status_receiver: Arc::new(Mutex::new(rx)),
            is_launching: Arc::new(Mutex::new(false)),
        })
        .invoke_handler(
            tauri::generate_handler![
                // 现有命令...
                handle_launch_game,
                get_game_status,
                login,
                get_files_to_update,
                update_file,
                download_all_files,
                
                // 新增命令
                get_version_info,
                get_server_version_info,
                analyze_files_status,
                execute_enhanced_update,
            ]
        )
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

#### 1.2 扩展配置文件

```json
// teralib/src/config/config.json
{
    "LOGIN_ACTION_URL": "http://localhost:8080/tera/LauncherLoginAction",
    "HASH_FILE_URL": "http://localhost:8080/tera/launcher/hash-file.json",
    "FILE_SERVER_URL": "http://localhost:8080/public",
    "SERVER_LIST_URL": "http://localhost:8080/tera/ServerList.json?lang=en&sort=3",
    
    // 新增配置
    "VERSION_INFO_URL": "http://localhost:8080/tera/version.json",
    "DELTA_SERVER_URL": "http://localhost:8080/deltas",
    "UPDATE_CONFIG": {
        "strategy": "Auto",
        "auto_update": true,
        "bandwidth_limit": null,
        "retry_count": 3,
        "timeout_seconds": 30,
        "verify_integrity": true,
        "backup_enabled": true,
        "parallel_downloads": 4,
        "delta_enabled": true
    }
}
```

### 2. 前端集成 (JavaScript)

#### 2.1 修改现有的 `checkForUpdates` 方法

```javascript
// 在 App 对象中修改现有方法
async checkForUpdates() {
    if (!UPDATE_CHECK_ENABLED) {
        // 现有逻辑保持不变
        return;
    }

    if (this.state.isCheckingForUpdates) {
        console.log("Update check already in progress");
        return;
    }

    this.setState({
        isCheckingForUpdates: true,
        currentUpdateMode: "file_check",
    });

    try {
        // 获取版本信息
        const localVersion = await invoke("get_version_info");
        const serverVersion = await invoke("get_server_version_info");
        
        // 分析文件状态
        const filesStatus = await invoke("analyze_files_status");
        
        // 获取需要更新的文件
        const filesToUpdate = await invoke("get_files_to_update");
        
        if (filesToUpdate.length === 0) {
            // 无需更新的逻辑保持不变
            this.setState({
                isUpdateAvailable: false,
                isFileCheckComplete: true,
                currentUpdateMode: "complete",
            });
            this.updateLaunchGameButton(false);
            this.toggleLanguageSelector(true);
            return;
        }

        // 显示更新模式选择
        const updateInfo = {
            localVersion,
            serverVersion,
            filesStatus,
            filesToUpdate,
            totalGameSize: this.calculateTotalGameSize(),
            estimatedSpeeds: {
                incremental: 1024 * 1024, // 1MB/s
                delta: 2 * 1024 * 1024,   // 2MB/s
                full: 512 * 1024          // 512KB/s
            }
        };

        const selectedMode = await this.showUpdateModeSelection(updateInfo);
        
        if (selectedMode === 'cancel') {
            this.setState({ isCheckingForUpdates: false });
            return;
        }

        // 执行选定的更新模式
        await this.executeSelectedUpdate(selectedMode, localVersion, serverVersion);

    } catch (error) {
        console.error("Error during update check:", error);
        this.showErrorMessage(error.message || "Update check failed");
    } finally {
        this.setState({ isCheckingForUpdates: false });
    }
},

async executeSelectedUpdate(updateMode, localVersion, serverVersion) {
    this.setState({
        isUpdateAvailable: true,
        isFileCheckComplete: true,
        currentUpdateMode: "download",
    });

    // 初始化增强的进度显示
    if (this.enhancedProgressDisplay) {
        this.enhancedProgressDisplay.initializeProgress(updateMode, 0, 0);
    }

    try {
        const result = await invoke("execute_enhanced_update", {
            localVersion,
            serverVersion,
            updateMethod: updateMode
        });

        console.log("Update completed:", result);
        
        this.setState({
            isUpdateComplete: true,
            currentUpdateMode: "complete",
        });

        // 重新启用游戏按钮
        this.updateLaunchGameButton(false);
        this.toggleLanguageSelector(true);

    } catch (error) {
        console.error("Update failed:", error);
        this.showErrorMessage(error.message || "Update failed");
    }
}
```

#### 2.2 添加新的事件监听器

```javascript
// 在 setupUpdateListeners 方法中添加新的监听器
setupUpdateListeners() {
    // 现有监听器保持不变
    listen("download_progress", this.handleDownloadProgress.bind(this));
    listen("file_check_progress", this.handleFileCheckProgress.bind(this));
    listen("file_check_completed", this.handleFileCheckCompleted.bind(this));
    listen("download_complete", () => {
        this.setState({
            isDownloadComplete: true,
            currentProgress: 100,
            currentUpdateMode: "complete",
        });
    });

    // 新增监听器
    listen("update_started", this.handleUpdateStarted.bind(this));
    listen("delta_progress", this.handleDeltaProgress.bind(this));
    listen("verification_progress", this.handleVerificationProgress.bind(this));
    listen("backup_progress", this.handleBackupProgress.bind(this));
},

handleUpdateStarted(event) {
    const { method, total_files, total_size } = event.payload;
    
    if (this.enhancedProgressDisplay) {
        this.enhancedProgressDisplay.initializeProgress(method, total_files, total_size);
    }
    
    this.setState({
        currentUpdateMethod: method,
        totalFiles: total_files,
        totalSize: total_size,
        currentUpdateMode: "download"
    });
},

handleDeltaProgress(event) {
    const { patches_applied, compression_ratio, patching_speed } = event.payload;
    
    // 更新增量更新特有的进度信息
    this.setState({
        patchesApplied: patches_applied,
        compressionRatio: compression_ratio,
        patchingSpeed: patching_speed
    });
}
```

### 3. 服务器端增强

#### 3.1 添加版本信息 API

```javascript
// 在 test-server/server.js 中添加新的端点

// 版本信息 API
app.get('/tera/version.json', (req, res) => {
    const versionInfo = {
        major: 1,
        minor: 1,
        patch: 0,
        build: 123,
        release_date: new Date().toISOString(),
        update_type: "Minor",
        force_full_update: false,
        changelog: [
            "Added new character class",
            "Fixed critical bugs",
            "Performance improvements"
        ]
    };
    
    res.json(versionInfo);
});

// 增量文件 API
app.get('/deltas/:filename', (req, res) => {
    const filename = req.params.filename;
    const deltaPath = path.join(__dirname, 'deltas', filename);
    
    if (fs.existsSync(deltaPath)) {
        res.sendFile(deltaPath);
    } else {
        res.status(404).json({ error: 'Delta file not found' });
    }
});
```

#### 3.2 增强哈希文件格式

```javascript
// 修改哈希文件生成，添加增量信息
const enhancedHashFile = {
    version: "*********",
    generated_at: new Date().toISOString(),
    files: [
        {
            path: "S1Game/Config/S1Engine.ini",
            hash: "abc123...",
            size: 1024,
            url: "http://localhost:8080/public/S1Game/Config/S1Engine.ini",
            category: {
                name: "config",
                priority: 1,
                can_skip: false,
                requires_restart: true
            },
            version_added: {
                major: 1,
                minor: 0,
                patch: 0,
                build: 1
            },
            delta_info: {
                base_hash: "def456...",
                delta_url: "http://localhost:8080/deltas/S1Engine_delta.bin",
                delta_size: 256,
                patch_algorithm: "bsdiff"
            }
        }
    ]
};
```

### 4. CSS 样式

```css
/* 更新模式选择对话框样式 */
.update-mode-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.update-mode-dialog {
    background: #2a2a2a;
    border-radius: 12px;
    padding: 24px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.update-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin: 24px 0;
}

.update-option {
    border: 2px solid #444;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.update-option:hover {
    border-color: #666;
    background: #333;
}

.update-option.selected {
    border-color: #4CAF50;
    background: #1a3a1a;
}

.update-option.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.recommended-badge {
    background: #4CAF50;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.option-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin: 12px 0;
}

.stat {
    text-align: center;
    padding: 8px;
    background: #1a1a1a;
    border-radius: 4px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #888;
    margin-bottom: 4px;
}

.stat-value {
    display: block;
    font-weight: bold;
    color: #fff;
}
```

## 🎯 测试步骤

1. **集成代码**: 将上述代码集成到现有项目中
2. **更新服务器**: 添加新的 API 端点
3. **测试版本检查**: 验证版本信息获取
4. **测试模式选择**: 验证用户界面显示
5. **测试更新流程**: 验证不同更新模式的执行

## 📊 预期效果

- ✅ 用户可以选择更新模式
- ✅ 智能推荐最优更新策略
- ✅ 详细的进度信息显示
- ✅ 更好的用户体验
- ✅ 更高的更新效率
