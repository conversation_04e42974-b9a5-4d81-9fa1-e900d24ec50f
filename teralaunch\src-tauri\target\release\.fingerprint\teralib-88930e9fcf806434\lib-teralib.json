{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 9968487133993622824, "profile": 2040997289075261528, "path": 3107050944943690935, "deps": [[1076501750996383263, "once_cell", false, 3317331631347182004], [1314680128599551313, "reqwest", false, 6364113982253313135], [1760623714118191065, "dotenv", false, 731302312252141346], [2062481783838671931, "parking_lot", false, 3843452040475919091], [4704408070981680310, "serde_json", false, 7911045850117905746], [7016560594308609179, "prost", false, 14212425162297996973], [7141661585461469887, "env_logger", false, 13389437967666249915], [7314894124883917868, "log", false, 17595646964979077647], [8858041990736880586, "tokio", false, 15737707881525506531], [10020888071089587331, "<PERSON>ap<PERSON>", false, 13475388162886347372], [10625088794814739235, "build_script_build", false, 6849782327902839694], [15349051716154188138, "tokio_macros", false, 13821334543920509347], [16601927120328658422, "protobuf", false, 2024255905414851363], [17917672826516349275, "lazy_static", false, 1284834228920270451], [18156568664791550917, "prost_types", false, 11278626669246094140]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\teralib-88930e9fcf806434\\dep-lib-teralib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}