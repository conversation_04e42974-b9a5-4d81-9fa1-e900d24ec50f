// Agent3: 单元测试套件
// 为最优下载系统提供全面的单元测试

#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    use mockito::{mock, Matcher};
    use tempfile::TempDir;
    use std::fs::File;
    use std::io::Write;

    // ============================================================================
    // 1. WebContentFetcher 测试
    // ============================================================================

    #[tokio::test]
    async fn test_fetch_encrypted_update_info_success() {
        // 模拟 Gitee 响应
        let _m = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_header("content-type", "text/html")
            .with_body(r#"
                <html>
                    <!-- UPDATE_INFO_START -->
                    eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cDovL2V4YW1wbGUuY29tL2ZpbGUxLnppcCJdLCJpbmNyZW1lbnRhbF9zZXJ2ZXJfdXJsIjoiaHR0cDovL2V4YW1wbGUuY29tL2luY3JlbWVudGFsIiwiZmlsZV9saXN0X3VybCI6Imh0dHA6Ly9leGFtcGxlLmNvbS9maWxlcyIsImNoZWNrc3VtIjoiYWJjMTIzIn0=
                    <!-- UPDATE_INFO_END -->
                </html>
            "#)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let fetcher = WebContentFetcher::new(config);
        let result = fetcher.fetch_encrypted_update_info().await;

        assert!(result.is_ok());
        let encrypted_info = result.unwrap();
        assert!(!encrypted_info.encrypted_data.is_empty());
        assert_eq!(encrypted_info.version, "1.0.0");
    }

    #[tokio::test]
    async fn test_fetch_encrypted_update_info_gitee_fail_weibo_success() {
        // Gitee 失败
        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(404)
            .create();

        // 微博成功
        let _m2 = mock("GET", "/weibo/test")
            .with_status(200)
            .with_body(r#"
                <!-- UPDATE_INFO_START -->
                eyJ2ZXJzaW9uIjoiMS4wLjEifQ==
                <!-- UPDATE_INFO_END -->
            "#)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            weibo_url: format!("{}/weibo/test", &mockito::server_url()),
            ..Default::default()
        };

        let fetcher = WebContentFetcher::new(config);
        let result = fetcher.fetch_encrypted_update_info().await;

        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_decrypt_update_info_success() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        // 创建测试用的加密信息（Base64 编码的 JSON）
        let test_data = r#"{"version":"1.0.0","full_download_urls":["http://example.com/file1.zip"],"incremental_server_url":"http://example.com/incremental","file_list_url":"http://example.com/files","checksum":"abc123"}"#;
        let encrypted_data = base64::encode(test_data);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data,
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        assert!(result.is_ok());

        let decrypted = result.unwrap();
        assert_eq!(decrypted.version, "1.0.0");
        assert_eq!(decrypted.full_download_urls.len(), 1);
        assert_eq!(decrypted.checksum, "abc123");
    }

    #[tokio::test]
    async fn test_decrypt_update_info_invalid_data() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data: "invalid_base64_data".to_string(),
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        assert!(result.is_err());
    }

    // ============================================================================
    // 2. SmartDownloadManager 测试
    // ============================================================================

    #[tokio::test]
    async fn test_download_from_url_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let test_content = "Hello, World!";
        let _m = mock("GET", "/test_file.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let url = format!("{}/test_file.txt", &mockito::server_url());
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        assert!(result.is_ok());

        // 验证文件内容
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
    }

    #[tokio::test]
    async fn test_download_from_url_http_error() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let _m = mock("GET", "/test_file.txt")
            .with_status(404)
            .create();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let url = format!("{}/test_file.txt", &mockito::server_url());
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("HTTP error: 404"));
    }

    #[tokio::test]
    async fn test_calculate_file_checksum() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test_checksum.txt");

        // 创建测试文件
        let test_content = "Hello, World!";
        let mut file = File::create(&file_path).unwrap();
        file.write_all(test_content.as_bytes()).unwrap();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let result = manager.calculate_file_checksum(&file_path).await;
        assert!(result.is_ok());

        let checksum = result.unwrap();
        // 验证 SHA256 哈希值
        assert_eq!(checksum.len(), 64); // SHA256 哈希长度
        assert!(checksum.chars().all(|c| c.is_ascii_hexdigit()));
    }

    #[tokio::test]
    async fn test_download_with_fallback_success_first_url() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let test_content = "Hello, World!";
        let expected_checksum = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"; // SHA256 of "Hello, World!"

        // 第一个 URL 成功
        let _m1 = mock("GET", "/file1.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "test_task".to_string(),
            update_type: UpdateType::Full,
            urls: vec![format!("{}/file1.txt", &mockito::server_url())],
            output_path,
            expected_checksum: Some(expected_checksum.to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_download_with_fallback_first_fail_second_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let test_content = "Hello, World!";
        let expected_checksum = "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f";

        // 第一个 URL 失败
        let _m1 = mock("GET", "/file1.txt")
            .with_status(404)
            .create();

        // 第二个 URL 成功
        let _m2 = mock("GET", "/file2.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig {
            address_switch_interval_seconds: 1, // 减少测试时间
            ..Default::default()
        };
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "test_task".to_string(),
            update_type: UpdateType::Full,
            urls: vec![
                format!("{}/file1.txt", &mockito::server_url()),
                format!("{}/file2.txt", &mockito::server_url()),
            ],
            output_path,
            expected_checksum: Some(expected_checksum.to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_download_with_checksum_mismatch() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let test_content = "Hello, World!";
        let wrong_checksum = "wrong_checksum";

        let _m = mock("GET", "/file1.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "test_task".to_string(),
            update_type: UpdateType::Full,
            urls: vec![format!("{}/file1.txt", &mockito::server_url())],
            output_path,
            expected_checksum: Some(wrong_checksum.to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        
        // 应该失败，因为校验和不匹配
        assert!(result.is_err());
    }

    // ============================================================================
    // 3. 配置测试
    // ============================================================================

    #[test]
    fn test_update_config_default() {
        let config = UpdateConfig::default();
        
        assert_eq!(config.gitee_url, "https://gitee.com/mango_mu/test");
        assert_eq!(config.retry_interval_seconds, 30);
        assert_eq!(config.address_switch_interval_seconds, 30);
        assert_eq!(config.max_retry_attempts, 5);
        assert_eq!(config.download_timeout_seconds, 300);
        assert_eq!(config.chunk_size, 1024 * 1024);
    }

    // ============================================================================
    // 4. 数据结构测试
    // ============================================================================

    #[test]
    fn test_download_task_creation() {
        let task = DownloadTask {
            task_id: "test_task".to_string(),
            update_type: UpdateType::Full,
            urls: vec!["http://example.com/file.zip".to_string()],
            output_path: PathBuf::from("/tmp/file.zip"),
            expected_checksum: Some("abc123".to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        assert_eq!(task.task_id, "test_task");
        assert!(matches!(task.update_type, UpdateType::Full));
        assert_eq!(task.urls.len(), 1);
        assert_eq!(task.current_url_index, 0);
        assert_eq!(task.retry_count, 0);
    }

    #[test]
    fn test_download_progress_creation() {
        let progress = DownloadProgress {
            downloaded_bytes: 1024,
            total_bytes: 2048,
            speed_bytes_per_sec: 512,
            eta_seconds: Some(2),
            current_url: "http://example.com/file.zip".to_string(),
        };

        assert_eq!(progress.downloaded_bytes, 1024);
        assert_eq!(progress.total_bytes, 2048);
        assert_eq!(progress.speed_bytes_per_sec, 512);
        assert_eq!(progress.eta_seconds, Some(2));
    }

    // ============================================================================
    // 5. 错误处理测试
    // ============================================================================

    #[test]
    fn test_download_error_display() {
        let error = DownloadError::Network("Connection timeout".to_string());
        assert_eq!(format!("{}", error), "Network error: Connection timeout");

        let error = DownloadError::FileSystem("Permission denied".to_string());
        assert_eq!(format!("{}", error), "File system error: Permission denied");

        let error = DownloadError::Validation("Checksum mismatch".to_string());
        assert_eq!(format!("{}", error), "Validation error: Checksum mismatch");
    }

    // ============================================================================
    // 6. 集成测试
    // ============================================================================

    #[tokio::test]
    async fn test_full_download_workflow() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("client.zip");

        // 模拟完整的下载流程
        let test_content = "Mock ZIP content";
        let expected_checksum = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"; // SHA256 of test_content

        // 模拟 Gitee 响应
        let update_info = format!(r#"{{"version":"1.0.0","full_download_urls":["{}"],"incremental_server_url":"http://example.com","file_list_url":"http://example.com/files","checksum":"{}"}}"#, 
            format!("{}/client.zip", &mockito::server_url()), expected_checksum);
        let encrypted_data = base64::encode(&update_info);

        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 模拟文件下载
        let _m2 = mock("GET", "/client.zip")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_full_client(output_path.clone(), progress_callback).await;
        assert!(result.is_ok());

        // 验证文件存在且内容正确
        assert!(output_path.exists());
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
    }

    // ============================================================================
    // 7. 性能测试
    // ============================================================================

    #[tokio::test]
    async fn test_concurrent_downloads_performance() {
        use std::time::Instant;

        let temp_dir = TempDir::new().unwrap();
        let test_content = "x".repeat(1024); // 1KB content

        // 创建多个模拟端点
        let mut mocks = Vec::new();
        for i in 0..5 {
            let mock = mock("GET", &format!("/file{}.txt", i))
                .with_status(200)
                .with_header("content-length", &test_content.len().to_string())
                .with_body(&test_content)
                .create();
            mocks.push(mock);
        }

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let start_time = Instant::now();
        
        // 并发下载多个文件
        let mut tasks = Vec::new();
        for i in 0..5 {
            let output_path = temp_dir.path().join(format!("file{}.txt", i));
            let url = format!("{}/file{}.txt", &mockito::server_url(), i);
            let progress_callback = |_progress: DownloadProgress| {};
            
            let task = manager.download_from_url(&url, &output_path, &progress_callback);
            tasks.push(task);
        }

        // 等待所有下载完成
        let results = futures::future::join_all(tasks).await;
        let elapsed = start_time.elapsed();

        // 验证所有下载都成功
        for result in results {
            assert!(result.is_ok());
        }

        // 性能断言：5个1KB文件应该在合理时间内完成
        assert!(elapsed.as_secs() < 10, "Downloads took too long: {:?}", elapsed);
    }
}
