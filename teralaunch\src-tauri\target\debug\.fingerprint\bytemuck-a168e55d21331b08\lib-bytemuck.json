{"rustc": 1842507548689473721, "features": "[\"extern_crate_alloc\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"min_const_generics\", \"must_cast\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\"]", "target": 5195934831136530909, "profile": 17003946029344894063, "path": 7660906076931890539, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bytemuck-a168e55d21331b08\\dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}