@echo off
echo.
echo ========================================
echo    Tera Rust Launcher 测试脚本
echo ========================================
echo.

set SERVER_URL=http://localhost:8080
set TESTS_PASSED=0
set TESTS_TOTAL=0

echo 🔍 测试服务器连接...
curl -s %SERVER_URL%/health > nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 服务器健康检查通过
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 服务器健康检查失败
)
set /a TESTS_TOTAL+=1

echo.
echo 🔍 测试哈希文件 API...
curl -s %SERVER_URL%/tera/launcher/hash-file.json > nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 哈希文件 API 通过
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 哈希文件 API 失败
)
set /a TESTS_TOTAL+=1

echo.
echo 🔍 测试登录 API...
curl -s -X POST -d "login=testuser&password=testpass" -H "Content-Type: application/x-www-form-urlencoded" %SERVER_URL%/tera/LauncherLoginAction > nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 登录 API 通过
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 登录 API 失败
)
set /a TESTS_TOTAL+=1

echo.
echo 🔍 测试启动器文件...
if exist "teralaunch\src-tauri\target\release\teralaunch.exe" (
    echo    ✅ 启动器可执行文件存在
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 启动器可执行文件不存在
)
set /a TESTS_TOTAL+=1

echo.
echo 🔍 测试配置文件...
if exist "teralib\src\config\config.json" (
    echo    ✅ 配置文件存在
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 配置文件不存在
)
set /a TESTS_TOTAL+=1

echo.
echo 🔍 测试游戏目录...
if exist "C:\tera" (
    echo    ✅ 游戏目录存在
    set /a TESTS_PASSED+=1
) else (
    echo    ❌ 游戏目录不存在
)
set /a TESTS_TOTAL+=1

echo.
echo ========================================
echo           测试结果汇总
echo ========================================
echo 通过: %TESTS_PASSED% / %TESTS_TOTAL%

if %TESTS_PASSED% equ %TESTS_TOTAL% (
    echo.
    echo 🎉 所有测试通过！
    echo.
    echo 📱 启动 Tera Launcher:
    echo    .\teralaunch\src-tauri\target\release\teralaunch.exe
    echo.
    echo 🌐 测试服务器运行在: %SERVER_URL%
    echo 👤 测试用户: testuser / testpass
    echo.
) else (
    echo.
    echo ⚠️  部分测试失败，请检查配置。
)

pause
