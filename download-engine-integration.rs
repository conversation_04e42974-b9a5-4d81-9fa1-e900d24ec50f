// 下载引擎集成到现有 Tauri 应用的示例
// 展示如何在现有的 main.rs 中集成新的下载系统

use crate::enhanced_download_implementation::*;
use std::sync::Arc;
use tokio::sync::Mutex;
use tauri::Window;

// ============================================================================
// 1. 下载管理器 - 统一管理不同的下载引擎
// ============================================================================

pub struct DownloadManager {
    current_engine: Arc<Mutex<Box<dyn DownloadEngine>>>,
    config: DownloadConfig,
}

impl DownloadManager {
    pub async fn new(config: DownloadConfig) -> Result<Self, String> {
        let engine: Box<dyn DownloadEngine> = match config.engine {
            DownloadEngine::Reqwest => {
                Box::new(EnhancedReqwestEngine::new(config.clone())
                    .map_err(|e| e.to_string())?)
            },
            DownloadEngine::Aria2 => {
                Box::new(Aria2Engine::new(config.clone()).await
                    .map_err(|e| e.to_string())?)
            },
            DownloadEngine::Curl => {
                // TODO: 实现 libcurl 引擎
                return Err("Curl engine not implemented yet".to_string());
            },
        };

        Ok(Self {
            current_engine: Arc::new(Mutex::new(engine)),
            config,
        })
    }

    pub async fn switch_engine(&self, new_engine_type: DownloadEngine) -> Result<(), String> {
        let mut config = self.config.clone();
        config.engine = new_engine_type;

        let new_engine: Box<dyn DownloadEngine> = match config.engine {
            DownloadEngine::Reqwest => {
                Box::new(EnhancedReqwestEngine::new(config)
                    .map_err(|e| e.to_string())?)
            },
            DownloadEngine::Aria2 => {
                Box::new(Aria2Engine::new(config).await
                    .map_err(|e| e.to_string())?)
            },
            DownloadEngine::Curl => {
                return Err("Curl engine not implemented yet".to_string());
            },
        };

        let mut current_engine = self.current_engine.lock().await;
        *current_engine = new_engine;

        Ok(())
    }

    pub async fn download_file(
        &self,
        url: &str,
        output_path: &std::path::Path,
        window: Window,
    ) -> Result<DownloadResult, String> {
        let engine = self.current_engine.lock().await;
        
        let progress_callback = Box::new(move |progress: DownloadProgress| {
            let payload = serde_json::json!({
                "task_id": progress.task_id,
                "downloaded_bytes": progress.downloaded_bytes,
                "total_bytes": progress.total_bytes,
                "speed_bytes_per_sec": progress.speed_bytes_per_sec,
                "eta_seconds": progress.eta_seconds,
                "status": format!("{:?}", progress.status),
            });

            if let Err(e) = window.emit("enhanced_download_progress", payload) {
                eprintln!("Failed to emit progress event: {}", e);
            }
        });

        engine.download_file(url, output_path, progress_callback).await
            .map_err(|e| e.to_string())
    }

    pub async fn download_multiple(
        &self,
        downloads: Vec<DownloadTask>,
        window: Window,
    ) -> Result<Vec<DownloadResult>, String> {
        let engine = self.current_engine.lock().await;
        
        let global_progress_callback = Box::new(move |progress: GlobalProgress| {
            let payload = serde_json::json!({
                "completed_tasks": progress.completed_tasks,
                "total_tasks": progress.total_tasks,
                "total_downloaded_bytes": progress.total_downloaded_bytes,
                "total_bytes": progress.total_bytes,
                "overall_speed": progress.overall_speed,
                "eta_seconds": progress.eta_seconds,
            });

            if let Err(e) = window.emit("enhanced_global_progress", payload) {
                eprintln!("Failed to emit global progress event: {}", e);
            }
        });

        engine.download_multiple(downloads, global_progress_callback).await
            .map_err(|e| e.to_string())
    }

    pub async fn get_engine_info(&self) -> EngineInfo {
        let engine = self.current_engine.lock().await;
        engine.get_engine_info()
    }
}

// ============================================================================
// 2. 全局下载管理器实例
// ============================================================================

use lazy_static::lazy_static;

lazy_static! {
    static ref DOWNLOAD_MANAGER: Arc<Mutex<Option<DownloadManager>>> = Arc::new(Mutex::new(None));
}

async fn get_download_manager() -> Result<Arc<DownloadManager>, String> {
    let manager_guard = DOWNLOAD_MANAGER.lock().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        // 这里需要克隆 Arc，但 DownloadManager 没有实现 Clone
        // 实际实现中需要重新设计这部分
        return Err("Manager access pattern needs redesign".to_string());
    }

    drop(manager_guard);

    // 初始化默认管理器
    let config = DownloadConfig::default();
    let manager = DownloadManager::new(config).await?;
    
    let mut manager_guard = DOWNLOAD_MANAGER.lock().await;
    *manager_guard = Some(manager);
    
    Err("Manager access pattern needs redesign".to_string())
}

// ============================================================================
// 3. Tauri 命令实现
// ============================================================================

#[tauri::command]
async fn configure_download_engine(
    engine_type: String,
    max_concurrent: Option<usize>,
    max_connections_per_file: Option<usize>,
    enable_resume: Option<bool>,
    enable_chunked: Option<bool>,
    speed_limit: Option<u64>,
) -> Result<String, String> {
    let engine = match engine_type.as_str() {
        "reqwest" => DownloadEngine::Reqwest,
        "aria2" => DownloadEngine::Aria2,
        "curl" => DownloadEngine::Curl,
        _ => return Err("Invalid engine type".to_string()),
    };

    let mut config = DownloadConfig::default();
    config.engine = engine;
    
    if let Some(max_concurrent) = max_concurrent {
        config.max_concurrent = max_concurrent;
    }
    if let Some(max_connections) = max_connections_per_file {
        config.max_connections_per_file = max_connections;
    }
    if let Some(resume) = enable_resume {
        config.enable_resume = resume;
    }
    if let Some(chunked) = enable_chunked {
        config.enable_chunked_download = chunked;
    }
    if let Some(limit) = speed_limit {
        config.speed_limit = Some(limit);
    }

    // 重新初始化下载管理器
    let manager = DownloadManager::new(config).await?;
    let mut manager_guard = DOWNLOAD_MANAGER.lock().await;
    *manager_guard = Some(manager);

    Ok("Download engine configured successfully".to_string())
}

#[tauri::command]
async fn get_download_engine_info() -> Result<serde_json::Value, String> {
    // 这里需要重新设计管理器访问模式
    let config = DownloadConfig::default();
    let manager = DownloadManager::new(config).await?;
    let info = manager.get_engine_info().await;

    Ok(serde_json::json!({
        "name": info.name,
        "version": info.version,
        "features": info.features,
        "max_concurrent_supported": info.max_concurrent_supported,
    }))
}

#[tauri::command]
async fn enhanced_download_file(
    url: String,
    output_path: String,
    window: Window,
) -> Result<serde_json::Value, String> {
    let config = DownloadConfig::default();
    let manager = DownloadManager::new(config).await?;
    
    let path = std::path::Path::new(&output_path);
    let result = manager.download_file(&url, path, window).await?;

    Ok(serde_json::json!({
        "task_id": result.task_id,
        "success": result.success,
        "downloaded_bytes": result.downloaded_bytes,
        "duration_seconds": result.duration_seconds,
        "average_speed": result.average_speed,
        "error": result.error,
    }))
}

#[tauri::command]
async fn enhanced_download_multiple(
    downloads: Vec<serde_json::Value>,
    window: Window,
) -> Result<Vec<serde_json::Value>, String> {
    let config = DownloadConfig::default();
    let manager = DownloadManager::new(config).await?;

    let mut download_tasks = Vec::new();
    
    for download_json in downloads {
        let task = DownloadTask {
            id: download_json["id"].as_str().unwrap_or("").to_string(),
            url: download_json["url"].as_str().unwrap_or("").to_string(),
            output_path: std::path::PathBuf::from(
                download_json["output_path"].as_str().unwrap_or("")
            ),
            expected_hash: download_json["expected_hash"].as_str().map(|s| s.to_string()),
            expected_size: download_json["expected_size"].as_u64(),
            priority: download_json["priority"].as_u64().unwrap_or(0) as u32,
        };
        download_tasks.push(task);
    }

    let results = manager.download_multiple(download_tasks, window).await?;

    let json_results: Vec<serde_json::Value> = results.into_iter().map(|result| {
        serde_json::json!({
            "task_id": result.task_id,
            "success": result.success,
            "downloaded_bytes": result.downloaded_bytes,
            "duration_seconds": result.duration_seconds,
            "average_speed": result.average_speed,
            "error": result.error,
        })
    }).collect();

    Ok(json_results)
}

// ============================================================================
// 4. 集成到现有的 update_file 函数
// ============================================================================

// 这个函数展示如何替换现有的 update_file 函数
#[tauri::command]
async fn enhanced_update_file(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    file_info: crate::FileInfo, // 使用现有的 FileInfo 结构
    total_files: usize,
    current_file_index: usize,
    total_size: u64,
    downloaded_size: u64,
) -> Result<u64, String> {
    // 创建下载任务
    let download_task = DownloadTask {
        id: format!("file_{}", current_file_index),
        url: file_info.url.clone(),
        output_path: {
            let game_path = crate::get_game_path()?;
            game_path.join(&file_info.path)
        },
        expected_hash: Some(file_info.hash.clone()),
        expected_size: Some(file_info.size),
        priority: 0,
    };

    // 使用增强的下载引擎
    let config = DownloadConfig {
        engine: DownloadEngine::Reqwest, // 可以根据配置选择
        enable_resume: true,
        enable_chunked_download: true,
        max_connections_per_file: 4,
        ..Default::default()
    };

    let manager = DownloadManager::new(config).await?;
    
    // 设置进度回调，兼容现有的进度事件格式
    let progress_callback = Box::new(move |progress: DownloadProgress| {
        let payload = crate::ProgressPayload {
            file_name: file_info.path.clone(),
            progress: (progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0,
            speed: progress.speed_bytes_per_sec as f64,
            downloaded_bytes: downloaded_size + progress.downloaded_bytes,
            total_bytes: total_size,
            total_files,
            elapsed_time: 0.0, // TODO: 计算实际时间
            current_file_index,
        };

        if let Err(e) = window.emit("download_progress", &payload) {
            eprintln!("Failed to emit download_progress event: {}", e);
        }
    });

    let result = manager.current_engine.lock().await
        .download_file(&download_task.url, &download_task.output_path, progress_callback).await
        .map_err(|e| e.to_string())?;

    if !result.success {
        return Err(result.error.unwrap_or("Download failed".to_string()));
    }

    Ok(result.downloaded_bytes)
}

// ============================================================================
// 5. 在 main 函数中注册新命令
// ============================================================================

/*
在现有的 main.rs 中，需要在 invoke_handler 中添加新的命令：

.invoke_handler(
    tauri::generate_handler![
        // 现有命令...
        handle_launch_game,
        get_game_status,
        login,
        get_files_to_update,
        update_file,
        download_all_files,
        
        // 新增的增强下载命令
        configure_download_engine,
        get_download_engine_info,
        enhanced_download_file,
        enhanced_download_multiple,
        enhanced_update_file,
    ]
)
*/
