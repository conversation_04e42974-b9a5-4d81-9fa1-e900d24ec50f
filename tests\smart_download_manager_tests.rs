// SmartDownloadManager 模块单元测试
// 测试智能下载管理功能

#[cfg(test)]
mod smart_download_manager_tests {
    use super::*;
    use crate::optimal_download_system::*;
    use mockito::{mock, Matcher};
    use tempfile::TempDir;
    use std::fs::File;
    use std::io::Write;
    use std::time::Instant;

    // ============================================================================
    // 1. SmartDownloadManager 构造函数测试
    // ============================================================================

    #[test]
    fn test_smart_download_manager_new() {
        let config = UpdateConfig {
            download_timeout_seconds: 60,
            chunk_size: 2048,
            max_retry_attempts: 3,
            ..Default::default()
        };

        let manager = SmartDownloadManager::new(config.clone());
        
        // 验证配置正确设置
        assert_eq!(manager.config.download_timeout_seconds, 60);
        assert_eq!(manager.config.chunk_size, 2048);
        assert_eq!(manager.config.max_retry_attempts, 3);
        assert!(manager.active_tasks.is_empty());
    }

    // ============================================================================
    // 2. 单个文件下载测试
    // ============================================================================

    #[tokio::test]
    async fn test_download_from_url_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("test_file.txt");

        let test_content = "Hello, World! This is a test file.";
        let _m = mock("GET", "/test_file.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_header("content-type", "text/plain")
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let url = format!("{}/test_file.txt", &mockito::server_url());
        let mut progress_updates = Vec::new();
        let progress_callback = |progress: DownloadProgress| {
            progress_updates.push(progress);
        };

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        
        assert!(result.is_ok());
        assert!(output_path.exists());
        
        // 验证文件内容
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
    }

    #[tokio::test]
    async fn test_download_from_url_large_file() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("large_file.bin");

        // 创建1MB的测试数据
        let test_content = "x".repeat(1024 * 1024);
        let _m = mock("GET", "/large_file.bin")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(&test_content)
            .create();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let url = format!("{}/large_file.bin", &mockito::server_url());
        let mut progress_count = 0;
        let progress_callback = |progress: DownloadProgress| {
            progress_count += 1;
            assert!(progress.downloaded_bytes <= progress.total_bytes);
            assert!(progress.speed_bytes_per_sec >= 0);
        };

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        
        assert!(result.is_ok());
        assert!(output_path.exists());
        
        // 验证文件大小
        let metadata = std::fs::metadata(&output_path).unwrap();
        assert_eq!(metadata.len(), test_content.len() as u64);
    }

    #[tokio::test]
    async fn test_download_from_url_http_error() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("not_found.txt");

        let _m = mock("GET", "/not_found.txt")
            .with_status(404)
            .with_body("Not Found")
            .create();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let url = format!("{}/not_found.txt", &mockito::server_url());
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        
        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(error.contains("HTTP error: 404"));
        assert!(!output_path.exists());
    }

    #[tokio::test]
    async fn test_download_from_url_network_error() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("network_error.txt");

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        // 使用无效的URL
        let url = "http://invalid-domain-xyz123.com/file.txt";
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_from_url(&url, &output_path, &progress_callback).await;
        
        assert!(result.is_err());
        assert!(!output_path.exists());
    }

    // ============================================================================
    // 3. 文件校验和测试
    // ============================================================================

    #[tokio::test]
    async fn test_calculate_file_checksum_success() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("checksum_test.txt");

        let test_content = "Hello, World!";
        let mut file = File::create(&file_path).unwrap();
        file.write_all(test_content.as_bytes()).unwrap();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let result = manager.calculate_file_checksum(&file_path).await;
        
        assert!(result.is_ok());
        let checksum = result.unwrap();
        
        // 验证SHA256哈希格式
        assert_eq!(checksum.len(), 64);
        assert!(checksum.chars().all(|c| c.is_ascii_hexdigit()));
        
        // 验证具体的哈希值（"Hello, World!" 的 SHA256）
        assert_eq!(checksum, "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f");
    }

    #[tokio::test]
    async fn test_calculate_file_checksum_empty_file() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("empty_file.txt");

        // 创建空文件
        File::create(&file_path).unwrap();

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let result = manager.calculate_file_checksum(&file_path).await;
        
        assert!(result.is_ok());
        let checksum = result.unwrap();
        
        // 空文件的SHA256哈希
        assert_eq!(checksum, "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855");
    }

    #[tokio::test]
    async fn test_calculate_file_checksum_nonexistent_file() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("nonexistent.txt");

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let result = manager.calculate_file_checksum(&file_path).await;
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Failed to open file"));
    }

    // ============================================================================
    // 4. 多源下载故障切换测试
    // ============================================================================

    #[tokio::test]
    async fn test_execute_download_with_fallback_first_url_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("fallback_test.txt");

        let test_content = "Success from first URL";
        let expected_checksum = "8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92"; // SHA256

        let _m1 = mock("GET", "/file1.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "test_fallback".to_string(),
            update_type: UpdateType::Full,
            urls: vec![
                format!("{}/file1.txt", &mockito::server_url()),
                format!("{}/file2.txt", &mockito::server_url()),
            ],
            output_path,
            expected_checksum: Some(expected_checksum.to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_execute_download_with_fallback_first_fail_second_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("fallback_test2.txt");

        let test_content = "Success from second URL";
        let expected_checksum = "5d41402abc4b2a76b9719d911017c592"; // 这里使用错误的哈希来测试

        // 第一个URL失败
        let _m1 = mock("GET", "/file1.txt")
            .with_status(500)
            .create();

        // 第二个URL成功
        let _m2 = mock("GET", "/file2.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig {
            address_switch_interval_seconds: 1, // 减少测试时间
            ..Default::default()
        };
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "test_fallback2".to_string(),
            update_type: UpdateType::Full,
            urls: vec![
                format!("{}/file1.txt", &mockito::server_url()),
                format!("{}/file2.txt", &mockito::server_url()),
            ],
            output_path,
            expected_checksum: None, // 不验证校验和
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_execute_download_with_fallback_checksum_mismatch() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("checksum_test.txt");

        let test_content = "Test content";
        let wrong_checksum = "wrong_checksum_value";

        let _m = mock("GET", "/file.txt")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig::default();
        let mut manager = SmartDownloadManager::new(config);

        let task = DownloadTask {
            task_id: "checksum_test".to_string(),
            update_type: UpdateType::Full,
            urls: vec![format!("{}/file.txt", &mockito::server_url())],
            output_path,
            expected_checksum: Some(wrong_checksum.to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        let progress_callback = |_progress: DownloadProgress| {};
        let result = manager.execute_download_with_fallback(task, progress_callback).await;
        
        // 应该失败，因为校验和不匹配
        assert!(result.is_err());
    }

    // ============================================================================
    // 5. 全量下载测试
    // ============================================================================

    #[tokio::test]
    async fn test_download_full_client_success() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("client.zip");

        let test_content = "Mock ZIP file content";
        let expected_checksum = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3";

        // 模拟获取更新信息的响应
        let update_info = format!(r#"{{"version":"1.0.0","full_download_urls":["{}"],"incremental_server_url":"http://example.com","file_list_url":"http://example.com/files","checksum":"{}"}}"#, 
            format!("{}/client.zip", &mockito::server_url()), expected_checksum);
        let encrypted_data = base64::encode(&update_info);

        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 模拟文件下载
        let _m2 = mock("GET", "/client.zip")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);
        let mut progress_updates = Vec::new();
        let progress_callback = |progress: DownloadProgress| {
            progress_updates.push(progress);
        };

        let result = manager.download_full_client(output_path.clone(), progress_callback).await;
        
        assert!(result.is_ok());
        assert!(output_path.exists());
        
        // 验证文件内容
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
        
        // 验证进度回调被调用
        assert!(!progress_updates.is_empty());
    }

    // ============================================================================
    // 6. 增量下载测试
    // ============================================================================

    #[tokio::test]
    async fn test_download_incremental_updates_success() {
        let temp_dir = TempDir::new().unwrap();

        // 创建测试文件信息
        let files_to_update = vec![
            FileUpdateInfo {
                path: "file1.txt".to_string(),
                hash: "hash1".to_string(),
                size: 100,
                local_path: temp_dir.path().join("file1.txt"),
            },
            FileUpdateInfo {
                path: "file2.txt".to_string(),
                hash: "hash2".to_string(),
                size: 200,
                local_path: temp_dir.path().join("file2.txt"),
            },
        ];

        // 模拟更新信息获取
        let update_info = r#"{"version":"1.0.0","full_download_urls":[],"incremental_server_url":"http://localhost:8080/files","file_list_url":"","checksum":""}"#;
        let encrypted_data = base64::encode(update_info);

        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 模拟文件下载
        let _m2 = mock("GET", "/files/file1.txt")
            .with_status(200)
            .with_body("Content of file1")
            .create();

        let _m3 = mock("GET", "/files/file2.txt")
            .with_status(200)
            .with_body("Content of file2")
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);
        let progress_callback = |_progress: DownloadProgress| {};

        let result = manager.download_incremental_updates(files_to_update, progress_callback).await;
        
        assert!(result.is_ok());
        
        // 验证文件被下载
        assert!(temp_dir.path().join("file1.txt").exists());
        assert!(temp_dir.path().join("file2.txt").exists());
    }

    // ============================================================================
    // 7. 数据结构测试
    // ============================================================================

    #[test]
    fn test_download_task_creation() {
        let task = DownloadTask {
            task_id: "test_task_123".to_string(),
            update_type: UpdateType::Incremental,
            urls: vec![
                "http://example.com/file1.zip".to_string(),
                "http://example.com/file2.zip".to_string(),
            ],
            output_path: std::path::PathBuf::from("/tmp/output.zip"),
            expected_checksum: Some("abc123def456".to_string()),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        assert_eq!(task.task_id, "test_task_123");
        assert!(matches!(task.update_type, UpdateType::Incremental));
        assert_eq!(task.urls.len(), 2);
        assert_eq!(task.urls[0], "http://example.com/file1.zip");
        assert_eq!(task.current_url_index, 0);
        assert_eq!(task.retry_count, 0);
        assert!(task.last_attempt.is_none());
    }

    #[test]
    fn test_download_progress_creation() {
        let progress = DownloadProgress {
            downloaded_bytes: 2048,
            total_bytes: 4096,
            speed_bytes_per_sec: 1024,
            eta_seconds: Some(2),
            current_url: "http://example.com/file.zip".to_string(),
        };

        assert_eq!(progress.downloaded_bytes, 2048);
        assert_eq!(progress.total_bytes, 4096);
        assert_eq!(progress.speed_bytes_per_sec, 1024);
        assert_eq!(progress.eta_seconds, Some(2));
        assert_eq!(progress.current_url, "http://example.com/file.zip");
    }

    #[test]
    fn test_file_update_info_creation() {
        let file_info = FileUpdateInfo {
            path: "game/data/config.xml".to_string(),
            hash: "sha256hash".to_string(),
            size: 1024,
            local_path: std::path::PathBuf::from("/game/data/config.xml"),
        };

        assert_eq!(file_info.path, "game/data/config.xml");
        assert_eq!(file_info.hash, "sha256hash");
        assert_eq!(file_info.size, 1024);
        assert_eq!(file_info.local_path, std::path::PathBuf::from("/game/data/config.xml"));
    }

    // ============================================================================
    // 8. 性能测试
    // ============================================================================

    #[tokio::test]
    async fn test_concurrent_downloads_performance() {
        let temp_dir = TempDir::new().unwrap();
        let test_content = "x".repeat(1024); // 1KB content

        // 创建多个模拟端点
        let mut mocks = Vec::new();
        for i in 0..5 {
            let mock = mock("GET", &format!("/perf_test_{}.txt", i))
                .with_status(200)
                .with_header("content-length", &test_content.len().to_string())
                .with_body(&test_content)
                .create();
            mocks.push(mock);
        }

        let config = UpdateConfig::default();
        let manager = SmartDownloadManager::new(config);

        let start_time = Instant::now();
        
        // 并发下载多个文件
        let mut tasks = Vec::new();
        for i in 0..5 {
            let output_path = temp_dir.path().join(format!("perf_test_{}.txt", i));
            let url = format!("{}/perf_test_{}.txt", &mockito::server_url(), i);
            let progress_callback = |_progress: DownloadProgress| {};
            
            let task = manager.download_from_url(&url, &output_path, &progress_callback);
            tasks.push(task);
        }

        // 等待所有下载完成
        let results = futures::future::join_all(tasks).await;
        let elapsed = start_time.elapsed();

        // 验证所有下载都成功
        for result in results {
            assert!(result.is_ok());
        }

        // 性能断言
        assert!(elapsed.as_secs() < 10, "Concurrent downloads took too long: {:?}", elapsed);
        
        // 验证所有文件都被创建
        for i in 0..5 {
            let file_path = temp_dir.path().join(format!("perf_test_{}.txt", i));
            assert!(file_path.exists());
            let content = std::fs::read_to_string(&file_path).unwrap();
            assert_eq!(content, test_content);
        }
    }
}
