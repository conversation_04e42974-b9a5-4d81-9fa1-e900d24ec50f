#!/bin/bash

# Tera Launcher 下载系统测试运行脚本
# 提供完整的测试执行、报告生成和结果分析

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="Tera Launcher Download System"
TEST_OUTPUT_DIR="test_results"
COVERAGE_OUTPUT_DIR="coverage_reports"
BENCHMARK_OUTPUT_DIR="benchmark_results"
LOG_FILE="test_execution.log"

# 创建输出目录
mkdir -p "$TEST_OUTPUT_DIR"
mkdir -p "$COVERAGE_OUTPUT_DIR"
mkdir -p "$BENCHMARK_OUTPUT_DIR"

# 日志函数
log() {
    echo -e "${CYAN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}❌ $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

# 显示帮助信息
show_help() {
    echo -e "${PURPLE}$PROJECT_NAME Test Runner${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -a, --all               Run all tests (default)"
    echo "  -u, --unit              Run unit tests only"
    echo "  -i, --integration       Run integration tests only"
    echo "  -p, --performance       Run performance tests"
    echo "  -c, --coverage          Generate coverage report"
    echo "  -b, --benchmark         Run benchmarks"
    echo "  -q, --quick             Run quick tests (skip slow tests)"
    echo "  -v, --verbose           Verbose output"
    echo "  -f, --fail-fast         Stop on first failure"
    echo "  --clean                 Clean previous test results"
    echo "  --report                Generate comprehensive report"
    echo "  --ci                    CI mode (optimized for CI/CD)"
    echo ""
    echo "Examples:"
    echo "  $0 --all --coverage     # Run all tests with coverage"
    echo "  $0 --unit --verbose     # Run unit tests with verbose output"
    echo "  $0 --quick              # Quick test run"
    echo "  $0 --ci                 # CI/CD optimized run"
}

# 清理函数
cleanup() {
    log "Cleaning up..."
    # 停止可能运行的模拟服务器
    pkill -f "mockito" 2>/dev/null || true
    # 清理临时文件
    rm -rf /tmp/tera_test_* 2>/dev/null || true
}

# 设置清理陷阱
trap cleanup EXIT

# 检查依赖
check_dependencies() {
    log "Checking dependencies..."
    
    # 检查 Rust 工具链
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo not found. Please install Rust toolchain."
        exit 1
    fi
    
    # 检查必要的 cargo 子命令
    local required_tools=("tarpaulin" "criterion")
    for tool in "${required_tools[@]}"; do
        if ! cargo "$tool" --version &> /dev/null; then
            log_warning "$tool not found. Installing..."
            cargo install cargo-"$tool" || log_warning "Failed to install $tool"
        fi
    done
    
    log_success "Dependencies check completed"
}

# 设置测试环境
setup_test_environment() {
    log "Setting up test environment..."
    
    # 设置环境变量
    export RUST_LOG=debug
    export RUST_BACKTRACE=1
    export TEST_TIMEOUT=300
    export MOCK_SERVER_PORT=8080
    
    # 创建测试数据目录
    mkdir -p tests/data/mock_files
    mkdir -p tests/data/configs
    mkdir -p tests/data/expected
    
    # 生成测试配置文件
    cat > tests/data/configs/test_config.json << EOF
{
    "gitee_url": "http://localhost:8080/gitee/test",
    "weibo_url": "http://localhost:8080/weibo/test",
    "retry_interval_seconds": 1,
    "address_switch_interval_seconds": 1,
    "max_retry_attempts": 3,
    "download_timeout_seconds": 30
}
EOF
    
    log_success "Test environment setup completed"
}

# 运行单元测试
run_unit_tests() {
    log "Running unit tests..."
    
    local test_args=""
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args -- --nocapture"
    fi
    
    if [[ "$FAIL_FAST" == "true" ]]; then
        test_args="$test_args --"
    else
        test_args="$test_args -- --no-fail-fast"
    fi
    
    # 运行 WebContentFetcher 测试
    log_info "Running WebContentFetcher tests..."
    if cargo test web_content_fetcher_tests $test_args; then
        log_success "WebContentFetcher tests passed"
    else
        log_error "WebContentFetcher tests failed"
        return 1
    fi
    
    # 运行 SmartDownloadManager 测试
    log_info "Running SmartDownloadManager tests..."
    if cargo test smart_download_manager_tests $test_args; then
        log_success "SmartDownloadManager tests passed"
    else
        log_error "SmartDownloadManager tests failed"
        return 1
    fi
    
    # 运行 Tauri 集成测试
    log_info "Running Tauri integration tests..."
    if cargo test tauri_integration_tests $test_args; then
        log_success "Tauri integration tests passed"
    else
        log_error "Tauri integration tests failed"
        return 1
    fi
    
    log_success "All unit tests passed"
}

# 运行集成测试
run_integration_tests() {
    log "Running integration tests..."
    
    local test_args="--test integration_tests"
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args -- --nocapture"
    fi
    
    if cargo test $test_args; then
        log_success "Integration tests passed"
    else
        log_error "Integration tests failed"
        return 1
    fi
}

# 运行性能测试
run_performance_tests() {
    log "Running performance tests..."
    
    # 设置性能测试环境变量
    export PERFORMANCE_TEST_ITERATIONS=50
    export CONCURRENT_DOWNLOAD_COUNT=5
    export MAX_FILE_SIZE_MB=10
    
    local test_args="--features performance-tests"
    if [[ "$VERBOSE" == "true" ]]; then
        test_args="$test_args -- --nocapture"
    fi
    
    # 运行性能相关的测试
    if cargo test $test_args test_concurrent_downloads_performance; then
        log_success "Performance tests passed"
    else
        log_error "Performance tests failed"
        return 1
    fi
}

# 生成覆盖率报告
generate_coverage_report() {
    log "Generating coverage report..."
    
    if command -v cargo-tarpaulin &> /dev/null; then
        cargo tarpaulin \
            --out Html \
            --out Xml \
            --out Json \
            --output-dir "$COVERAGE_OUTPUT_DIR" \
            --exclude-files "tests/*" \
            --exclude-files "benches/*" \
            --fail-under 80 \
            --verbose
        
        if [[ $? -eq 0 ]]; then
            log_success "Coverage report generated in $COVERAGE_OUTPUT_DIR"
            log_info "Open $COVERAGE_OUTPUT_DIR/tarpaulin-report.html to view the report"
        else
            log_error "Coverage report generation failed"
            return 1
        fi
    else
        log_warning "cargo-tarpaulin not available, skipping coverage report"
    fi
}

# 运行基准测试
run_benchmarks() {
    log "Running benchmarks..."
    
    if command -v cargo-criterion &> /dev/null; then
        cargo bench --features benchmark -- --output-format html
        
        if [[ $? -eq 0 ]]; then
            log_success "Benchmarks completed"
            log_info "Benchmark results saved in target/criterion"
        else
            log_error "Benchmarks failed"
            return 1
        fi
    else
        log_warning "cargo-criterion not available, skipping benchmarks"
    fi
}

# 运行快速测试
run_quick_tests() {
    log "Running quick tests..."
    
    # 只运行标记为快速的测试
    cargo test --features unit-tests -- --skip slow
    
    if [[ $? -eq 0 ]]; then
        log_success "Quick tests passed"
    else
        log_error "Quick tests failed"
        return 1
    fi
}

# 生成综合报告
generate_comprehensive_report() {
    log "Generating comprehensive test report..."
    
    local report_file="$TEST_OUTPUT_DIR/comprehensive_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>$PROJECT_NAME - Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>$PROJECT_NAME - Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test execution log: <a href="../$LOG_FILE">$LOG_FILE</a></p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <ul>
            <li>Unit Tests: $(grep -c "unit tests" "$LOG_FILE" || echo "N/A")</li>
            <li>Integration Tests: $(grep -c "integration tests" "$LOG_FILE" || echo "N/A")</li>
            <li>Performance Tests: $(grep -c "performance tests" "$LOG_FILE" || echo "N/A")</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Coverage Report</h2>
        <p><a href="../$COVERAGE_OUTPUT_DIR/tarpaulin-report.html">View Coverage Report</a></p>
    </div>
    
    <div class="section">
        <h2>Benchmark Results</h2>
        <p><a href="../target/criterion/report/index.html">View Benchmark Report</a></p>
    </div>
    
    <div class="section">
        <h2>Test Execution Log</h2>
        <pre>$(tail -50 "$LOG_FILE")</pre>
    </div>
</body>
</html>
EOF
    
    log_success "Comprehensive report generated: $report_file"
}

# CI 模式运行
run_ci_mode() {
    log "Running in CI mode..."
    
    # CI 优化的测试配置
    export RUST_LOG=info
    export TEST_TIMEOUT=600
    
    # 运行所有测试但跳过交互式测试
    cargo test --all-features -- --skip interactive
    
    # 生成覆盖率报告
    generate_coverage_report
    
    # 检查覆盖率阈值
    if [[ -f "$COVERAGE_OUTPUT_DIR/cobertura.xml" ]]; then
        local coverage=$(grep -o 'line-rate="[^"]*"' "$COVERAGE_OUTPUT_DIR/cobertura.xml" | head -1 | cut -d'"' -f2)
        local coverage_percent=$(echo "$coverage * 100" | bc -l | cut -d'.' -f1)
        
        if [[ $coverage_percent -lt 80 ]]; then
            log_error "Coverage $coverage_percent% is below threshold (80%)"
            exit 1
        else
            log_success "Coverage $coverage_percent% meets threshold"
        fi
    fi
}

# 清理之前的结果
clean_previous_results() {
    log "Cleaning previous test results..."
    rm -rf "$TEST_OUTPUT_DIR"
    rm -rf "$COVERAGE_OUTPUT_DIR"
    rm -rf "$BENCHMARK_OUTPUT_DIR"
    rm -f "$LOG_FILE"
    cargo clean
    log_success "Previous results cleaned"
}

# 主函数
main() {
    # 解析命令行参数
    local run_all=true
    local run_unit=false
    local run_integration=false
    local run_performance=false
    local generate_coverage=false
    local run_benchmarks=false
    local quick_mode=false
    local generate_report=false
    local ci_mode=false
    local clean_first=false
    
    VERBOSE=false
    FAIL_FAST=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                run_all=true
                shift
                ;;
            -u|--unit)
                run_all=false
                run_unit=true
                shift
                ;;
            -i|--integration)
                run_all=false
                run_integration=true
                shift
                ;;
            -p|--performance)
                run_performance=true
                shift
                ;;
            -c|--coverage)
                generate_coverage=true
                shift
                ;;
            -b|--benchmark)
                run_benchmarks=true
                shift
                ;;
            -q|--quick)
                quick_mode=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--fail-fast)
                FAIL_FAST=true
                shift
                ;;
            --clean)
                clean_first=true
                shift
                ;;
            --report)
                generate_report=true
                shift
                ;;
            --ci)
                ci_mode=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示启动信息
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                 $PROJECT_NAME                 ║"
    echo "║                        Test Runner                           ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # 清理之前的结果（如果需要）
    if [[ "$clean_first" == "true" ]]; then
        clean_previous_results
    fi
    
    # 检查依赖
    check_dependencies
    
    # 设置测试环境
    setup_test_environment
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # CI 模式
    if [[ "$ci_mode" == "true" ]]; then
        run_ci_mode
        exit $?
    fi
    
    # 快速模式
    if [[ "$quick_mode" == "true" ]]; then
        run_quick_tests
        exit $?
    fi
    
    # 运行测试
    local test_failed=false
    
    if [[ "$run_all" == "true" ]] || [[ "$run_unit" == "true" ]]; then
        if ! run_unit_tests; then
            test_failed=true
        fi
    fi
    
    if [[ "$run_all" == "true" ]] || [[ "$run_integration" == "true" ]]; then
        if ! run_integration_tests; then
            test_failed=true
        fi
    fi
    
    if [[ "$run_performance" == "true" ]]; then
        if ! run_performance_tests; then
            test_failed=true
        fi
    fi
    
    # 生成覆盖率报告
    if [[ "$generate_coverage" == "true" ]] || [[ "$run_all" == "true" ]]; then
        generate_coverage_report
    fi
    
    # 运行基准测试
    if [[ "$run_benchmarks" == "true" ]]; then
        run_benchmarks
    fi
    
    # 生成综合报告
    if [[ "$generate_report" == "true" ]] || [[ "$run_all" == "true" ]]; then
        generate_comprehensive_report
    fi
    
    # 计算总时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 显示最终结果
    echo ""
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    if [[ "$test_failed" == "true" ]]; then
        echo -e "${PURPLE}║${RED}                    TESTS FAILED                             ${PURPLE}║${NC}"
        echo -e "${PURPLE}║${NC}  Some tests failed. Check the log for details.             ${PURPLE}║${NC}"
    else
        echo -e "${PURPLE}║${GREEN}                   TESTS PASSED                             ${PURPLE}║${NC}"
        echo -e "${PURPLE}║${NC}  All tests completed successfully!                          ${PURPLE}║${NC}"
    fi
    echo -e "${PURPLE}║${NC}  Total execution time: ${duration}s                           ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  Log file: $LOG_FILE                                ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    # 退出码
    if [[ "$test_failed" == "true" ]]; then
        exit 1
    else
        exit 0
    fi
}

# 运行主函数
main "$@"
