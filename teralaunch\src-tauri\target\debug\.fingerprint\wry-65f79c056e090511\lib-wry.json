{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\", \"tracing\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 17874989004314119331, "profile": 15657897354478470176, "path": 8697551644951991076, "deps": [[1076501750996383263, "once_cell", false, 11895857675154832601], [3353796506826114049, "thiserror", false, 6358471964182336296], [3540822385484940109, "windows_implement", false, 15649336191604428548], [4381063397040571828, "webview2_com", false, 17950373685392314976], [4405182208873388884, "http", false, 7948823251931111416], [4704408070981680310, "serde_json", false, 11199486250301317617], [7314894124883917868, "log", false, 7275729046824401138], [7653476968652377684, "windows", false, 6375946999918238853], [9181830537994570299, "tao", false, 8639747391903300895], [10229761508789367700, "libc", false, 11679455655556748723], [11269248247346606853, "url", false, 12915646722162745668], [14626413149905853098, "tracing", false, 2674625738167854746], [17068110978337472778, "build_script_build", false, 9731910130829167512], [17174345729924723953, "serde", false, 2591028312899638256], [18053436020821374870, "dunce", false, 8109531313742568891]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-65f79c056e090511\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}