{"rustc": 1842507548689473721, "features": "[\"compression\", \"shell-scope\", \"tracing\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 7777433936937008561, "profile": 1369601567987815722, "path": 10985147272338128700, "deps": [[1144798529435568546, "tauri_utils", false, 11208090494566982770], [2537567469363538103, "proc_macro2", false, 743369950721806150], [2713742371683562785, "syn", false, 9631889259208296532], [7870143691443937648, "tauri_codegen", false, 15107466953393734378], [13077543566650298139, "heck", false, 1577398889775765422], [16437840124237027127, "quote", false, 10508328123799040355]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-db700bfe0a9f65a0\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}