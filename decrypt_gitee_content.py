#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解密Gitee内容脚本
"""

import base64
import urllib.parse
import requests

def decrypt_base64_content():
    """解密Base64内容"""
    
    # 从Gitee获取的Base64内容
    base64_content = "aHR0cHM6Ly9wYXJzZS5seG93bi5jb20vTmV0ZGlza19kaXJlY3RsaW5rP0NhcmQ9NDZCNUYwMUM4NUZDOEFNSzBESFNHSzlYUklVSSZOZXRkaXNrPWxhbnpvdVUmU2hhcmVMaW5rPWh0dHBzOi8vd3d3LmlsYW56b3UuY29tL3Mvb1Z2WjNaMDc="
    
    print("🔓 Gitee Base64内容解密")
    print("=" * 50)
    
    try:
        # Base64解码
        decoded_bytes = base64.b64decode(base64_content)
        decoded_url = decoded_bytes.decode('utf-8').strip()
        
        print(f"✅ 解密成功!")
        print(f"📄 原始Base64长度: {len(base64_content)} 字符")
        print(f"📄 解密后长度: {len(decoded_url)} 字符")
        print()
        print(f"🌐 解密后的URL:")
        print(decoded_url)
        print()
        
        # 解析URL参数
        parsed = urllib.parse.urlparse(decoded_url)
        params = urllib.parse.parse_qs(parsed.query)
        
        print("📋 URL参数解析:")
        print(f"  🏠 主机: {parsed.netloc}")
        print(f"  📂 路径: {parsed.path}")
        print(f"  🔑 Card: {params.get('Card', ['N/A'])[0]}")
        print(f"  💾 Netdisk: {params.get('Netdisk', ['N/A'])[0]}")
        print(f"  🔗 ShareLink: {params.get('ShareLink', ['N/A'])[0]}")
        print()
        
        print("🔍 分析结果:")
        print("  这是一个网盘直链解析服务URL")
        print("  用于将蓝奏云(lanzouU)分享链接转换为直接下载链接")
        print("  原始分享链接: https://www.ilanzou.com/s/oVvZ3Z07")
        print()
        
        return decoded_url, params
        
    except Exception as e:
        print(f"❌ 解密失败: {e}")
        return None, None

def test_direct_link_service(url):
    """测试直链解析服务"""
    print("🧪 测试直链解析服务")
    print("=" * 50)
    
    try:
        print("📡 正在请求直链解析服务...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        print(f"📏 响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ 服务响应正常")
            
            # 检查响应内容
            content = response.text.strip()
            print(f"📄 响应内容: {content[:200]}...")
            
            # 检查是否是直接下载链接
            if content.startswith('http'):
                print("🎯 获取到直接下载链接:")
                print(content)
                return content
            else:
                print("⚠️ 响应不是直接下载链接")
                return None
        else:
            print(f"❌ 服务响应异常: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None

def main():
    print("🔐 Gitee内容解密和测试工具")
    print("=" * 60)
    
    # 解密Base64内容
    decoded_url, params = decrypt_base64_content()
    
    if decoded_url:
        # 测试直链解析服务
        direct_link = test_direct_link_service(decoded_url)
        
        if direct_link:
            print("\n🎉 完整流程测试成功!")
            print("=" * 50)
            print("📋 总结:")
            print(f"  1. ✅ Base64解密成功")
            print(f"  2. ✅ URL解析成功")
            print(f"  3. ✅ 直链服务响应正常")
            print(f"  4. ✅ 获取到直接下载链接")
            print()
            print("🚀 可以在启动器中使用此解密逻辑!")
        else:
            print("\n⚠️ 直链解析服务测试失败")
            print("但Base64解密功能正常，可以集成到启动器中")
    else:
        print("\n❌ 解密失败，请检查Base64内容")

if __name__ == "__main__":
    main()
