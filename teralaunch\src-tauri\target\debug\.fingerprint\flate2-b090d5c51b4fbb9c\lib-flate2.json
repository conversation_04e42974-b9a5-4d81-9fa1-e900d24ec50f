{"rustc": 1842507548689473721, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 7395671289533959251, "profile": 15657897354478470176, "path": 16468801252226967131, "deps": [[1389548511596694517, "miniz_oxide", false, 9261737382164324282], [5466618496199522463, "crc32fast", false, 12635705759980533536]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-b090d5c51b4fbb9c\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}