# Tera Launcher 增强下载系统生产环境启动脚本
# 启动生产环境并进行实时监控

param(
    [switch]$Build,
    [switch]$Release,
    [switch]$Monitor,
    [switch]$Verbose,
    [string]$LogLevel = "info"
)

# 颜色定义
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"
$Magenta = "Magenta"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Log "✅ $Message" $Green
}

function Write-Error {
    param([string]$Message)
    Write-Log "❌ $Message" $Red
}

function Write-Warning {
    param([string]$Message)
    Write-Log "⚠️  $Message" $Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Log "ℹ️  $Message" $Blue
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-Host $Message -ForegroundColor Cyan
    Write-Host ("=" * $Message.Length) -ForegroundColor Cyan
}

# 全局变量
$script:ProcessId = $null
$script:LogFile = "production_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$script:StartTime = Get-Date

# 主启动函数
function Start-ProductionEnvironment {
    Write-Header "🚀 Tera Launcher 增强下载系统 - 生产环境启动"
    
    # 1. 环境检查
    Test-ProductionReadiness
    
    # 2. 构建应用（如果需要）
    if ($Build) {
        Build-Application
    }
    
    # 3. 启动应用
    Start-Application
    
    # 4. 监控（如果启用）
    if ($Monitor) {
        Start-Monitoring
    }
}

# 生产就绪性检查
function Test-ProductionReadiness {
    Write-Info "检查生产环境就绪性..."
    
    # 运行验证脚本
    if (Test-Path "verify_production_deployment.ps1") {
        Write-Info "运行生产环境验证..."
        $verifyResult = & powershell -File "verify_production_deployment.ps1" -SkipTests:$(!$Verbose)
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "生产环境验证通过"
        } else {
            Write-Error "生产环境验证失败，请检查问题后重试"
            exit 1
        }
    } else {
        Write-Warning "未找到验证脚本，跳过验证"
    }
    
    # 检查配置文件
    if (Test-Path "teralaunch/src-tauri/enhanced_download_config.json") {
        Write-Success "增强下载配置文件存在"
    } else {
        Write-Warning "增强下载配置文件不存在，将使用默认配置"
    }
    
    # 检查日志目录
    $logDir = "logs"
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        Write-Info "创建日志目录: $logDir"
    }
}

# 构建应用
function Build-Application {
    Write-Header "🔨 构建应用"
    
    Push-Location "teralaunch"
    try {
        # 安装前端依赖
        if (Test-Path "package.json") {
            Write-Info "安装前端依赖..."
            $npmResult = & npm install 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Success "前端依赖安装完成"
            } else {
                Write-Warning "前端依赖安装失败: $npmResult"
            }
        }
        
        # 构建Tauri应用
        Write-Info "构建Tauri应用..."
        $buildCommand = if ($Release) { "npm run tauri build" } else { "npm run tauri build --debug" }
        
        $buildResult = Invoke-Expression $buildCommand 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "应用构建完成"
        } else {
            Write-Error "应用构建失败: $buildResult"
            exit 1
        }
        
    } finally {
        Pop-Location
    }
}

# 启动应用
function Start-Application {
    Write-Header "🚀 启动应用"
    
    # 设置环境变量
    $env:RUST_LOG = $LogLevel
    $env:ENHANCED_DOWNLOAD_ENABLED = "true"
    $env:PRODUCTION_MODE = "true"
    
    if ($Verbose) {
        $env:RUST_LOG = "debug"
        $env:ENHANCED_DOWNLOAD_DEBUG = "true"
    }
    
    Write-Info "环境变量设置:"
    Write-Info "  RUST_LOG = $env:RUST_LOG"
    Write-Info "  ENHANCED_DOWNLOAD_ENABLED = $env:ENHANCED_DOWNLOAD_ENABLED"
    Write-Info "  PRODUCTION_MODE = $env:PRODUCTION_MODE"
    
    # 确定可执行文件路径
    $exePath = if ($Release) {
        "teralaunch/src-tauri/target/release/teralaunch.exe"
    } else {
        "teralaunch/src-tauri/target/debug/teralaunch.exe"
    }
    
    if (-not (Test-Path $exePath)) {
        Write-Error "未找到可执行文件: $exePath"
        Write-Info "请先运行构建: $($MyInvocation.MyCommand.Name) -Build"
        exit 1
    }
    
    Write-Info "启动应用: $exePath"
    Write-Info "日志文件: logs/$script:LogFile"
    
    # 启动应用进程
    try {
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = $exePath
        $processInfo.UseShellExecute = $false
        $processInfo.RedirectStandardOutput = $true
        $processInfo.RedirectStandardError = $true
        $processInfo.CreateNoWindow = $false
        
        $process = New-Object System.Diagnostics.Process
        $process.StartInfo = $processInfo
        
        # 设置输出处理
        $outputAction = {
            param($sender, $e)
            if ($e.Data) {
                $logMessage = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [OUT] $($e.Data)"
                Write-Host $logMessage -ForegroundColor White
                Add-Content -Path "logs/$script:LogFile" -Value $logMessage
            }
        }
        
        $errorAction = {
            param($sender, $e)
            if ($e.Data) {
                $logMessage = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [ERR] $($e.Data)"
                Write-Host $logMessage -ForegroundColor Red
                Add-Content -Path "logs/$script:LogFile" -Value $logMessage
            }
        }
        
        Register-ObjectEvent -InputObject $process -EventName OutputDataReceived -Action $outputAction | Out-Null
        Register-ObjectEvent -InputObject $process -EventName ErrorDataReceived -Action $errorAction | Out-Null
        
        # 启动进程
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        $script:ProcessId = $process.Id
        Write-Success "应用已启动，进程ID: $script:ProcessId"
        
        # 等待进程启动
        Start-Sleep -Seconds 3
        
        if (-not $process.HasExited) {
            Write-Success "应用运行正常"
            
            # 如果不是监控模式，等待用户输入
            if (-not $Monitor) {
                Write-Info "按任意键停止应用..."
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                Stop-Application $process
            } else {
                return $process
            }
        } else {
            Write-Error "应用启动后立即退出，退出代码: $($process.ExitCode)"
            exit 1
        }
        
    } catch {
        Write-Error "启动应用时发生错误: $($_.Exception.Message)"
        exit 1
    }
}

# 停止应用
function Stop-Application {
    param($Process)
    
    Write-Info "正在停止应用..."
    
    try {
        if ($Process -and -not $Process.HasExited) {
            $Process.CloseMainWindow()
            
            # 等待优雅关闭
            if (-not $Process.WaitForExit(5000)) {
                Write-Warning "应用未在5秒内关闭，强制终止"
                $Process.Kill()
            }
            
            Write-Success "应用已停止"
        }
    } catch {
        Write-Error "停止应用时发生错误: $($_.Exception.Message)"
    }
}

# 启动监控
function Start-Monitoring {
    Write-Header "📊 启动系统监控"
    
    $process = Start-Application
    
    if (-not $process) {
        Write-Error "无法启动监控，应用未运行"
        return
    }
    
    Write-Info "监控模式已启动，按 Ctrl+C 停止"
    Write-Info "监控指标:"
    Write-Info "  - 进程状态"
    Write-Info "  - 内存使用"
    Write-Info "  - CPU使用率"
    Write-Info "  - 日志输出"
    
    # 监控循环
    try {
        while (-not $process.HasExited) {
            # 获取进程信息
            $processInfo = Get-Process -Id $process.Id -ErrorAction SilentlyContinue
            
            if ($processInfo) {
                $memoryMB = [math]::Round($processInfo.WorkingSet64 / 1MB, 2)
                $cpuTime = $processInfo.TotalProcessorTime
                
                # 显示监控信息
                $uptime = (Get-Date) - $script:StartTime
                $uptimeStr = "{0:hh\:mm\:ss}" -f $uptime
                
                Write-Host "`r监控状态 [运行时间: $uptimeStr] [内存: ${memoryMB}MB] [进程ID: $($process.Id)]" -NoNewline -ForegroundColor Green
                
                # 检查日志文件大小
                $logPath = "logs/$script:LogFile"
                if (Test-Path $logPath) {
                    $logSize = [math]::Round((Get-Item $logPath).Length / 1KB, 2)
                    if ($logSize -gt 1000) {  # 如果日志文件超过1MB
                        Write-Warning "`n日志文件较大: ${logSize}KB，建议检查"
                    }
                }
            } else {
                Write-Error "`n进程已意外退出"
                break
            }
            
            Start-Sleep -Seconds 2
        }
    } catch {
        Write-Error "监控过程中发生错误: $($_.Exception.Message)"
    } finally {
        Stop-Application $process
    }
}

# 清理函数
function Cleanup {
    Write-Info "正在清理资源..."
    
    if ($script:ProcessId) {
        try {
            $process = Get-Process -Id $script:ProcessId -ErrorAction SilentlyContinue
            if ($process) {
                Stop-Application $process
            }
        } catch {
            Write-Warning "清理进程时发生错误: $($_.Exception.Message)"
        }
    }
    
    # 清理事件订阅
    Get-EventSubscriber | Unregister-Event
    
    Write-Success "清理完成"
}

# 信号处理
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Cleanup
}

# Ctrl+C 处理
[Console]::TreatControlCAsInput = $false
$null = Register-ObjectEvent -InputObject ([Console]) -EventName CancelKeyPress -Action {
    Write-Host "`n正在停止..." -ForegroundColor Yellow
    Cleanup
    exit 0
}

# 显示帮助信息
function Show-Help {
    Write-Host ""
    Write-Host "Tera Launcher 增强下载系统生产环境启动脚本" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法:" -ForegroundColor White
    Write-Host "  $($MyInvocation.MyCommand.Name) [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor White
    Write-Host "  -Build          构建应用后启动" -ForegroundColor Green
    Write-Host "  -Release        使用Release模式构建" -ForegroundColor Green
    Write-Host "  -Monitor        启用监控模式" -ForegroundColor Green
    Write-Host "  -Verbose        启用详细日志" -ForegroundColor Green
    Write-Host "  -LogLevel       设置日志级别 (debug|info|warn|error)" -ForegroundColor Green
    Write-Host ""
    Write-Host "示例:" -ForegroundColor White
    Write-Host "  $($MyInvocation.MyCommand.Name) -Build -Release -Monitor" -ForegroundColor Yellow
    Write-Host "  $($MyInvocation.MyCommand.Name) -Verbose -LogLevel debug" -ForegroundColor Yellow
    Write-Host ""
}

# 主程序入口
if ($args -contains "-h" -or $args -contains "--help" -or $args -contains "help") {
    Show-Help
    exit 0
}

try {
    Start-ProductionEnvironment
} catch {
    Write-Error "启动过程中发生错误: $($_.Exception.Message)"
    Cleanup
    exit 1
}

Write-Success "生产环境启动完成"
