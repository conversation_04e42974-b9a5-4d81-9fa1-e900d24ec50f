{"rustc": 1842507548689473721, "features": "[\"compression\", \"shell-scope\", \"tracing\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 7777433936937008561, "profile": 2225463790103693989, "path": 10985147272338128700, "deps": [[1144798529435568546, "tauri_utils", false, 6005736629602156391], [2537567469363538103, "proc_macro2", false, 2633387941319026123], [2713742371683562785, "syn", false, 14049580308614544169], [7870143691443937648, "tauri_codegen", false, 1839014765833815256], [13077543566650298139, "heck", false, 8749295945489699306], [16437840124237027127, "quote", false, 8978455854684825129]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-5e63c101060cbff9\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}