// Tauri 集成代码 - 将最优下载系统集成到现有启动器中

use crate::optimal_download_system::*;
use tauri::{Window, State};
use std::sync::Arc;
use tokio::sync::Mutex;
use serde_json::json;

// ============================================================================
// 1. 全局状态管理
// ============================================================================

pub struct DownloadState {
    pub manager: Arc<Mutex<Option<SmartDownloadManager>>>,
    pub config: Arc<Mutex<UpdateConfig>>,
}

impl DownloadState {
    pub fn new() -> Self {
        Self {
            manager: Arc::new(Mutex::new(None)),
            config: Arc::new(Mutex::new(UpdateConfig::default())),
        }
    }
}

// ============================================================================
// 2. Tauri 命令实现
// ============================================================================

#[tauri::command]
pub async fn initialize_download_system(
    state: State<'_, DownloadState>,
    gitee_url: Option<String>,
    weibo_url: Option<String>,
) -> Result<String, String> {
    let mut config = state.config.lock().await;
    
    if let Some(url) = gitee_url {
        config.gitee_url = url;
    }
    if let Some(url) = weibo_url {
        config.weibo_url = url;
    }

    let manager = SmartDownloadManager::new(config.clone());
    let mut manager_guard = state.manager.lock().await;
    *manager_guard = Some(manager);

    Ok("Download system initialized successfully".to_string())
}

#[tauri::command]
pub async fn fetch_update_info(
    state: State<'_, DownloadState>,
) -> Result<serde_json::Value, String> {
    let manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_ref()
        .ok_or("Download system not initialized")?;

    let web_fetcher = WebContentFetcher::new(state.config.lock().await.clone());
    
    match web_fetcher.fetch_encrypted_update_info().await {
        Ok(encrypted_info) => {
            match web_fetcher.decrypt_update_info(&encrypted_info) {
                Ok(decrypted_info) => {
                    Ok(json!({
                        "success": true,
                        "version": decrypted_info.version,
                        "full_download_urls_count": decrypted_info.full_download_urls.len(),
                        "incremental_server_url": decrypted_info.incremental_server_url,
                        "checksum": decrypted_info.checksum
                    }))
                }
                Err(e) => {
                    Ok(json!({
                        "success": false,
                        "error": format!("Decryption failed: {}", e)
                    }))
                }
            }
        }
        Err(e) => {
            Ok(json!({
                "success": false,
                "error": format!("Failed to fetch update info: {}", e)
            }))
        }
    }
}

#[tauri::command]
pub async fn start_full_download(
    state: State<'_, DownloadState>,
    output_path: String,
    window: Window,
) -> Result<String, String> {
    let mut manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_mut()
        .ok_or("Download system not initialized")?;

    let output_path = std::path::PathBuf::from(output_path);
    
    // 创建进度回调
    let window_clone = window.clone();
    let progress_callback = move |progress: DownloadProgress| {
        let payload = json!({
            "type": "full_download_progress",
            "downloaded_bytes": progress.downloaded_bytes,
            "total_bytes": progress.total_bytes,
            "speed_bytes_per_sec": progress.speed_bytes_per_sec,
            "eta_seconds": progress.eta_seconds,
            "current_url": progress.current_url,
            "progress_percentage": if progress.total_bytes > 0 {
                (progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0
            } else {
                0.0
            }
        });

        if let Err(e) = window_clone.emit("download_progress", payload) {
            log::error!("Failed to emit progress event: {}", e);
        }
    };

    // 启动下载任务
    tokio::spawn(async move {
        match manager.download_full_client(output_path, progress_callback).await {
            Ok(_) => {
                let completion_payload = json!({
                    "type": "full_download_complete",
                    "success": true
                });
                if let Err(e) = window.emit("download_complete", completion_payload) {
                    log::error!("Failed to emit completion event: {}", e);
                }
            }
            Err(e) => {
                let error_payload = json!({
                    "type": "full_download_error",
                    "success": false,
                    "error": e
                });
                if let Err(e) = window.emit("download_complete", error_payload) {
                    log::error!("Failed to emit error event: {}", e);
                }
            }
        }
    });

    Ok("Full download started".to_string())
}

#[tauri::command]
pub async fn start_incremental_download(
    state: State<'_, DownloadState>,
    files_to_update: Vec<serde_json::Value>,
    window: Window,
) -> Result<String, String> {
    let mut manager_guard = state.manager.lock().await;
    let manager = manager_guard.as_mut()
        .ok_or("Download system not initialized")?;

    // 解析文件更新信息
    let mut file_updates = Vec::new();
    for file_json in files_to_update {
        let file_info = FileUpdateInfo {
            path: file_json["path"].as_str().unwrap_or("").to_string(),
            hash: file_json["hash"].as_str().unwrap_or("").to_string(),
            size: file_json["size"].as_u64().unwrap_or(0),
            local_path: std::path::PathBuf::from(
                file_json["local_path"].as_str().unwrap_or("")
            ),
        };
        file_updates.push(file_info);
    }

    let total_files = file_updates.len();
    let window_clone = window.clone();
    
    // 创建进度回调
    let progress_callback = move |progress: DownloadProgress| {
        let payload = json!({
            "type": "incremental_download_progress",
            "downloaded_bytes": progress.downloaded_bytes,
            "total_bytes": progress.total_bytes,
            "speed_bytes_per_sec": progress.speed_bytes_per_sec,
            "eta_seconds": progress.eta_seconds,
            "current_url": progress.current_url,
            "total_files": total_files,
            "progress_percentage": if progress.total_bytes > 0 {
                (progress.downloaded_bytes as f64 / progress.total_bytes as f64) * 100.0
            } else {
                0.0
            }
        });

        if let Err(e) = window_clone.emit("download_progress", payload) {
            log::error!("Failed to emit progress event: {}", e);
        }
    };

    // 启动增量下载任务
    tokio::spawn(async move {
        match manager.download_incremental_updates(file_updates, progress_callback).await {
            Ok(_) => {
                let completion_payload = json!({
                    "type": "incremental_download_complete",
                    "success": true
                });
                if let Err(e) = window.emit("download_complete", completion_payload) {
                    log::error!("Failed to emit completion event: {}", e);
                }
            }
            Err(e) => {
                let error_payload = json!({
                    "type": "incremental_download_error",
                    "success": false,
                    "error": e
                });
                if let Err(e) = window.emit("download_complete", error_payload) {
                    log::error!("Failed to emit error event: {}", e);
                }
            }
        }
    });

    Ok("Incremental download started".to_string())
}

#[tauri::command]
pub async fn test_direct_download_url(
    url: String,
) -> Result<serde_json::Value, String> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .map_err(|e| e.to_string())?;

    match client.head(&url).send().await {
        Ok(response) => {
            Ok(json!({
                "success": true,
                "status_code": response.status().as_u16(),
                "content_length": response.content_length(),
                "headers": {
                    "content_type": response.headers().get("content-type")
                        .and_then(|v| v.to_str().ok()),
                    "last_modified": response.headers().get("last-modified")
                        .and_then(|v| v.to_str().ok()),
                }
            }))
        }
        Err(e) => {
            Ok(json!({
                "success": false,
                "error": e.to_string()
            }))
        }
    }
}

#[tauri::command]
pub async fn parse_netdisk_direct_link(
    share_link: String,
    card: String,
    netdisk: String,
) -> Result<String, String> {
    let parse_url = format!(
        "https://parse.lxown.com/Netdisk_directlink?Card={}&Netdisk={}&ShareLink={}",
        card, netdisk, share_link
    );

    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .map_err(|e| e.to_string())?;

    let response = client.get(&parse_url).send().await
        .map_err(|e| format!("Request failed: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("HTTP error: {}", response.status()));
    }

    let direct_url = response.text().await
        .map_err(|e| format!("Failed to read response: {}", e))?;

    // 验证返回的是否是有效的下载链接
    if direct_url.starts_with("http") {
        Ok(direct_url)
    } else {
        Err(format!("Invalid direct link returned: {}", direct_url))
    }
}

#[tauri::command]
pub async fn extract_zip_file(
    zip_path: String,
    extract_to: String,
    window: Window,
) -> Result<String, String> {
    use std::fs::File;
    use zip::ZipArchive;

    let file = File::open(&zip_path)
        .map_err(|e| format!("Failed to open ZIP file: {}", e))?;
    
    let mut archive = ZipArchive::new(file)
        .map_err(|e| format!("Failed to read ZIP archive: {}", e))?;

    let total_files = archive.len();
    let extract_path = std::path::Path::new(&extract_to);

    for i in 0..archive.len() {
        let mut file = archive.by_index(i)
            .map_err(|e| format!("Failed to access file {}: {}", i, e))?;
        
        let outpath = match file.enclosed_name() {
            Some(path) => extract_path.join(path),
            None => continue,
        };

        if file.name().ends_with('/') {
            // 创建目录
            std::fs::create_dir_all(&outpath)
                .map_err(|e| format!("Failed to create directory: {}", e))?;
        } else {
            // 提取文件
            if let Some(p) = outpath.parent() {
                if !p.exists() {
                    std::fs::create_dir_all(p)
                        .map_err(|e| format!("Failed to create parent directory: {}", e))?;
                }
            }
            
            let mut outfile = File::create(&outpath)
                .map_err(|e| format!("Failed to create output file: {}", e))?;
            
            std::io::copy(&mut file, &mut outfile)
                .map_err(|e| format!("Failed to extract file: {}", e))?;
        }

        // 发送解压进度
        let progress = ((i + 1) as f64 / total_files as f64) * 100.0;
        let payload = json!({
            "type": "extraction_progress",
            "progress_percentage": progress,
            "current_file": file.name(),
            "files_processed": i + 1,
            "total_files": total_files
        });

        if let Err(e) = window.emit("extraction_progress", payload) {
            log::error!("Failed to emit extraction progress: {}", e);
        }
    }

    Ok("Extraction completed successfully".to_string())
}

// ============================================================================
// 3. 在 main 函数中注册命令
// ============================================================================

/*
在现有的 main.rs 中添加以下内容：

use tauri_integration::*;

fn main() {
    tauri::Builder::default()
        .manage(DownloadState::new())
        .invoke_handler(
            tauri::generate_handler![
                // 现有命令...
                handle_launch_game,
                get_game_status,
                login,
                
                // 新增的下载系统命令
                initialize_download_system,
                fetch_update_info,
                start_full_download,
                start_incremental_download,
                test_direct_download_url,
                parse_netdisk_direct_link,
                extract_zip_file,
            ]
        )
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
*/

// ============================================================================
// 4. 前端集成示例
// ============================================================================

/*
JavaScript 前端代码示例：

// 初始化下载系统
await invoke('initialize_download_system', {
    giteeUrl: 'https://gitee.com/mango_mu/test',
    weiboUrl: 'https://weibo.com/your_url'
});

// 获取更新信息
const updateInfo = await invoke('fetch_update_info');
if (updateInfo.success) {
    console.log('Available version:', updateInfo.version);
    console.log('Download URLs count:', updateInfo.full_download_urls_count);
}

// 开始全量下载
await invoke('start_full_download', {
    outputPath: 'C:\\Games\\Tera\\client.zip'
});

// 监听下载进度
listen('download_progress', (event) => {
    const progress = event.payload;
    console.log(`Progress: ${progress.progress_percentage.toFixed(2)}%`);
    console.log(`Speed: ${formatBytes(progress.speed_bytes_per_sec)}/s`);
    console.log(`ETA: ${progress.eta_seconds}s`);
});

// 监听下载完成
listen('download_complete', (event) => {
    const result = event.payload;
    if (result.success) {
        console.log('Download completed successfully');
        // 开始解压
        invoke('extract_zip_file', {
            zipPath: 'C:\\Games\\Tera\\client.zip',
            extractTo: 'C:\\Games\\Tera\\'
        });
    } else {
        console.error('Download failed:', result.error);
    }
});

// 监听解压进度
listen('extraction_progress', (event) => {
    const progress = event.payload;
    console.log(`Extracting: ${progress.progress_percentage.toFixed(2)}%`);
    console.log(`Current file: ${progress.current_file}`);
});
*/
