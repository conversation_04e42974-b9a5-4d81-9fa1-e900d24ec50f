# Tera Rust Launcher 测试环境启动脚本

Write-Host "🚀 启动 Tera Rust Launcher 测试环境" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User") + ";$env:USERPROFILE\.cargo\bin"

# 检查依赖
Write-Host "🔍 检查依赖..." -ForegroundColor Cyan

$dependencies = @(
    @{ Name = "Node.js"; Command = "node --version" },
    @{ Name = "Rust"; Command = "rustc --version" },
    @{ Name = "Cargo"; Command = "cargo --version" }
)

foreach ($dep in $dependencies) {
    try {
        $version = Invoke-Expression $dep.Command 2>$null
        Write-Host "   ✅ $($dep.Name): $version" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ $($dep.Name): 未安装或不可用" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""

# 启动测试服务器
Write-Host "🌐 启动测试服务器..." -ForegroundColor Cyan
Write-Host "服务器将在 http://localhost:8080 运行" -ForegroundColor Yellow
Write-Host ""

# 检查端口是否被占用
try {
    $connection = Test-NetConnection -ComputerName localhost -Port 8080 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($connection) {
        Write-Host "⚠️  端口 8080 已被占用，尝试停止现有服务..." -ForegroundColor Yellow
        # 尝试找到并停止占用端口的进程
        $process = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
        if ($process) {
            Stop-Process -Id $process -Force -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        }
    }
} catch {
    # 忽略错误，继续启动
}

# 启动服务器（后台运行）
$serverJob = Start-Job -ScriptBlock {
    param($workingDir)
    Set-Location $workingDir
    $env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")
    Set-Location "test-server"
    node server.js
} -ArgumentList (Get-Location).Path

# 等待服务器启动
Write-Host "等待服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# 检查服务器是否启动成功
$maxRetries = 10
$retryCount = 0
$serverReady = $false

while ($retryCount -lt $maxRetries -and -not $serverReady) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            $serverReady = $true
            Write-Host "✅ 测试服务器启动成功！" -ForegroundColor Green
        }
    } catch {
        $retryCount++
        Write-Host "   重试 $retryCount/$maxRetries..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
}

if (-not $serverReady) {
    Write-Host "❌ 测试服务器启动失败！" -ForegroundColor Red
    Stop-Job $serverJob -ErrorAction SilentlyContinue
    Remove-Job $serverJob -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""
Write-Host "📋 测试服务器信息:" -ForegroundColor Cyan
Write-Host "   URL: http://localhost:8080" -ForegroundColor White
Write-Host "   健康检查: http://localhost:8080/health" -ForegroundColor White
Write-Host "   哈希文件: http://localhost:8080/tera/launcher/hash-file.json" -ForegroundColor White
Write-Host "   登录 API: http://localhost:8080/tera/LauncherLoginAction" -ForegroundColor White
Write-Host ""
Write-Host "👤 测试用户:" -ForegroundColor Cyan
Write-Host "   用户名: testuser" -ForegroundColor White
Write-Host "   密码: testpass" -ForegroundColor White
Write-Host "   管理员: admin / admin123" -ForegroundColor White
Write-Host ""

# 运行自动化测试
Write-Host "🧪 运行自动化测试..." -ForegroundColor Cyan
try {
    & ".\test-scripts\test-launcher.ps1"
    $testResult = $LASTEXITCODE
} catch {
    Write-Host "❌ 测试脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    $testResult = 1
}

Write-Host ""

if ($testResult -eq 0) {
    Write-Host "🎉 测试环境准备完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "📱 启动 Tera Launcher:" -ForegroundColor Cyan
    Write-Host "   .\teralaunch\src-tauri\target\release\teralaunch.exe" -ForegroundColor White
    Write-Host ""
    Write-Host "🛑 停止测试环境:" -ForegroundColor Cyan
    Write-Host "   按 Ctrl+C 或关闭此窗口" -ForegroundColor White
    Write-Host ""
    
    # 保持服务器运行
    Write-Host "服务器正在运行中... 按任意键停止服务器并退出。" -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} else {
    Write-Host "⚠️  测试环境启动时遇到问题，请检查上述错误。" -ForegroundColor Yellow
}

# 清理
Write-Host ""
Write-Host "🧹 清理资源..." -ForegroundColor Cyan
Stop-Job $serverJob -ErrorAction SilentlyContinue
Remove-Job $serverJob -ErrorAction SilentlyContinue
Write-Host "✅ 测试环境已停止。" -ForegroundColor Green
