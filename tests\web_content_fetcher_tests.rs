// WebContentFetcher 模块单元测试
// 测试网页内容获取和解密功能

#[cfg(test)]
mod web_content_fetcher_tests {
    use super::*;
    use crate::optimal_download_system::*;
    use mockito::{mock, Matcher};
    use tokio_test;

    // ============================================================================
    // 1. WebContentFetcher 构造函数测试
    // ============================================================================

    #[test]
    fn test_web_content_fetcher_new() {
        let config = UpdateConfig {
            gitee_url: "https://gitee.com/test".to_string(),
            weibo_url: "https://weibo.com/test".to_string(),
            download_timeout_seconds: 30,
            ..Default::default()
        };

        let fetcher = WebContentFetcher::new(config.clone());
        
        // 验证配置正确设置
        assert_eq!(fetcher.config.gitee_url, "https://gitee.com/test");
        assert_eq!(fetcher.config.weibo_url, "https://weibo.com/test");
        assert_eq!(fetcher.config.download_timeout_seconds, 30);
    }

    // ============================================================================
    // 2. 网页内容获取测试
    // ============================================================================

    #[tokio::test]
    async fn test_fetch_from_url_success() {
        let test_content = r#"
            <html>
                <body>
                    <!-- UPDATE_INFO_START -->
                    eyJ2ZXJzaW9uIjoiMS4wLjAifQ==
                    <!-- UPDATE_INFO_END -->
                </body>
            </html>
        "#;

        let _m = mock("GET", "/test")
            .with_status(200)
            .with_header("content-type", "text/html; charset=utf-8")
            .with_body(test_content)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/test", &mockito::server_url()),
            ..Default::default()
        };
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_from_url(&format!("{}/test", &mockito::server_url())).await;
        
        assert!(result.is_ok());
        let content = result.unwrap();
        assert!(content.contains("UPDATE_INFO_START"));
        assert!(content.contains("eyJ2ZXJzaW9uIjoiMS4wLjAifQ=="));
    }

    #[tokio::test]
    async fn test_fetch_from_url_http_error() {
        let _m = mock("GET", "/test")
            .with_status(404)
            .create();

        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_from_url(&format!("{}/test", &mockito::server_url())).await;
        
        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(error.contains("HTTP error: 404"));
    }

    #[tokio::test]
    async fn test_fetch_from_url_network_error() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        // 使用无效的URL测试网络错误
        let result = fetcher.fetch_from_url("http://invalid-domain-12345.com/test").await;
        
        assert!(result.is_err());
        let error = result.unwrap_err();
        assert!(error.contains("Request failed"));
    }

    #[tokio::test]
    async fn test_fetch_from_url_timeout() {
        // 模拟超时响应
        let _m = mock("GET", "/slow")
            .with_status(200)
            .with_body_from_fn(|_| {
                std::thread::sleep(std::time::Duration::from_secs(2));
                "slow response"
            })
            .create();

        let config = UpdateConfig {
            download_timeout_seconds: 1, // 1秒超时
            ..Default::default()
        };
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_from_url(&format!("{}/slow", &mockito::server_url())).await;
        
        assert!(result.is_err());
    }

    // ============================================================================
    // 3. 内容解析测试
    // ============================================================================

    #[test]
    fn test_parse_encrypted_content_success() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let html_content = r#"
            <html>
                <head><title>Test</title></head>
                <body>
                    <div>Some content</div>
                    <!-- UPDATE_INFO_START -->
                    eyJ2ZXJzaW9uIjoiMS4wLjAiLCJkYXRhIjoidGVzdCJ9
                    <!-- UPDATE_INFO_END -->
                    <div>More content</div>
                </body>
            </html>
        "#;

        let result = fetcher.parse_encrypted_content(html_content);
        
        assert!(result.is_ok());
        let encrypted_info = result.unwrap();
        assert_eq!(encrypted_info.encrypted_data, "eyJ2ZXJzaW9uIjoiMS4wLjAiLCJkYXRhIjoidGVzdCJ9");
        assert_eq!(encrypted_info.version, "1.0.0");
    }

    #[test]
    fn test_parse_encrypted_content_missing_start_tag() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let html_content = r#"
            <html>
                <body>
                    <div>Some content</div>
                    <!-- UPDATE_INFO_END -->
                </body>
            </html>
        "#;

        let result = fetcher.parse_encrypted_content(html_content);
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Failed to parse encrypted content"));
    }

    #[test]
    fn test_parse_encrypted_content_missing_end_tag() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let html_content = r#"
            <html>
                <body>
                    <!-- UPDATE_INFO_START -->
                    eyJ2ZXJzaW9uIjoiMS4wLjAifQ==
                </body>
            </html>
        "#;

        let result = fetcher.parse_encrypted_content(html_content);
        
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_encrypted_content_empty_data() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let html_content = r#"
            <!-- UPDATE_INFO_START -->
            <!-- UPDATE_INFO_END -->
        "#;

        let result = fetcher.parse_encrypted_content(html_content);
        
        assert!(result.is_ok());
        let encrypted_info = result.unwrap();
        assert!(encrypted_info.encrypted_data.is_empty());
    }

    // ============================================================================
    // 4. 解密功能测试
    // ============================================================================

    #[test]
    fn test_decrypt_update_info_success() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        // 创建有效的加密数据（Base64编码的JSON）
        let test_data = r#"{"version":"1.0.0","full_download_urls":["http://example.com/file1.zip","http://example.com/file2.zip"],"incremental_server_url":"http://example.com/incremental","file_list_url":"http://example.com/files","checksum":"abc123def456"}"#;
        let encrypted_data = base64::encode(test_data);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data,
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        
        assert!(result.is_ok());
        let decrypted = result.unwrap();
        assert_eq!(decrypted.version, "1.0.0");
        assert_eq!(decrypted.full_download_urls.len(), 2);
        assert_eq!(decrypted.full_download_urls[0], "http://example.com/file1.zip");
        assert_eq!(decrypted.incremental_server_url, "http://example.com/incremental");
        assert_eq!(decrypted.checksum, "abc123def456");
    }

    #[test]
    fn test_decrypt_update_info_invalid_base64() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data: "invalid_base64_data!@#$%".to_string(),
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Failed to decode"));
    }

    #[test]
    fn test_decrypt_update_info_invalid_utf8() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        // 创建无效的UTF-8数据
        let invalid_utf8_bytes = vec![0xFF, 0xFE, 0xFD];
        let encrypted_data = base64::encode(&invalid_utf8_bytes);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data,
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Invalid UTF-8"));
    }

    #[test]
    fn test_decrypt_update_info_invalid_json() {
        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let invalid_json = "{ invalid json data }";
        let encrypted_data = base64::encode(invalid_json);

        let encrypted_info = EncryptedUpdateInfo {
            encrypted_data,
            version: "1.0.0".to_string(),
            timestamp: 1234567890,
        };

        let result = fetcher.decrypt_update_info(&encrypted_info);
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Failed to parse JSON"));
    }

    // ============================================================================
    // 5. 完整获取流程测试
    // ============================================================================

    #[tokio::test]
    async fn test_fetch_encrypted_update_info_gitee_success() {
        let test_data = r#"{"version":"1.0.0","full_download_urls":["http://example.com/file.zip"],"incremental_server_url":"http://example.com","file_list_url":"http://example.com/files","checksum":"abc123"}"#;
        let encrypted_data = base64::encode(test_data);

        let html_response = format!(r#"
            <html>
                <body>
                    <!-- UPDATE_INFO_START -->
                    {}
                    <!-- UPDATE_INFO_END -->
                </body>
            </html>
        "#, encrypted_data);

        let _m = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(&html_response)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            weibo_url: "".to_string(),
            ..Default::default()
        };
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_encrypted_update_info().await;
        
        assert!(result.is_ok());
        let encrypted_info = result.unwrap();
        assert!(!encrypted_info.encrypted_data.is_empty());
    }

    #[tokio::test]
    async fn test_fetch_encrypted_update_info_gitee_fail_weibo_success() {
        // Gitee 失败
        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(500)
            .create();

        // 微博成功
        let test_data = r#"{"version":"1.0.1"}"#;
        let encrypted_data = base64::encode(test_data);
        let html_response = format!(r#"
            <!-- UPDATE_INFO_START -->
            {}
            <!-- UPDATE_INFO_END -->
        "#, encrypted_data);

        let _m2 = mock("GET", "/weibo/test")
            .with_status(200)
            .with_body(&html_response)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            weibo_url: format!("{}/weibo/test", &mockito::server_url()),
            ..Default::default()
        };
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_encrypted_update_info().await;
        
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_fetch_encrypted_update_info_all_sources_fail() {
        // Gitee 失败
        let _m1 = mock("GET", "/mango_mu/test")
            .with_status(404)
            .create();

        // 微博失败
        let _m2 = mock("GET", "/weibo/test")
            .with_status(503)
            .create();

        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            weibo_url: format!("{}/weibo/test", &mockito::server_url()),
            ..Default::default()
        };
        let fetcher = WebContentFetcher::new(config);

        let result = fetcher.fetch_encrypted_update_info().await;
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Failed to fetch update info from all sources"));
    }

    // ============================================================================
    // 6. 边界条件测试
    // ============================================================================

    #[test]
    fn test_encrypted_update_info_creation() {
        let info = EncryptedUpdateInfo {
            encrypted_data: "test_data".to_string(),
            version: "2.0.0".to_string(),
            timestamp: 9876543210,
        };

        assert_eq!(info.encrypted_data, "test_data");
        assert_eq!(info.version, "2.0.0");
        assert_eq!(info.timestamp, 9876543210);
    }

    #[test]
    fn test_decrypted_update_info_creation() {
        let info = DecryptedUpdateInfo {
            version: "1.5.0".to_string(),
            full_download_urls: vec![
                "http://url1.com".to_string(),
                "http://url2.com".to_string(),
            ],
            incremental_server_url: "http://incremental.com".to_string(),
            file_list_url: "http://files.com".to_string(),
            checksum: "hash123".to_string(),
        };

        assert_eq!(info.version, "1.5.0");
        assert_eq!(info.full_download_urls.len(), 2);
        assert_eq!(info.incremental_server_url, "http://incremental.com");
        assert_eq!(info.checksum, "hash123");
    }

    // ============================================================================
    // 7. 性能测试
    // ============================================================================

    #[tokio::test]
    async fn test_concurrent_fetch_requests() {
        use std::time::Instant;

        let test_content = r#"
            <!-- UPDATE_INFO_START -->
            eyJ2ZXJzaW9uIjoiMS4wLjAifQ==
            <!-- UPDATE_INFO_END -->
        "#;

        // 创建多个模拟端点
        let mut mocks = Vec::new();
        for i in 0..5 {
            let mock = mock("GET", &format!("/test{}", i))
                .with_status(200)
                .with_body(test_content)
                .create();
            mocks.push(mock);
        }

        let config = UpdateConfig::default();
        let fetcher = WebContentFetcher::new(config);

        let start_time = Instant::now();
        
        // 并发请求
        let mut tasks = Vec::new();
        for i in 0..5 {
            let url = format!("{}/test{}", &mockito::server_url(), i);
            let task = fetcher.fetch_from_url(&url);
            tasks.push(task);
        }

        let results = futures::future::join_all(tasks).await;
        let elapsed = start_time.elapsed();

        // 验证所有请求都成功
        for result in results {
            assert!(result.is_ok());
        }

        // 性能断言：5个并发请求应该在合理时间内完成
        assert!(elapsed.as_secs() < 5, "Concurrent requests took too long: {:?}", elapsed);
    }
}
