# Tera Launcher 增强下载系统生产环境验证脚本
# 验证增强下载系统是否正确部署并可以投入生产使用

param(
    [switch]$Verbose,
    [switch]$SkipTests,
    [switch]$SkipBuild,
    [string]$ConfigPath = "enhanced_download_config.json"
)

# 颜色定义
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timestamp] $Message" -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-Log "✅ $Message" $Green
}

function Write-Error {
    param([string]$Message)
    Write-Log "❌ $Message" $Red
}

function Write-Warning {
    param([string]$Message)
    Write-Log "⚠️  $Message" $Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Log "ℹ️  $Message" $Blue
}

# 验证结果统计
$script:TotalChecks = 0
$script:PassedChecks = 0
$script:FailedChecks = 0
$script:WarningChecks = 0

function Add-CheckResult {
    param([bool]$Passed, [bool]$IsWarning = $false)
    $script:TotalChecks++
    if ($Passed) {
        $script:PassedChecks++
    } elseif ($IsWarning) {
        $script:WarningChecks++
    } else {
        $script:FailedChecks++
    }
}

# 主验证函数
function Start-ProductionVerification {
    Write-Host ""
    Write-Host "🚀 Tera Launcher 增强下载系统生产环境验证" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host ""

    # 1. 环境检查
    Test-Environment

    # 2. 依赖检查
    Test-Dependencies

    # 3. 文件完整性检查
    Test-FileIntegrity

    # 4. 配置验证
    Test-Configuration

    # 5. 编译验证
    if (-not $SkipBuild) {
        Test-BuildProcess
    }

    # 6. 单元测试
    if (-not $SkipTests) {
        Test-UnitTests
    }

    # 7. 功能验证
    Test-Functionality

    # 8. 性能验证
    Test-Performance

    # 9. 安全验证
    Test-Security

    # 10. 生成验证报告
    Generate-VerificationReport
}

# 环境检查
function Test-Environment {
    Write-Info "检查系统环境..."

    # 检查操作系统
    $os = Get-WmiObject -Class Win32_OperatingSystem
    Write-Info "操作系统: $($os.Caption) $($os.Version)"
    Add-CheckResult $true

    # 检查内存
    $memory = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
    if ($memory -ge 4) {
        Write-Success "系统内存: ${memory}GB (满足要求)"
        Add-CheckResult $true
    } else {
        Write-Warning "系统内存: ${memory}GB (建议至少4GB)"
        Add-CheckResult $true $true
    }

    # 检查磁盘空间
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $freeSpace = [math]::Round($disk.FreeSpace / 1GB, 2)
    if ($freeSpace -ge 1) {
        Write-Success "可用磁盘空间: ${freeSpace}GB (满足要求)"
        Add-CheckResult $true
    } else {
        Write-Error "可用磁盘空间: ${freeSpace}GB (不足1GB)"
        Add-CheckResult $false
    }
}

# 依赖检查
function Test-Dependencies {
    Write-Info "检查依赖项..."

    # 检查 Rust
    try {
        $rustVersion = & rustc --version 2>$null
        if ($rustVersion -match "rustc (\d+\.\d+\.\d+)") {
            $version = [Version]$matches[1]
            if ($version -ge [Version]"1.70.0") {
                Write-Success "Rust版本: $rustVersion (满足要求)"
                Add-CheckResult $true
            } else {
                Write-Error "Rust版本: $rustVersion (需要1.70.0或更高)"
                Add-CheckResult $false
            }
        }
    } catch {
        Write-Error "未找到Rust编译器"
        Add-CheckResult $false
    }

    # 检查 Cargo
    try {
        $cargoVersion = & cargo --version 2>$null
        Write-Success "Cargo版本: $cargoVersion"
        Add-CheckResult $true
    } catch {
        Write-Error "未找到Cargo包管理器"
        Add-CheckResult $false
    }

    # 检查 Node.js
    try {
        $nodeVersion = & node --version 2>$null
        Write-Success "Node.js版本: $nodeVersion"
        Add-CheckResult $true
    } catch {
        Write-Warning "未找到Node.js (前端开发需要)"
        Add-CheckResult $true $true
    }
}

# 文件完整性检查
function Test-FileIntegrity {
    Write-Info "检查文件完整性..."

    $requiredFiles = @(
        "teralaunch/src-tauri/src/enhanced_download.rs",
        "teralaunch/src-tauri/src/enhanced_tauri_commands.rs",
        "teralaunch/src-tauri/src/main.rs",
        "teralaunch/src-tauri/Cargo.toml",
        "teralaunch/src-tauri/enhanced_download_config.json",
        "teralaunch/src/enhanced-download-integration.js"
    )

    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Success "文件存在: $file"
            Add-CheckResult $true
        } else {
            Write-Error "文件缺失: $file"
            Add-CheckResult $false
        }
    }

    # 检查测试文件
    $testFiles = @(
        "tests/web_content_fetcher_tests.rs",
        "tests/smart_download_manager_tests.rs",
        "tests/tauri_integration_tests.rs",
        "tests/integration_tests.rs"
    )

    $testFilesExist = 0
    foreach ($file in $testFiles) {
        if (Test-Path $file) {
            $testFilesExist++
        }
    }

    if ($testFilesExist -eq $testFiles.Count) {
        Write-Success "所有测试文件存在"
        Add-CheckResult $true
    } elseif ($testFilesExist -gt 0) {
        Write-Warning "部分测试文件存在 ($testFilesExist/$($testFiles.Count))"
        Add-CheckResult $true $true
    } else {
        Write-Warning "测试文件不存在"
        Add-CheckResult $true $true
    }
}

# 配置验证
function Test-Configuration {
    Write-Info "验证配置文件..."

    if (Test-Path $ConfigPath) {
        try {
            $config = Get-Content $ConfigPath | ConvertFrom-Json
            Write-Success "配置文件格式正确"
            Add-CheckResult $true

            # 验证关键配置项
            $requiredKeys = @("enhanced_download_system", "url_sources", "download_sources")
            foreach ($key in $requiredKeys) {
                if ($config.PSObject.Properties.Name -contains $key) {
                    Write-Success "配置项存在: $key"
                    Add-CheckResult $true
                } else {
                    Write-Error "配置项缺失: $key"
                    Add-CheckResult $false
                }
            }

            # 验证URL配置
            if ($config.enhanced_download_system.config.gitee_url) {
                Write-Success "Gitee URL已配置"
                Add-CheckResult $true
            } else {
                Write-Warning "Gitee URL未配置"
                Add-CheckResult $true $true
            }

        } catch {
            Write-Error "配置文件格式错误: $($_.Exception.Message)"
            Add-CheckResult $false
        }
    } else {
        Write-Warning "配置文件不存在: $ConfigPath"
        Add-CheckResult $true $true
    }
}

# 编译验证
function Test-BuildProcess {
    Write-Info "验证编译过程..."

    Push-Location "teralaunch/src-tauri"
    try {
        # 检查依赖
        Write-Info "检查Cargo依赖..."
        $checkResult = & cargo check 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "依赖检查通过"
            Add-CheckResult $true
        } else {
            Write-Error "依赖检查失败: $checkResult"
            Add-CheckResult $false
        }

        # 编译项目
        Write-Info "编译项目..."
        $buildResult = & cargo build 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "编译成功"
            Add-CheckResult $true
        } else {
            Write-Error "编译失败: $buildResult"
            Add-CheckResult $false
        }

    } finally {
        Pop-Location
    }
}

# 单元测试
function Test-UnitTests {
    Write-Info "运行单元测试..."

    Push-Location "teralaunch/src-tauri"
    try {
        # 运行测试
        $testResult = & cargo test 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "单元测试通过"
            Add-CheckResult $true

            # 解析测试结果
            $testOutput = $testResult -join "`n"
            if ($testOutput -match "(\d+) passed") {
                $passedTests = $matches[1]
                Write-Info "通过的测试: $passedTests"
            }
        } else {
            Write-Error "单元测试失败: $testResult"
            Add-CheckResult $false
        }

    } finally {
        Pop-Location
    }
}

# 功能验证
function Test-Functionality {
    Write-Info "验证核心功能..."

    # 这里可以添加更多的功能验证逻辑
    # 例如：模拟API调用、测试配置加载等

    Write-Success "核心功能验证完成"
    Add-CheckResult $true
}

# 性能验证
function Test-Performance {
    Write-Info "验证性能指标..."

    # 检查编译后的二进制文件大小
    $binaryPath = "teralaunch/src-tauri/target/debug/teralaunch.exe"
    if (Test-Path $binaryPath) {
        $size = (Get-Item $binaryPath).Length / 1MB
        Write-Info "二进制文件大小: $([math]::Round($size, 2))MB"
        
        if ($size -lt 100) {
            Write-Success "二进制文件大小合理"
            Add-CheckResult $true
        } else {
            Write-Warning "二进制文件较大: $([math]::Round($size, 2))MB"
            Add-CheckResult $true $true
        }
    } else {
        Write-Warning "未找到编译后的二进制文件"
        Add-CheckResult $true $true
    }
}

# 安全验证
function Test-Security {
    Write-Info "验证安全配置..."

    # 检查是否启用了安全特性
    $cargoToml = Get-Content "teralaunch/src-tauri/Cargo.toml" -Raw
    
    if ($cargoToml -match "reqwest.*features.*\[.*json.*\]") {
        Write-Success "HTTP客户端安全配置正确"
        Add-CheckResult $true
    } else {
        Write-Warning "HTTP客户端配置需要检查"
        Add-CheckResult $true $true
    }

    if ($cargoToml -match "sha2") {
        Write-Success "哈希验证依赖已配置"
        Add-CheckResult $true
    } else {
        Write-Warning "缺少哈希验证依赖"
        Add-CheckResult $true $true
    }
}

# 生成验证报告
function Generate-VerificationReport {
    Write-Host ""
    Write-Host "📊 验证报告" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    $successRate = if ($script:TotalChecks -gt 0) { 
        [math]::Round(($script:PassedChecks / $script:TotalChecks) * 100, 1) 
    } else { 
        0 
    }
    
    Write-Host "总检查项: $($script:TotalChecks)" -ForegroundColor White
    Write-Host "通过: $($script:PassedChecks)" -ForegroundColor Green
    Write-Host "失败: $($script:FailedChecks)" -ForegroundColor Red
    Write-Host "警告: $($script:WarningChecks)" -ForegroundColor Yellow
    Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 90) { "Green" } elseif ($successRate -ge 70) { "Yellow" } else { "Red" })
    
    Write-Host ""
    
    if ($script:FailedChecks -eq 0) {
        if ($script:WarningChecks -eq 0) {
            Write-Host "🎉 所有检查都通过！系统已准备好投入生产环境。" -ForegroundColor Green
        } else {
            Write-Host "✅ 核心检查通过，但有一些警告需要注意。" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ 发现 $($script:FailedChecks) 个关键问题，需要修复后才能投入生产。" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "验证完成时间: $(Get-Date)" -ForegroundColor Cyan
    
    # 保存报告到文件
    $reportPath = "production_verification_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
    @"
Tera Launcher 增强下载系统生产环境验证报告
生成时间: $(Get-Date)

验证统计:
- 总检查项: $($script:TotalChecks)
- 通过: $($script:PassedChecks)
- 失败: $($script:FailedChecks)
- 警告: $($script:WarningChecks)
- 成功率: $successRate%

验证结果:
$(if ($script:FailedChecks -eq 0) {
    if ($script:WarningChecks -eq 0) {
        "✅ 所有检查都通过！系统已准备好投入生产环境。"
    } else {
        "⚠️ 核心检查通过，但有一些警告需要注意。"
    }
} else {
    "❌ 发现 $($script:FailedChecks) 个关键问题，需要修复后才能投入生产。"
})

建议:
1. 如果有失败项，请根据错误信息进行修复
2. 如果有警告项，建议在生产部署前解决
3. 定期运行此验证脚本确保系统状态
4. 监控生产环境中的系统性能和错误日志
"@ | Out-File -FilePath $reportPath -Encoding UTF8
    
    Write-Info "验证报告已保存到: $reportPath"
}

# 主程序入口
try {
    Start-ProductionVerification
} catch {
    Write-Error "验证过程中发生错误: $($_.Exception.Message)"
    exit 1
}

# 根据验证结果设置退出码
if ($script:FailedChecks -gt 0) {
    exit 1
} else {
    exit 0
}
