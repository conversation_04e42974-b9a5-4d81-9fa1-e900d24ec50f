[package]
name = "teralaunch"
version = "0.0.0"
description = "A Tauri App"
authors = ["TNC97"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1", features = [] }

[dependencies]
tauri = { version = "1", features = [ "api-all"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
teralib = { path = "../../teralib" }
parking_lot = "0.12.1"
tokio = { version = "1.37.0", features = ["full"] }
tokio-macros = "2.2.0"
log = "0.4.22"
reqwest = { version = "0.12.7", features = ["json", "stream"] }
lazy_static = "1.4.0"
rust-ini = "0.21.0"
sha2 = "0.10.8"
futures-util = "0.3"
indicatif = "0.17.8"
walkdir = "2.5.0"
rayon = "1.10.0"
thiserror = "1.0.63"
env_logger = "0.10.0"
devtools = "0.3.3"
tracing = "0.1"
dotenv = "0.15.0"

# 增强下载系统依赖
async-trait = "0.1.74"
bytes = "1.5.0"
uuid = { version = "1.6.1", features = ["v4"] }
base64 = "0.21"
chrono = { version = "0.4", features = ["serde"] }
zip = "0.6"



[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
custom-menu = []
