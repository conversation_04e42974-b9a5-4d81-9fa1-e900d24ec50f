# Tera Rust Launcher 完整测试脚本
Write-Host "🚀 Tera Rust Launcher 完整测试环境验证" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host ""

$ServerUrl = "http://localhost:8080"
$TestUser = "admin"
$TestPassword = "admin123"

# 函数：测试 API 端点
function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = $null,
        [string]$ContentType = "application/json",
        [string]$ExpectedContent = ""
    )
    
    Write-Host "🔍 测试: $Name" -ForegroundColor Yellow
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
        }
        
        if ($Body) {
            if ($ContentType -eq "application/x-www-form-urlencoded") {
                $bodyString = ($Body.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
                $params.Body = $bodyString
            } else {
                $params.Body = $Body | ConvertTo-Json
            }
            $params.ContentType = $ContentType
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq 200) {
            if ($ExpectedContent -and $response.Content -like "*$ExpectedContent*") {
                Write-Host "   ✅ 成功 - 状态码: $($response.StatusCode), 包含预期内容" -ForegroundColor Green
                return $true
            } elseif (!$ExpectedContent) {
                Write-Host "   ✅ 成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
                return $true
            } else {
                Write-Host "   ⚠️  警告 - 状态码正确但内容不匹配" -ForegroundColor Yellow
                return $false
            }
        } else {
            Write-Host "   ❌ 失败 - 状态码: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ❌ 错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. 检查服务器是否运行
Write-Host "📡 检查测试服务器状态..." -ForegroundColor Cyan
$serverRunning = Test-Endpoint -Name "服务器健康检查" -Url "$ServerUrl/health" -ExpectedContent "ok"

if (!$serverRunning) {
    Write-Host ""
    Write-Host "❌ 测试服务器未运行！请先启动服务器：" -ForegroundColor Red
    Write-Host "   cd test-server" -ForegroundColor White
    Write-Host "   node server.js" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host ""

# 2. 测试所有 API 端点
Write-Host "🧪 测试 API 端点..." -ForegroundColor Cyan

$apiTests = @(
    @{ Name = "哈希文件获取"; Url = "$ServerUrl/tera/launcher/hash-file.json"; Expected = "files" },
    @{ Name = "服务器列表获取"; Url = "$ServerUrl/tera/ServerList.json"; Expected = "servers" }
)

$apiSuccess = $true
foreach ($test in $apiTests) {
    $result = Test-Endpoint -Name $test.Name -Url $test.Url -ExpectedContent $test.Expected
    if (!$result) { $apiSuccess = $false }
}

# 3. 测试登录 API
Write-Host ""
Write-Host "🔐 测试登录功能..." -ForegroundColor Cyan

$loginBody = @{
    login = $TestUser
    password = $TestPassword
}

$loginSuccess = Test-Endpoint -Name "用户登录 (正确凭据)" -Url "$ServerUrl/tera/LauncherLoginAction" -Method "POST" -Body $loginBody -ContentType "application/x-www-form-urlencoded" -ExpectedContent "success"

# 4. 检查文件系统
Write-Host ""
Write-Host "📁 检查文件系统..." -ForegroundColor Cyan

$fileChecks = @(
    @{ Path = "teralaunch\src-tauri\target\release\teralaunch.exe"; Name = "启动器可执行文件" },
    @{ Path = "teralaunch\src-tauri\target\release\tera_config.ini"; Name = "配置文件 (可执行文件目录)" },
    @{ Path = "C:\tera"; Name = "游戏目录" },
    @{ Path = "C:\tera\Binaries\Tera.exe"; Name = "游戏可执行文件" },
    @{ Path = "test-server\server.js"; Name = "测试服务器" }
)

$fileSuccess = $true
foreach ($check in $fileChecks) {
    if (Test-Path $check.Path) {
        Write-Host "   ✅ $($check.Name): 存在" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $($check.Name): 不存在 - $($check.Path)" -ForegroundColor Red
        $fileSuccess = $false
    }
}

# 5. 检查配置文件内容
Write-Host ""
Write-Host "⚙️  检查配置..." -ForegroundColor Cyan

try {
    $configPath = "teralaunch\src-tauri\target\release\tera_config.ini"
    if (Test-Path $configPath) {
        $configContent = Get-Content $configPath -Raw
        if ($configContent -like "*path=C:\\tera*") {
            Write-Host "   ✅ 配置文件路径正确" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 配置文件路径不正确" -ForegroundColor Red
            $fileSuccess = $false
        }
        
        if ($configContent -like "*lang=EUR*") {
            Write-Host "   ✅ 配置文件语言设置正确" -ForegroundColor Green
        } else {
            Write-Host "   ❌ 配置文件语言设置不正确" -ForegroundColor Red
            $fileSuccess = $false
        }
    }
} catch {
    Write-Host "   ❌ 无法读取配置文件: $($_.Exception.Message)" -ForegroundColor Red
    $fileSuccess = $false
}

# 6. 总结
Write-Host ""
Write-Host "📊 测试结果总结" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

if ($serverRunning) {
    Write-Host "✅ 测试服务器: 运行正常" -ForegroundColor Green
} else {
    Write-Host "❌ 测试服务器: 未运行" -ForegroundColor Red
}

if ($apiSuccess) {
    Write-Host "✅ API 端点: 全部正常" -ForegroundColor Green
} else {
    Write-Host "❌ API 端点: 部分失败" -ForegroundColor Red
}

if ($loginSuccess) {
    Write-Host "✅ 登录功能: 正常" -ForegroundColor Green
} else {
    Write-Host "❌ 登录功能: 失败" -ForegroundColor Red
}

if ($fileSuccess) {
    Write-Host "✅ 文件系统: 配置正确" -ForegroundColor Green
} else {
    Write-Host "❌ 文件系统: 配置不完整" -ForegroundColor Red
}

Write-Host ""

if ($serverRunning -and $apiSuccess -and $loginSuccess -and $fileSuccess) {
    Write-Host "🎉 所有测试通过！环境配置完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 现在可以启动启动器进行测试：" -ForegroundColor Cyan
    Write-Host "   .\teralaunch\src-tauri\target\release\teralaunch.exe" -ForegroundColor White
    Write-Host ""
    Write-Host "🔐 测试登录凭据：" -ForegroundColor Cyan
    Write-Host "   用户名: admin" -ForegroundColor White
    Write-Host "   密码: admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "   或者:" -ForegroundColor White
    Write-Host "   用户名: testuser" -ForegroundColor White
    Write-Host "   密码: testpass" -ForegroundColor White
} else {
    Write-Host "⚠️  部分测试失败，请检查上述错误并修复" -ForegroundColor Yellow
    exit 1
}
