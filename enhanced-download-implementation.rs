// 增强下载系统实现 - 集成第三方下载库
// 这个文件展示了如何在现有架构基础上集成多种下载引擎

use serde::{Deserialize, Serialize};
use std::path::Path;
use std::sync::Arc;
use tokio::sync::{Mutex, Semaphore};
use futures_util::StreamExt;

// ============================================================================
// 1. 下载引擎抽象层
// ============================================================================

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DownloadEngine {
    Reqwest,    // 默认引擎，基于现有代码
    Aria2,      // 高性能多线程下载
    Curl,       // libcurl 集成
}

#[derive(Debug, Clone)]
pub struct DownloadConfig {
    pub engine: DownloadEngine,
    pub max_concurrent: usize,
    pub max_connections_per_file: usize,
    pub chunk_size: usize,
    pub enable_resume: bool,
    pub enable_chunked_download: bool,
    pub speed_limit: Option<u64>, // bytes/sec
    pub timeout_seconds: u64,
    pub retry_count: u32,
}

impl Default for DownloadConfig {
    fn default() -> Self {
        Self {
            engine: DownloadEngine::Reqwest,
            max_concurrent: 4,
            max_connections_per_file: 8,
            chunk_size: 1024 * 1024, // 1MB
            enable_resume: true,
            enable_chunked_download: true,
            speed_limit: None,
            timeout_seconds: 300,
            retry_count: 3,
        }
    }
}

#[async_trait::async_trait]
pub trait DownloadEngine: Send + Sync {
    async fn download_file(
        &self,
        url: &str,
        output_path: &Path,
        progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>,
    ) -> Result<DownloadResult, DownloadError>;

    async fn download_multiple(
        &self,
        downloads: Vec<DownloadTask>,
        global_progress_callback: Box<dyn Fn(GlobalProgress) + Send + Sync>,
    ) -> Result<Vec<DownloadResult>, DownloadError>;

    fn get_engine_info(&self) -> EngineInfo;
}

#[derive(Debug, Clone)]
pub struct DownloadTask {
    pub id: String,
    pub url: String,
    pub output_path: std::path::PathBuf,
    pub expected_hash: Option<String>,
    pub expected_size: Option<u64>,
    pub priority: u32,
}

#[derive(Debug, Clone)]
pub struct DownloadProgress {
    pub task_id: String,
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub speed_bytes_per_sec: u64,
    pub eta_seconds: Option<u64>,
    pub status: DownloadStatus,
}

#[derive(Debug, Clone)]
pub struct GlobalProgress {
    pub completed_tasks: usize,
    pub total_tasks: usize,
    pub total_downloaded_bytes: u64,
    pub total_bytes: u64,
    pub overall_speed: u64,
    pub eta_seconds: Option<u64>,
}

#[derive(Debug, Clone)]
pub enum DownloadStatus {
    Waiting,
    Downloading,
    Completed,
    Failed(String),
    Paused,
}

#[derive(Debug)]
pub struct DownloadResult {
    pub task_id: String,
    pub success: bool,
    pub downloaded_bytes: u64,
    pub duration_seconds: f64,
    pub average_speed: u64,
    pub error: Option<String>,
}

#[derive(Debug)]
pub struct EngineInfo {
    pub name: String,
    pub version: String,
    pub features: Vec<String>,
    pub max_concurrent_supported: usize,
}

#[derive(Debug, thiserror::Error)]
pub enum DownloadError {
    #[error("Network error: {0}")]
    Network(String),
    #[error("File system error: {0}")]
    FileSystem(String),
    #[error("Engine error: {0}")]
    Engine(String),
    #[error("Validation error: {0}")]
    Validation(String),
}

// ============================================================================
// 2. 增强的 Reqwest 引擎实现
// ============================================================================

pub struct EnhancedReqwestEngine {
    client: reqwest::Client,
    config: DownloadConfig,
    semaphore: Arc<Semaphore>,
}

impl EnhancedReqwestEngine {
    pub fn new(config: DownloadConfig) -> Result<Self, DownloadError> {
        let client = reqwest::Client::builder()
            .pool_max_idle_per_host(config.max_concurrent)
            .pool_idle_timeout(std::time::Duration::from_secs(30))
            .timeout(std::time::Duration::from_secs(config.timeout_seconds))
            .tcp_keepalive(std::time::Duration::from_secs(60))
            .user_agent("Tera-Launcher/1.0")
            .build()
            .map_err(|e| DownloadError::Engine(e.to_string()))?;

        let semaphore = Arc::new(Semaphore::new(config.max_concurrent));

        Ok(Self {
            client,
            config,
            semaphore,
        })
    }

    async fn download_with_resume(
        &self,
        task: &DownloadTask,
        progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>,
    ) -> Result<DownloadResult, DownloadError> {
        let start_time = std::time::Instant::now();
        let _permit = self.semaphore.acquire().await
            .map_err(|e| DownloadError::Engine(e.to_string()))?;

        // 检查已存在的文件大小（断点续传）
        let existing_size = if task.output_path.exists() {
            std::fs::metadata(&task.output_path)
                .map(|m| m.len())
                .unwrap_or(0)
        } else {
            0
        };

        // 创建目录
        if let Some(parent) = task.output_path.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
        }

        let mut request = self.client.get(&task.url);
        
        // 断点续传
        if existing_size > 0 && self.config.enable_resume {
            request = request.header("Range", format!("bytes={}-", existing_size));
        }

        let response = request.send().await
            .map_err(|e| DownloadError::Network(e.to_string()))?;

        if !response.status().is_success() && response.status().as_u16() != 206 {
            return Err(DownloadError::Network(format!("HTTP {}", response.status())));
        }

        let total_size = response.content_length().unwrap_or(0) + existing_size;
        
        // 验证预期大小
        if let Some(expected_size) = task.expected_size {
            if total_size != expected_size {
                return Err(DownloadError::Validation(
                    format!("Size mismatch: expected {}, got {}", expected_size, total_size)
                ));
            }
        }

        // 打开文件进行写入
        let mut file = if existing_size > 0 && self.config.enable_resume {
            tokio::fs::OpenOptions::new()
                .append(true)
                .open(&task.output_path)
                .await
        } else {
            tokio::fs::File::create(&task.output_path).await
        }.map_err(|e| DownloadError::FileSystem(e.to_string()))?;

        let mut downloaded = existing_size;
        let mut stream = response.bytes_stream();
        let mut last_progress_time = std::time::Instant::now();
        let mut last_downloaded = downloaded;

        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result
                .map_err(|e| DownloadError::Network(e.to_string()))?;
            
            tokio::io::AsyncWriteExt::write_all(&mut file, &chunk).await
                .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
            
            downloaded += chunk.len() as u64;

            // 更新进度（每100ms或每1MB）
            let now = std::time::Instant::now();
            if now.duration_since(last_progress_time) >= std::time::Duration::from_millis(100) 
                || downloaded - last_downloaded >= 1024 * 1024 {
                
                let elapsed = now.duration_since(last_progress_time).as_secs_f64();
                let speed = if elapsed > 0.0 {
                    ((downloaded - last_downloaded) as f64 / elapsed) as u64
                } else {
                    0
                };

                let eta = if speed > 0 && total_size > downloaded {
                    Some((total_size - downloaded) / speed)
                } else {
                    None
                };

                progress_callback(DownloadProgress {
                    task_id: task.id.clone(),
                    downloaded_bytes: downloaded,
                    total_bytes: total_size,
                    speed_bytes_per_sec: speed,
                    eta_seconds: eta,
                    status: DownloadStatus::Downloading,
                });

                last_progress_time = now;
                last_downloaded = downloaded;
            }

            // 速度限制
            if let Some(speed_limit) = self.config.speed_limit {
                let elapsed = start_time.elapsed().as_secs_f64();
                let expected_bytes = (speed_limit as f64 * elapsed) as u64;
                if downloaded > expected_bytes {
                    let delay = ((downloaded - expected_bytes) as f64 / speed_limit as f64 * 1000.0) as u64;
                    tokio::time::sleep(std::time::Duration::from_millis(delay)).await;
                }
            }

            // 让出 CPU 时间
            tokio::task::yield_now().await;
        }

        tokio::io::AsyncWriteExt::flush(&mut file).await
            .map_err(|e| DownloadError::FileSystem(e.to_string()))?;

        // 验证文件哈希
        if let Some(expected_hash) = &task.expected_hash {
            let actual_hash = self.calculate_file_hash(&task.output_path).await?;
            if actual_hash != *expected_hash {
                return Err(DownloadError::Validation(
                    format!("Hash mismatch: expected {}, got {}", expected_hash, actual_hash)
                ));
            }
        }

        let duration = start_time.elapsed().as_secs_f64();
        let average_speed = if duration > 0.0 {
            (downloaded as f64 / duration) as u64
        } else {
            0
        };

        Ok(DownloadResult {
            task_id: task.id.clone(),
            success: true,
            downloaded_bytes: downloaded,
            duration_seconds: duration,
            average_speed,
            error: None,
        })
    }

    async fn download_with_chunks(
        &self,
        task: &DownloadTask,
        progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>,
    ) -> Result<DownloadResult, DownloadError> {
        // 首先获取文件大小
        let head_response = self.client.head(&task.url).send().await
            .map_err(|e| DownloadError::Network(e.to_string()))?;
        
        let total_size = head_response.content_length()
            .ok_or_else(|| DownloadError::Network("Cannot determine file size".to_string()))?;

        // 小文件不分片
        if total_size < self.config.chunk_size as u64 * 2 {
            return self.download_with_resume(task, progress_callback).await;
        }

        let chunk_count = std::cmp::min(
            self.config.max_connections_per_file,
            (total_size / self.config.chunk_size as u64) as usize + 1
        );

        let chunk_size = total_size / chunk_count as u64;
        let mut chunk_tasks = Vec::new();
        let progress_callback = Arc::new(Mutex::new(progress_callback));

        // 创建分片下载任务
        for i in 0..chunk_count {
            let start = i as u64 * chunk_size;
            let end = if i == chunk_count - 1 {
                total_size - 1
            } else {
                (i as u64 + 1) * chunk_size - 1
            };

            let client = self.client.clone();
            let url = task.url.clone();
            let temp_path = task.output_path.with_extension(format!("part{}", i));
            let task_id = format!("{}.chunk{}", task.id, i);

            let chunk_task = tokio::spawn(async move {
                let response = client
                    .get(&url)
                    .header("Range", format!("bytes={}-{}", start, end))
                    .send()
                    .await?;

                if response.status().as_u16() != 206 {
                    return Err(DownloadError::Network(format!("Chunk download failed: {}", response.status())));
                }

                let mut file = tokio::fs::File::create(&temp_path).await
                    .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
                
                let mut stream = response.bytes_stream();
                let mut downloaded = 0u64;

                while let Some(chunk_result) = stream.next().await {
                    let chunk = chunk_result
                        .map_err(|e| DownloadError::Network(e.to_string()))?;
                    
                    tokio::io::AsyncWriteExt::write_all(&mut file, &chunk).await
                        .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
                    
                    downloaded += chunk.len() as u64;
                }

                tokio::io::AsyncWriteExt::flush(&mut file).await
                    .map_err(|e| DownloadError::FileSystem(e.to_string()))?;

                Ok::<(String, u64), DownloadError>((temp_path.to_string_lossy().to_string(), downloaded))
            });

            chunk_tasks.push(chunk_task);
        }

        // 等待所有分片完成
        let chunk_results: Result<Vec<_>, _> = futures_util::future::try_join_all(chunk_tasks).await
            .map_err(|e| DownloadError::Engine(e.to_string()))?
            .into_iter()
            .collect();

        let chunk_files = chunk_results?;

        // 合并分片
        self.merge_chunks(&chunk_files, &task.output_path).await?;

        // 清理临时文件
        for (temp_file, _) in &chunk_files {
            let _ = tokio::fs::remove_file(temp_file).await;
        }

        let total_downloaded: u64 = chunk_files.iter().map(|(_, size)| *size).sum();

        Ok(DownloadResult {
            task_id: task.id.clone(),
            success: true,
            downloaded_bytes: total_downloaded,
            duration_seconds: 0.0, // TODO: 计算实际时间
            average_speed: 0,       // TODO: 计算平均速度
            error: None,
        })
    }

    async fn merge_chunks(
        &self,
        chunk_files: &[(String, u64)],
        output_path: &Path,
    ) -> Result<(), DownloadError> {
        let mut output_file = tokio::fs::File::create(output_path).await
            .map_err(|e| DownloadError::FileSystem(e.to_string()))?;

        for (chunk_file, _) in chunk_files {
            let mut input_file = tokio::fs::File::open(chunk_file).await
                .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
            
            tokio::io::copy(&mut input_file, &mut output_file).await
                .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
        }

        tokio::io::AsyncWriteExt::flush(&mut output_file).await
            .map_err(|e| DownloadError::FileSystem(e.to_string()))?;

        Ok(())
    }

    async fn calculate_file_hash(&self, path: &Path) -> Result<String, DownloadError> {
        use sha2::{Sha256, Digest};
        
        let mut file = tokio::fs::File::open(path).await
            .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
        
        let mut hasher = Sha256::new();
        let mut buffer = vec![0u8; 8192];
        
        loop {
            let bytes_read = tokio::io::AsyncReadExt::read(&mut file, &mut buffer).await
                .map_err(|e| DownloadError::FileSystem(e.to_string()))?;
            
            if bytes_read == 0 {
                break;
            }
            
            hasher.update(&buffer[..bytes_read]);
        }
        
        Ok(format!("{:x}", hasher.finalize()))
    }
}

#[async_trait::async_trait]
impl DownloadEngine for EnhancedReqwestEngine {
    async fn download_file(
        &self,
        url: &str,
        output_path: &Path,
        progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>,
    ) -> Result<DownloadResult, DownloadError> {
        let task = DownloadTask {
            id: "single".to_string(),
            url: url.to_string(),
            output_path: output_path.to_path_buf(),
            expected_hash: None,
            expected_size: None,
            priority: 0,
        };

        if self.config.enable_chunked_download {
            self.download_with_chunks(&task, progress_callback).await
        } else {
            self.download_with_resume(&task, progress_callback).await
        }
    }

    async fn download_multiple(
        &self,
        downloads: Vec<DownloadTask>,
        global_progress_callback: Box<dyn Fn(GlobalProgress) + Send + Sync>,
    ) -> Result<Vec<DownloadResult>, DownloadError> {
        let total_tasks = downloads.len();
        let completed_tasks = Arc::new(std::sync::atomic::AtomicUsize::new(0));
        let total_downloaded = Arc::new(std::sync::atomic::AtomicU64::new(0));
        let global_callback = Arc::new(global_progress_callback);

        let mut tasks = Vec::new();

        for download in downloads {
            let completed_tasks = completed_tasks.clone();
            let total_downloaded = total_downloaded.clone();
            let global_callback = global_callback.clone();
            let engine = self.clone(); // 需要实现 Clone

            let task = tokio::spawn(async move {
                let progress_callback = Box::new(move |progress: DownloadProgress| {
                    // 更新全局进度
                    let completed = completed_tasks.load(std::sync::atomic::Ordering::Relaxed);
                    let downloaded = total_downloaded.load(std::sync::atomic::Ordering::Relaxed);
                    
                    global_callback(GlobalProgress {
                        completed_tasks: completed,
                        total_tasks,
                        total_downloaded_bytes: downloaded + progress.downloaded_bytes,
                        total_bytes: 0, // TODO: 计算总大小
                        overall_speed: progress.speed_bytes_per_sec,
                        eta_seconds: progress.eta_seconds,
                    });
                });

                let result = if engine.config.enable_chunked_download {
                    engine.download_with_chunks(&download, progress_callback).await
                } else {
                    engine.download_with_resume(&download, progress_callback).await
                };

                if result.is_ok() {
                    completed_tasks.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                }

                result
            });

            tasks.push(task);
        }

        let results: Result<Vec<_>, _> = futures_util::future::try_join_all(tasks).await
            .map_err(|e| DownloadError::Engine(e.to_string()))?
            .into_iter()
            .collect();

        results
    }

    fn get_engine_info(&self) -> EngineInfo {
        EngineInfo {
            name: "Enhanced Reqwest".to_string(),
            version: "1.0.0".to_string(),
            features: vec![
                "Resume Support".to_string(),
                "Chunked Download".to_string(),
                "Speed Limiting".to_string(),
                "Hash Verification".to_string(),
            ],
            max_concurrent_supported: 32,
        }
    }
}

// 为了支持 clone，需要实现 Clone trait
impl Clone for EnhancedReqwestEngine {
    fn clone(&self) -> Self {
        Self {
            client: self.client.clone(),
            config: self.config.clone(),
            semaphore: self.semaphore.clone(),
        }
    }
}

// ============================================================================
// 3. Aria2 引擎实现
// ============================================================================

pub struct Aria2Engine {
    rpc_url: String,
    secret: String,
    process: Arc<Mutex<Option<std::process::Child>>>,
    config: DownloadConfig,
    client: reqwest::Client,
}

impl Aria2Engine {
    pub async fn new(config: DownloadConfig) -> Result<Self, DownloadError> {
        let secret = "tera-launcher-secret".to_string();
        let rpc_url = "http://localhost:6800/jsonrpc".to_string();

        // 启动 aria2c 进程
        let mut child = std::process::Command::new("aria2c")
            .args(&[
                "--enable-rpc",
                "--rpc-listen-all=false",
                "--rpc-listen-port=6800",
                &format!("--rpc-secret={}", secret),
                &format!("--max-connection-per-server={}", config.max_connections_per_file),
                &format!("--split={}", config.max_connections_per_file),
                "--min-split-size=1M",
                &format!("--max-concurrent-downloads={}", config.max_concurrent),
                "--continue=true",
                "--auto-file-renaming=false",
                "--allow-overwrite=true",
                "--check-integrity=true",
            ])
            .stdout(std::process::Stdio::null())
            .stderr(std::process::Stdio::null())
            .spawn()
            .map_err(|e| DownloadError::Engine(format!("Failed to start aria2c: {}", e)))?;

        // 等待 RPC 服务启动
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

        let client = reqwest::Client::new();

        Ok(Self {
            rpc_url,
            secret,
            process: Arc::new(Mutex::new(Some(child))),
            config,
            client,
        })
    }

    async fn call_rpc(&self, method: &str, params: serde_json::Value) -> Result<serde_json::Value, DownloadError> {
        let request_body = serde_json::json!({
            "jsonrpc": "2.0",
            "id": "tera-launcher",
            "method": method,
            "params": params
        });

        let response = self.client
            .post(&self.rpc_url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| DownloadError::Network(e.to_string()))?;

        let result: serde_json::Value = response.json().await
            .map_err(|e| DownloadError::Network(e.to_string()))?;

        if let Some(error) = result.get("error") {
            return Err(DownloadError::Engine(format!("Aria2 RPC error: {}", error)));
        }

        Ok(result["result"].clone())
    }

    async fn add_download(&self, task: &DownloadTask) -> Result<String, DownloadError> {
        let mut options = serde_json::Map::new();

        // 设置输出目录和文件名
        if let Some(parent) = task.output_path.parent() {
            options.insert("dir".to_string(), serde_json::Value::String(
                parent.to_string_lossy().to_string()
            ));
        }

        if let Some(filename) = task.output_path.file_name() {
            options.insert("out".to_string(), serde_json::Value::String(
                filename.to_string_lossy().to_string()
            ));
        }

        // 设置下载选项
        options.insert("max-connection-per-server".to_string(),
            serde_json::Value::String(self.config.max_connections_per_file.to_string()));
        options.insert("split".to_string(),
            serde_json::Value::String(self.config.max_connections_per_file.to_string()));

        if let Some(speed_limit) = self.config.speed_limit {
            options.insert("max-download-limit".to_string(),
                serde_json::Value::String(speed_limit.to_string()));
        }

        // 设置校验和
        if let Some(hash) = &task.expected_hash {
            options.insert("checksum".to_string(),
                serde_json::Value::String(format!("sha-256={}", hash)));
        }

        let params = serde_json::json!([
            format!("token:{}", self.secret),
            [&task.url],
            options
        ]);

        let result = self.call_rpc("aria2.addUri", params).await?;

        result.as_str()
            .ok_or_else(|| DownloadError::Engine("Invalid GID returned".to_string()))
            .map(|s| s.to_string())
    }

    async fn get_download_status(&self, gid: &str) -> Result<Aria2Status, DownloadError> {
        let params = serde_json::json!([
            format!("token:{}", self.secret),
            gid,
            ["status", "totalLength", "completedLength", "downloadSpeed", "errorMessage", "files"]
        ]);

        let result = self.call_rpc("aria2.tellStatus", params).await?;

        Ok(Aria2Status {
            status: result["status"].as_str().unwrap_or("unknown").to_string(),
            total_length: result["totalLength"].as_str().unwrap_or("0").parse().unwrap_or(0),
            completed_length: result["completedLength"].as_str().unwrap_or("0").parse().unwrap_or(0),
            download_speed: result["downloadSpeed"].as_str().unwrap_or("0").parse().unwrap_or(0),
            error_message: result["errorMessage"].as_str().map(|s| s.to_string()),
        })
    }

    async fn wait_for_completion(&self, gid: &str, progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>) -> Result<DownloadResult, DownloadError> {
        let start_time = std::time::Instant::now();
        let mut last_completed = 0u64;
        let mut last_time = start_time;

        loop {
            let status = self.get_download_status(gid).await?;

            match status.status.as_str() {
                "complete" => {
                    return Ok(DownloadResult {
                        task_id: gid.to_string(),
                        success: true,
                        downloaded_bytes: status.total_length,
                        duration_seconds: start_time.elapsed().as_secs_f64(),
                        average_speed: if start_time.elapsed().as_secs() > 0 {
                            status.total_length / start_time.elapsed().as_secs()
                        } else {
                            0
                        },
                        error: None,
                    });
                },
                "error" => {
                    return Ok(DownloadResult {
                        task_id: gid.to_string(),
                        success: false,
                        downloaded_bytes: status.completed_length,
                        duration_seconds: start_time.elapsed().as_secs_f64(),
                        average_speed: 0,
                        error: status.error_message,
                    });
                },
                "active" | "waiting" | "paused" => {
                    // 计算 ETA
                    let now = std::time::Instant::now();
                    let time_diff = now.duration_since(last_time).as_secs_f64();
                    let bytes_diff = status.completed_length.saturating_sub(last_completed);

                    let current_speed = if time_diff > 0.0 {
                        (bytes_diff as f64 / time_diff) as u64
                    } else {
                        status.download_speed
                    };

                    let eta = if current_speed > 0 && status.total_length > status.completed_length {
                        Some((status.total_length - status.completed_length) / current_speed)
                    } else {
                        None
                    };

                    progress_callback(DownloadProgress {
                        task_id: gid.to_string(),
                        downloaded_bytes: status.completed_length,
                        total_bytes: status.total_length,
                        speed_bytes_per_sec: current_speed,
                        eta_seconds: eta,
                        status: match status.status.as_str() {
                            "active" => DownloadStatus::Downloading,
                            "waiting" => DownloadStatus::Waiting,
                            "paused" => DownloadStatus::Paused,
                            _ => DownloadStatus::Downloading,
                        },
                    });

                    last_completed = status.completed_length;
                    last_time = now;
                },
                _ => {
                    return Err(DownloadError::Engine(format!("Unknown status: {}", status.status)));
                }
            }

            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
        }
    }
}

#[derive(Debug)]
struct Aria2Status {
    status: String,
    total_length: u64,
    completed_length: u64,
    download_speed: u64,
    error_message: Option<String>,
}

#[async_trait::async_trait]
impl DownloadEngine for Aria2Engine {
    async fn download_file(
        &self,
        url: &str,
        output_path: &Path,
        progress_callback: Box<dyn Fn(DownloadProgress) + Send + Sync>,
    ) -> Result<DownloadResult, DownloadError> {
        let task = DownloadTask {
            id: "single".to_string(),
            url: url.to_string(),
            output_path: output_path.to_path_buf(),
            expected_hash: None,
            expected_size: None,
            priority: 0,
        };

        let gid = self.add_download(&task).await?;
        self.wait_for_completion(&gid, progress_callback).await
    }

    async fn download_multiple(
        &self,
        downloads: Vec<DownloadTask>,
        global_progress_callback: Box<dyn Fn(GlobalProgress) + Send + Sync>,
    ) -> Result<Vec<DownloadResult>, DownloadError> {
        let mut gids = Vec::new();

        // 添加所有下载任务
        for download in &downloads {
            let gid = self.add_download(download).await?;
            gids.push((gid, download.id.clone()));
        }

        let mut results = Vec::new();
        let total_tasks = downloads.len();
        let mut completed_tasks = 0;

        // 等待所有下载完成
        for (gid, task_id) in gids {
            let progress_callback = Box::new(|_progress: DownloadProgress| {
                // 这里可以聚合进度信息
            });

            let result = self.wait_for_completion(&gid, progress_callback).await?;

            if result.success {
                completed_tasks += 1;
            }

            global_progress_callback(GlobalProgress {
                completed_tasks,
                total_tasks,
                total_downloaded_bytes: 0, // TODO: 聚合已下载字节数
                total_bytes: 0,            // TODO: 聚合总字节数
                overall_speed: 0,          // TODO: 计算总体速度
                eta_seconds: None,         // TODO: 计算剩余时间
            });

            results.push(result);
        }

        Ok(results)
    }

    fn get_engine_info(&self) -> EngineInfo {
        EngineInfo {
            name: "Aria2".to_string(),
            version: "1.36.0".to_string(), // 需要动态获取
            features: vec![
                "Multi-connection Download".to_string(),
                "Resume Support".to_string(),
                "BitTorrent Support".to_string(),
                "Metalink Support".to_string(),
                "RPC Interface".to_string(),
            ],
            max_concurrent_supported: 256,
        }
    }
}

impl Drop for Aria2Engine {
    fn drop(&mut self) {
        // 清理 aria2c 进程
        if let Ok(mut process_guard) = self.process.try_lock() {
            if let Some(mut child) = process_guard.take() {
                let _ = child.kill();
                let _ = child.wait();
            }
        }
    }
}
