#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tera Launcher 增强下载系统代码验证脚本
验证Rust代码的语法正确性和集成完整性
"""

import os
import re
import json
from pathlib import Path

def print_header(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def print_info(message):
    print(f"ℹ️  {message}")

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print_success(f"{description} 存在: {file_path}")
        return True
    else:
        print_error(f"{description} 缺失: {file_path}")
        return False

def check_rust_syntax(file_path):
    """检查Rust文件的基本语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查基本语法元素
        checks = [
            (r'use\s+\w+', "use语句"),
            (r'pub\s+(struct|fn|enum)', "公共声明"),
            (r'impl\s+\w+', "实现块"),
            (r'async\s+fn', "异步函数"),
            (r'#\[derive\(', "派生宏"),
        ]
        
        issues = []
        for pattern, description in checks:
            if re.search(pattern, content):
                print_success(f"  {description} ✓")
            else:
                print_warning(f"  {description} 未找到")
        
        # 检查常见错误
        if content.count('{') != content.count('}'):
            issues.append("大括号不匹配")
        
        if content.count('(') != content.count(')'):
            issues.append("小括号不匹配")
        
        if issues:
            for issue in issues:
                print_error(f"  语法问题: {issue}")
            return False
        else:
            print_success(f"  基本语法检查通过")
            return True
            
    except Exception as e:
        print_error(f"  读取文件失败: {e}")
        return False

def check_cargo_toml(file_path):
    """检查Cargo.toml配置"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_deps = [
            'async-trait',
            'bytes',
            'uuid',
            'base64',
            'chrono',
            'zip',
            'tokio',
            'reqwest',
            'serde',
            'sha2'
        ]
        
        missing_deps = []
        for dep in required_deps:
            if dep in content:
                print_success(f"  依赖 {dep} ✓")
            else:
                missing_deps.append(dep)
                print_error(f"  依赖 {dep} 缺失")
        
        return len(missing_deps) == 0
        
    except Exception as e:
        print_error(f"  读取Cargo.toml失败: {e}")
        return False

def check_module_integration(main_rs_path):
    """检查模块集成"""
    try:
        with open(main_rs_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        integrations = [
            ('mod enhanced_download', "enhanced_download模块声明"),
            ('mod enhanced_tauri_commands', "enhanced_tauri_commands模块声明"),
            ('EnhancedDownloadState', "增强下载状态管理"),
            ('initialize_enhanced_download_system', "初始化命令"),
            ('enhanced_download_all_files', "批量下载命令"),
            ('get_enhanced_download_config', "配置获取命令"),
        ]
        
        all_integrated = True
        for pattern, description in integrations:
            if pattern in content:
                print_success(f"  {description} ✓")
            else:
                print_error(f"  {description} 缺失")
                all_integrated = False
        
        return all_integrated
        
    except Exception as e:
        print_error(f"  读取main.rs失败: {e}")
        return False

def analyze_code_metrics(file_path):
    """分析代码指标"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        total_lines = len(lines)
        code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('//')])
        comment_lines = len([line for line in lines if line.strip().startswith('//')])
        
        functions = len(re.findall(r'fn\s+\w+', content))
        structs = len(re.findall(r'struct\s+\w+', content))
        enums = len(re.findall(r'enum\s+\w+', content))
        
        print_info(f"  总行数: {total_lines}")
        print_info(f"  代码行数: {code_lines}")
        print_info(f"  注释行数: {comment_lines}")
        print_info(f"  函数数量: {functions}")
        print_info(f"  结构体数量: {structs}")
        print_info(f"  枚举数量: {enums}")
        
        if code_lines > 0:
            comment_ratio = (comment_lines / code_lines) * 100
            print_info(f"  注释比例: {comment_ratio:.1f}%")
        
        return {
            'total_lines': total_lines,
            'code_lines': code_lines,
            'comment_lines': comment_lines,
            'functions': functions,
            'structs': structs,
            'enums': enums
        }
        
    except Exception as e:
        print_error(f"  分析失败: {e}")
        return None

def main():
    print_header("Tera Launcher 增强下载系统代码验证")
    
    base_path = Path("D:/Takumi/Tool/tera-rust-launcher-main/teralaunch/src-tauri")
    
    # 1. 检查核心文件存在性
    print_header("1. 文件完整性检查")
    
    files_to_check = [
        (base_path / "src/enhanced_download.rs", "增强下载核心模块"),
        (base_path / "src/enhanced_tauri_commands.rs", "Tauri命令集成模块"),
        (base_path / "src/main.rs", "主程序文件"),
        (base_path / "Cargo.toml", "Cargo配置文件"),
    ]
    
    all_files_exist = True
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_files_exist = False
    
    if not all_files_exist:
        print_error("部分核心文件缺失，无法继续验证")
        return
    
    # 2. 检查Cargo.toml依赖
    print_header("2. 依赖配置检查")
    cargo_ok = check_cargo_toml(base_path / "Cargo.toml")
    
    # 3. 检查Rust语法
    print_header("3. Rust语法检查")
    
    rust_files = [
        (base_path / "src/enhanced_download.rs", "增强下载模块"),
        (base_path / "src/enhanced_tauri_commands.rs", "Tauri命令模块"),
    ]
    
    syntax_ok = True
    for file_path, description in rust_files:
        print_info(f"检查 {description}:")
        if not check_rust_syntax(file_path):
            syntax_ok = False
    
    # 4. 检查模块集成
    print_header("4. 模块集成检查")
    integration_ok = check_module_integration(base_path / "src/main.rs")
    
    # 5. 代码指标分析
    print_header("5. 代码指标分析")
    
    total_metrics = {'total_lines': 0, 'code_lines': 0, 'functions': 0, 'structs': 0}
    
    for file_path, description in rust_files:
        print_info(f"分析 {description}:")
        metrics = analyze_code_metrics(file_path)
        if metrics:
            for key in total_metrics:
                total_metrics[key] += metrics.get(key, 0)
    
    print_info(f"总计:")
    print_info(f"  总代码行数: {total_metrics['code_lines']}")
    print_info(f"  总函数数量: {total_metrics['functions']}")
    print_info(f"  总结构体数量: {total_metrics['structs']}")
    
    # 6. 生成验证报告
    print_header("6. 验证结果总结")
    
    checks = [
        ("文件完整性", all_files_exist),
        ("依赖配置", cargo_ok),
        ("语法检查", syntax_ok),
        ("模块集成", integration_ok),
    ]
    
    passed_checks = sum(1 for _, status in checks if status)
    total_checks = len(checks)
    
    for check_name, status in checks:
        if status:
            print_success(f"{check_name}: 通过")
        else:
            print_error(f"{check_name}: 失败")
    
    success_rate = (passed_checks / total_checks) * 100
    print_info(f"验证通过率: {success_rate:.1f}% ({passed_checks}/{total_checks})")
    
    if success_rate >= 100:
        print_success("🎉 所有验证通过！增强下载系统代码准备就绪")
    elif success_rate >= 75:
        print_warning("⚠️ 大部分验证通过，但有一些问题需要修复")
    else:
        print_error("❌ 验证失败，需要修复多个问题")
    
    # 7. 生成验证报告文件
    report = {
        "timestamp": "2024-06-29",
        "verification_results": {
            "file_integrity": all_files_exist,
            "dependency_config": cargo_ok,
            "syntax_check": syntax_ok,
            "module_integration": integration_ok,
            "success_rate": success_rate
        },
        "code_metrics": total_metrics,
        "recommendations": []
    }
    
    if not all_files_exist:
        report["recommendations"].append("确保所有核心文件都已正确创建")
    if not cargo_ok:
        report["recommendations"].append("检查并添加缺失的依赖项")
    if not syntax_ok:
        report["recommendations"].append("修复Rust语法错误")
    if not integration_ok:
        report["recommendations"].append("完善模块集成")
    
    if success_rate >= 100:
        report["recommendations"].append("代码验证通过，可以进行编译测试")
    
    try:
        with open("verification_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print_success("验证报告已保存到 verification_report.json")
    except Exception as e:
        print_warning(f"保存验证报告失败: {e}")

if __name__ == "__main__":
    main()
