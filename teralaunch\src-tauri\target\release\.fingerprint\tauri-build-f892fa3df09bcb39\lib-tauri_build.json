{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 9570484432207332980, "profile": 1369601567987815722, "path": 14648466730814367167, "deps": [[533142347765177280, "anyhow", false, 15689311046229657655], [1144798529435568546, "tauri_utils", false, 11208090494566982770], [2455542577989222201, "json_patch", false, 14244959492845236611], [3851720982022213547, "semver", false, 3352851235484081017], [4450062412064442726, "dirs_next", false, 10180981991972309300], [4704408070981680310, "serde_json", false, 12273186240585252122], [7468248713591957673, "cargo_toml", false, 12696336319663706288], [13077543566650298139, "heck", false, 1577398889775765422], [14189313126492979171, "tauri_winres", false, 3925267179965106453], [15622660310229662834, "walkdir", false, 12826178330065208541], [17174345729924723953, "serde", false, 11674544619059281444]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-f892fa3df09bcb39\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}