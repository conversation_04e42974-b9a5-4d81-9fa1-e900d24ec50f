#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Base64解密测试脚本
测试生成的Base64内容的解密功能
"""

import json
import base64

def test_base64_decryption():
    """测试Base64解密功能"""
    print("🔓 Base64解密测试")
    print("=" * 50)
    
    # 读取生成的Base64内容
    try:
        with open("base64_content.txt", "r", encoding="utf-8") as f:
            base64_content = f.read().strip()
        
        print(f"✅ 读取Base64内容成功，长度: {len(base64_content)} 字符")
        print(f"📄 Base64内容预览: {base64_content[:100]}...")
        
    except FileNotFoundError:
        print("❌ 未找到 base64_content.txt 文件")
        return False
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    # 解密Base64内容
    try:
        print("\n🔓 开始解密...")
        
        # Base64解码
        decoded_bytes = base64.b64decode(base64_content)
        decoded_str = decoded_bytes.decode('utf-8')
        
        print(f"✅ Base64解码成功，解码后长度: {len(decoded_str)} 字符")
        
        # JSON解析
        decoded_json = json.loads(decoded_str)
        
        print("✅ JSON解析成功")
        
        return decoded_json
        
    except base64.binascii.Error as e:
        print(f"❌ Base64解码失败: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 解密过程出错: {e}")
        return False

def display_decrypted_content(data):
    """显示解密后的内容"""
    if not data:
        return
    
    print("\n📋 解密后的内容详情:")
    print("=" * 50)
    
    # 基本信息
    print(f"🏷️  版本: {data.get('version', 'N/A')}")
    print(f"📅 发布日期: {data.get('release_date', 'N/A')}")
    print(f"🔐 校验和: {data.get('checksum', 'N/A')}")
    
    # 下载URL
    urls = data.get('full_download_urls', [])
    print(f"\n📥 下载URL ({len(urls)}个):")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
    
    # 增量更新信息
    print(f"\n🔄 增量服务器: {data.get('incremental_server_url', 'N/A')}")
    print(f"📋 文件列表URL: {data.get('file_list_url', 'N/A')}")
    
    # 下载信息
    download_info = data.get('download_info', {})
    if download_info:
        print(f"\n📊 下载信息:")
        print(f"  总大小: {download_info.get('total_size', 0) / (1024*1024*1024):.2f} GB")
        print(f"  压缩大小: {download_info.get('compressed_size', 0) / (1024*1024*1024):.2f} GB")
        print(f"  文件数量: {download_info.get('file_count', 0):,}")
        print(f"  预计下载时间: {download_info.get('estimated_download_time', 'N/A')}")
    
    # 更新日志
    changelog = data.get('changelog', [])
    if changelog:
        print(f"\n📝 更新日志 ({len(changelog)}项):")
        for i, item in enumerate(changelog, 1):
            print(f"  {i}. {item}")
    
    # 系统要求
    sys_req = data.get('system_requirements', {})
    if sys_req:
        print(f"\n💻 系统要求:")
        print(f"  最低操作系统: {sys_req.get('min_os', 'N/A')}")
        print(f"  最低内存: {sys_req.get('min_ram', 'N/A')}")
        print(f"  最低存储: {sys_req.get('min_storage', 'N/A')}")
        print(f"  推荐内存: {sys_req.get('recommended_ram', 'N/A')}")
        print(f"  推荐存储: {sys_req.get('recommended_storage', 'N/A')}")
    
    # 安全信息
    security = data.get('security', {})
    if security:
        print(f"\n🔒 安全信息:")
        print(f"  数字签名: {security.get('signature', 'N/A')[:50]}...")
        print(f"  证书指纹: {security.get('certificate_thumbprint', 'N/A')}")
        print(f"  验证URL: {security.get('verification_url', 'N/A')}")

def simulate_gitee_parsing():
    """模拟Gitee页面解析过程"""
    print("\n🌐 模拟Gitee页面解析")
    print("=" * 50)
    
    # 读取生成的测试页面
    try:
        with open("gitee_test_page.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        print("✅ 读取Gitee测试页面成功")
        
        # 查找 [[ ]] 标志
        start_marker = "[["
        end_marker = "]]"
        
        start_pos = html_content.find(start_marker)
        if start_pos == -1:
            print("❌ 未找到开始标志 [[")
            return False
        
        end_pos = html_content.find(end_marker, start_pos + len(start_marker))
        if end_pos == -1:
            print("❌ 未找到结束标志 ]]")
            return False
        
        # 提取内容
        extracted_content = html_content[start_pos + len(start_marker):end_pos].strip()
        
        print(f"✅ 成功提取内容，长度: {len(extracted_content)} 字符")
        print(f"📄 提取内容预览: {extracted_content[:100]}...")
        
        # 解密提取的内容
        try:
            decoded_bytes = base64.b64decode(extracted_content)
            decoded_str = decoded_bytes.decode('utf-8')
            decoded_json = json.loads(decoded_str)
            
            print("✅ 从HTML页面成功解密内容")
            
            return decoded_json
            
        except Exception as e:
            print(f"❌ 解密提取内容失败: {e}")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到 gitee_test_page.html 文件")
        return False
    except Exception as e:
        print(f"❌ 读取HTML文件失败: {e}")
        return False

def verify_data_integrity():
    """验证数据完整性"""
    print("\n🔍 数据完整性验证")
    print("=" * 50)
    
    try:
        # 读取原始JSON
        with open("original_update_info.json", "r", encoding="utf-8") as f:
            original_data = json.load(f)
        
        # 解密Base64内容
        decrypted_data = test_base64_decryption()
        
        if not decrypted_data:
            print("❌ 解密失败，无法验证完整性")
            return False
        
        # 比较数据
        if original_data == decrypted_data:
            print("✅ 数据完整性验证通过")
            print("🎉 原始数据与解密数据完全一致")
            return True
        else:
            print("❌ 数据完整性验证失败")
            print("⚠️  原始数据与解密数据不一致")
            
            # 详细比较
            for key in original_data:
                if key not in decrypted_data:
                    print(f"  缺失字段: {key}")
                elif original_data[key] != decrypted_data[key]:
                    print(f"  字段不匹配: {key}")
            
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def main():
    print("🔓 Tera Launcher Base64解密测试套件")
    print("=" * 60)
    
    # 测试1: 直接解密Base64内容
    print("\n📋 测试1: 直接Base64解密")
    decrypted_data = test_base64_decryption()
    
    if decrypted_data:
        display_decrypted_content(decrypted_data)
    
    # 测试2: 模拟Gitee页面解析
    print("\n📋 测试2: Gitee页面解析模拟")
    gitee_data = simulate_gitee_parsing()
    
    # 测试3: 数据完整性验证
    print("\n📋 测试3: 数据完整性验证")
    integrity_ok = verify_data_integrity()
    
    # 总结
    print("\n🎯 测试总结")
    print("=" * 50)
    
    tests = [
        ("Base64解密", decrypted_data is not False),
        ("Gitee页面解析", gitee_data is not False),
        ("数据完整性验证", integrity_ok)
    ]
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 100:
        print("🎉 所有测试通过！Base64加密解密功能完全正常")
        print("\n🚀 您现在可以:")
        print("1. 将Base64内容复制到您的Gitee页面")
        print("2. 使用格式: [[ Base64内容 ]]")
        print("3. 通过启动器测试内容获取和解密")
    elif success_rate >= 75:
        print("⚠️  大部分测试通过，但有一些问题需要修复")
    else:
        print("❌ 多个测试失败，需要检查实现")

if __name__ == "__main__":
    main()
