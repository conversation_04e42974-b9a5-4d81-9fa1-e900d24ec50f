{"rustc": 1842507548689473721, "features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"bytes\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"default\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-request\", \"indexmap\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"tauri-runtime-wry\", \"tracing\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 1305593064144852035, "profile": 2040997289075261528, "path": 8458121070050212388, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [533142347765177280, "anyhow", false, 5054827551355654798], [588322964515033593, "sys_locale", false, 9203771133348842687], [1076501750996383263, "once_cell", false, 3317331631347182004], [1144798529435568546, "tauri_utils", false, 10955185112020874079], [1389103965215294897, "flate2", false, 17106967885815777212], [2999978091831213404, "serde_repr", false, 841066534207666655], [3100833517036967723, "uuid", false, 3552296484931498028], [3353796506826114049, "thiserror", false, 6822031306346486717], [3597337515006351837, "tauri_macros", false, 453317140297949417], [3851720982022213547, "semver", false, 3107634668915628016], [3988549704697787137, "open", false, 14609487152149479169], [4381063397040571828, "webview2_com", false, 16178044120070079241], [4405182208873388884, "http", false, 10162976284368013428], [4450062412064442726, "dirs_next", false, 14429067226665745197], [4704408070981680310, "serde_json", false, 7911045850117905746], [4788400838143230126, "tauri_runtime_wry", false, 15656929441308391084], [4919829919303820331, "serialize_to_javascript", false, 7056970301859152391], [5099504066399492044, "rfd", false, 9473935713145222202], [5335596306115456489, "os_info", false, 3532753819069640477], [6235911559405361224, "tar", false, 10158727473902074872], [7244058819997729774, "reqwest", false, 15233216921825830011], [7653476968652377684, "windows", false, 14235264620752131749], [7670211519503158651, "getrandom", false, 10349748776571566680], [8145704335312028338, "glob", false, 13760738684759537197], [8800316697867969272, "regex", false, 8106054280523136889], [8858041990736880586, "tokio", false, 15737707881525506531], [9177686306454747611, "ignore", false, 622768764264572959], [9351479248917069247, "encoding_rs", false, 3662838284785351967], [10642096736294159704, "shared_child", false, 15759692877198131289], [11269248247346606853, "url", false, 15232645414707203371], [11637111059468842078, "tempfile", false, 4314121931603621123], [11693073011723388840, "raw_window_handle", false, 17436084257692388002], [11732963870180628222, "os_pipe", false, 213571219430118510], [11913130400938634928, "futures_util", false, 7705237049435048172], [13208667028893622512, "rand", false, 9325621976732195567], [14626413149905853098, "tracing", false, 15162072962576219282], [14854634077503860916, "build_script_build", false, 9668206616824697114], [14923790796823607459, "indexmap", false, 15018575592049718018], [15005373913467947101, "notify_rust", false, 13960207236513587497], [16227728351758841112, "bytes", false, 15932244784508788198], [16534286206067577747, "tauri_runtime", false, 12487180336084401378], [17174345729924723953, "serde", false, 10483066152181423585], [17278893514130263345, "state", false, 1813593475155985366], [18053436020821374870, "dunce", false, 15069243742515159317]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-ebea70bdb96a390e\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}