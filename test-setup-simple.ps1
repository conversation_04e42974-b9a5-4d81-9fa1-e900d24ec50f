# Tera Rust Launcher Complete Test Script
Write-Host "Tera Rust Launcher Complete Test Environment Verification" -ForegroundColor Green
Write-Host "=========================================================" -ForegroundColor Green
Write-Host ""

$ServerUrl = "http://localhost:8080"
$TestUser = "admin"
$TestPassword = "admin123"

# Function to test API endpoints
function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = $null,
        [string]$ContentType = "application/json",
        [string]$ExpectedContent = ""
    )
    
    Write-Host "Testing: $Name" -ForegroundColor Yellow
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
        }
        
        if ($Body) {
            if ($ContentType -eq "application/x-www-form-urlencoded") {
                $bodyString = ($Body.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
                $params.Body = $bodyString
            } else {
                $params.Body = $Body | ConvertTo-Json
            }
            $params.ContentType = $ContentType
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq 200) {
            if ($ExpectedContent -and $response.Content -like "*$ExpectedContent*") {
                Write-Host "   SUCCESS - Status: $($response.StatusCode), Contains expected content" -ForegroundColor Green
                return $true
            } elseif (!$ExpectedContent) {
                Write-Host "   SUCCESS - Status: $($response.StatusCode)" -ForegroundColor Green
                return $true
            } else {
                Write-Host "   WARNING - Status OK but content mismatch" -ForegroundColor Yellow
                return $false
            }
        } else {
            Write-Host "   FAILED - Status: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 1. Check if server is running
Write-Host "Checking test server status..." -ForegroundColor Cyan
$serverRunning = Test-Endpoint -Name "Server Health Check" -Url "$ServerUrl/health" -ExpectedContent "ok"

if (!$serverRunning) {
    Write-Host ""
    Write-Host "ERROR: Test server is not running! Please start the server first:" -ForegroundColor Red
    Write-Host "   cd test-server" -ForegroundColor White
    Write-Host "   node server.js" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host ""

# 2. Test all API endpoints
Write-Host "Testing API endpoints..." -ForegroundColor Cyan

$apiTests = @(
    @{ Name = "Hash File API"; Url = "$ServerUrl/tera/launcher/hash-file.json"; Expected = "files" },
    @{ Name = "Server List API"; Url = "$ServerUrl/tera/ServerList.json"; Expected = "servers" }
)

$apiSuccess = $true
foreach ($test in $apiTests) {
    $result = Test-Endpoint -Name $test.Name -Url $test.Url -ExpectedContent $test.Expected
    if (!$result) { $apiSuccess = $false }
}

# 3. Test login API
Write-Host ""
Write-Host "Testing login functionality..." -ForegroundColor Cyan

$loginBody = @{
    login = $TestUser
    password = $TestPassword
}

$loginSuccess = Test-Endpoint -Name "User Login (Correct Credentials)" -Url "$ServerUrl/tera/LauncherLoginAction" -Method "POST" -Body $loginBody -ContentType "application/x-www-form-urlencoded" -ExpectedContent "success"

# 4. Check file system
Write-Host ""
Write-Host "Checking file system..." -ForegroundColor Cyan

$fileChecks = @(
    @{ Path = "teralaunch\src-tauri\target\release\teralaunch.exe"; Name = "Launcher Executable" },
    @{ Path = "teralaunch\src-tauri\target\release\tera_config.ini"; Name = "Config File (Executable Dir)" },
    @{ Path = "C:\tera"; Name = "Game Directory" },
    @{ Path = "C:\tera\Binaries\Tera.exe"; Name = "Game Executable" },
    @{ Path = "test-server\server.js"; Name = "Test Server" }
)

$fileSuccess = $true
foreach ($check in $fileChecks) {
    if (Test-Path $check.Path) {
        Write-Host "   SUCCESS: $($check.Name) exists" -ForegroundColor Green
    } else {
        Write-Host "   ERROR: $($check.Name) missing - $($check.Path)" -ForegroundColor Red
        $fileSuccess = $false
    }
}

# 5. Check configuration file content
Write-Host ""
Write-Host "Checking configuration..." -ForegroundColor Cyan

try {
    $configPath = "teralaunch\src-tauri\target\release\tera_config.ini"
    if (Test-Path $configPath) {
        $configContent = Get-Content $configPath -Raw
        if ($configContent -like "*path=C:\\tera*") {
            Write-Host "   SUCCESS: Config file path is correct" -ForegroundColor Green
        } else {
            Write-Host "   ERROR: Config file path is incorrect" -ForegroundColor Red
            $fileSuccess = $false
        }
        
        if ($configContent -like "*lang=EUR*") {
            Write-Host "   SUCCESS: Config file language setting is correct" -ForegroundColor Green
        } else {
            Write-Host "   ERROR: Config file language setting is incorrect" -ForegroundColor Red
            $fileSuccess = $false
        }
    }
} catch {
    Write-Host "   ERROR: Cannot read config file: $($_.Exception.Message)" -ForegroundColor Red
    $fileSuccess = $false
}

# 6. Summary
Write-Host ""
Write-Host "Test Results Summary" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($serverRunning) {
    Write-Host "SUCCESS: Test Server is running" -ForegroundColor Green
} else {
    Write-Host "ERROR: Test Server is not running" -ForegroundColor Red
}

if ($apiSuccess) {
    Write-Host "SUCCESS: All API endpoints working" -ForegroundColor Green
} else {
    Write-Host "ERROR: Some API endpoints failed" -ForegroundColor Red
}

if ($loginSuccess) {
    Write-Host "SUCCESS: Login functionality working" -ForegroundColor Green
} else {
    Write-Host "ERROR: Login functionality failed" -ForegroundColor Red
}

if ($fileSuccess) {
    Write-Host "SUCCESS: File system configured correctly" -ForegroundColor Green
} else {
    Write-Host "ERROR: File system configuration incomplete" -ForegroundColor Red
}

Write-Host ""

if ($serverRunning -and $apiSuccess -and $loginSuccess -and $fileSuccess) {
    Write-Host "ALL TESTS PASSED! Environment setup complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now start the launcher for testing:" -ForegroundColor Cyan
    Write-Host "   .\teralaunch\src-tauri\target\release\teralaunch.exe" -ForegroundColor White
    Write-Host ""
    Write-Host "Test login credentials:" -ForegroundColor Cyan
    Write-Host "   Username: admin" -ForegroundColor White
    Write-Host "   Password: admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "   Or:" -ForegroundColor White
    Write-Host "   Username: testuser" -ForegroundColor White
    Write-Host "   Password: testpass" -ForegroundColor White
} else {
    Write-Host "Some tests failed, please check the errors above and fix them" -ForegroundColor Yellow
    exit 1
}
