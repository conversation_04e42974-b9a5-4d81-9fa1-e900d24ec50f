{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 3165595516910038244, "profile": 15657897354478470176, "path": 8601774455100860520, "deps": [[1133100163585637996, "tower_service", false, 11194521150169456342], [3664886486575944113, "tower_layer", false, 3323670639348905146], [4405182208873388884, "http", false, 7948823251931111416], [5459299554234637951, "async_trait", false, 11988379071944273682], [8915503303801890683, "http_body", false, 8847447128091011592], [9293824762099617471, "build_script_build", false, 11389531582947996455], [10229185211513642314, "mime", false, 12149313404929737718], [11913130400938634928, "futures_util", false, 1357397610844759036], [16227728351758841112, "bytes", false, 13084351430820299866]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-a25f5598da0ebe96\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}