# Tera Rust Launcher 本地测试指南

## 🎯 测试环境概述

本测试环境包含：
- **本地测试服务器** (Node.js) - 模拟 Tera 游戏服务器
- **编译好的启动器** - Tera Rust Launcher 可执行文件
- **测试数据** - 模拟的游戏文件和用户数据

## 🚀 快速开始

### 1. 启动测试服务器

```powershell
# 进入项目目录
cd d:\Takumi\Tool\tera-rust-launcher-main

# 设置环境变量
$env:PATH = [System.Environment]::GetEnvironmentVariable("PATH","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("PATH","User")

# 启动服务器
cd test-server
node server.js
```

服务器将在 `http://localhost:8080` 运行

### 2. 启动 Tera Launcher

```powershell
# 在新的 PowerShell 窗口中
cd d:\Takumi\Tool\tera-rust-launcher-main
.\teralaunch\src-tauri\target\release\teralaunch.exe
```

## 🧪 测试功能

### 登录测试
- **用户名**: `testuser`
- **密码**: `testpass`
- **管理员**: `admin` / `admin123`

### API 端点测试

#### 健康检查
```powershell
curl http://localhost:8080/health
```
预期响应: `{"status":"ok","timestamp":"...","server":"Tera Test Server"}`

#### 用户登录
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/tera/LauncherLoginAction" -Method POST -Body "login=testuser&password=testpass" -ContentType "application/x-www-form-urlencoded"
```
预期响应: `OK|3|test-ticket-12345|1`

#### 哈希文件获取
```powershell
curl http://localhost:8080/tera/launcher/hash-file.json
```
预期响应: JSON 格式的文件哈希列表

#### 服务器列表
```powershell
curl http://localhost:8080/tera/ServerList.json
```
预期响应: 服务器列表 JSON

## 📁 文件结构

```
tera-rust-launcher-main/
├── teralaunch/                          # 主启动器项目
│   ├── src-tauri/target/release/
│   │   └── teralaunch.exe              # 编译好的启动器
│   └── tera_config.ini                 # 游戏配置文件
├── teralib/                            # Rust 核心库
│   └── src/config/config.json          # 服务器配置
├── test-server/                        # 测试服务器
│   ├── server.js                       # 服务器主文件
│   ├── data/hash-file.json            # 哈希文件数据
│   └── public/                         # 静态文件目录
│       ├── Tera.exe                    # 模拟游戏可执行文件
│       ├── ReleaseRevision.txt         # 版本信息
│       └── S1Game/Config/S1Engine.ini  # 游戏配置
└── test-scripts/                       # 测试脚本
    └── test.bat                        # 简单测试脚本
```

## ⚙️ 配置说明

### 服务器配置 (`teralib/src/config/config.json`)
```json
{
    "LOGIN_ACTION_URL": "http://localhost:8080/tera/LauncherLoginAction",
    "HASH_FILE_URL": "http://localhost:8080/tera/launcher/hash-file.json",
    "FILE_SERVER_URL": "http://localhost:8080/public",
    "SERVER_LIST_URL": "http://localhost:8080/tera/ServerList.json?lang=en&sort=3"
}
```

### 游戏配置 (`teralaunch/tera_config.ini`)
```ini
[game]
lang=EUR
path=C:\tera
```

## 🔧 功能测试清单

### ✅ 已验证功能
- [x] 服务器健康检查
- [x] 用户登录验证
- [x] 哈希文件获取
- [x] 服务器列表获取
- [x] 静态文件服务
- [x] 启动器编译
- [x] 配置文件加载

### 🧪 需要手动测试的功能
- [ ] 启动器 UI 界面
- [ ] 文件完整性检查
- [ ] 文件下载更新
- [ ] 游戏启动流程
- [ ] 多语言切换
- [ ] 错误处理

## 🐛 常见问题

### 1. 服务器启动失败
**问题**: `Error: Cannot find module 'express'`
**解决**: 
```powershell
cd test-server
npm install
```

### 2. 端口被占用
**问题**: `EADDRINUSE: address already in use :::8080`
**解决**: 
```powershell
# 查找占用端口的进程
netstat -ano | findstr :8080
# 终止进程 (替换 PID)
taskkill /PID <PID> /F
```

### 3. 启动器无法连接服务器
**问题**: 启动器显示连接错误
**解决**: 
1. 确认服务器正在运行: `curl http://localhost:8080/health`
2. 检查防火墙设置
3. 验证配置文件中的服务器地址

### 4. 游戏目录不存在
**问题**: 启动器提示游戏路径无效
**解决**: 
```powershell
mkdir C:\tera -Force
```

## 📊 性能指标

### 服务器响应时间
- 健康检查: < 10ms
- 登录验证: < 50ms
- 哈希文件: < 100ms
- 文件下载: 取决于文件大小

### 启动器性能
- 启动时间: < 3 秒
- 文件检查: 并行处理，取决于文件数量
- 内存使用: ~50MB

## 🔄 开发模式

### 热重载开发
```powershell
cd teralaunch
npm run tauri dev
```

### 重新编译
```powershell
cd teralaunch/src-tauri
cargo build --release
```

## 📝 日志和调试

### 服务器日志
服务器控制台会显示所有 API 请求和响应

### 启动器日志
- 开发模式: 浏览器开发者工具
- 生产模式: 系统事件日志

## 🎉 测试完成标准

当以下所有项目都通过时，测试环境配置成功：

1. ✅ 服务器健康检查返回 200
2. ✅ 登录 API 返回正确的票据信息
3. ✅ 哈希文件 API 返回文件列表
4. ✅ 启动器可执行文件存在且可运行
5. ✅ 配置文件格式正确且指向本地服务器
6. ✅ 游戏目录存在

---

**注意**: 这是一个测试环境，不适用于生产环境。实际部署时需要配置真实的服务器地址和安全设置。
