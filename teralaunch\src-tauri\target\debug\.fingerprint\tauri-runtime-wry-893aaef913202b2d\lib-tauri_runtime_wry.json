{"rustc": 1842507548689473721, "features": "[\"arboard\", \"clipboard\", \"global-shortcut\", \"objc-exception\", \"tracing\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 12286328633679254747, "profile": 15657897354478470176, "path": 7448810318923470451, "deps": [[1144798529435568546, "tauri_utils", false, 7903705833100539925], [3100833517036967723, "uuid", false, 3269919355752741088], [4381063397040571828, "webview2_com", false, 17950373685392314976], [4788400838143230126, "build_script_build", false, 4723738786912925395], [7483013550296171226, "arboard", false, 15014527398276070566], [7653476968652377684, "windows", false, 6375946999918238853], [11693073011723388840, "raw_window_handle", false, 5676378102289126459], [13208667028893622512, "rand", false, 18054765264797586153], [14626413149905853098, "tracing", false, 2674625738167854746], [16534286206067577747, "tauri_runtime", false, 15310161457339588962], [17068110978337472778, "wry", false, 736161172087677936]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-893aaef913202b2d\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}