{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 13418454777403878664, "profile": 2040997289075261528, "path": 16454806223936796563, "deps": [[1144798529435568546, "tauri_utils", false, 10955185112020874079], [3100833517036967723, "uuid", false, 3552296484931498028], [3353796506826114049, "thiserror", false, 6822031306346486717], [4381063397040571828, "webview2_com", false, 16178044120070079241], [4405182208873388884, "http", false, 10162976284368013428], [4704408070981680310, "serde_json", false, 7911045850117905746], [7653476968652377684, "windows", false, 14235264620752131749], [8866577183823226611, "http_range", false, 5210849386109630347], [11269248247346606853, "url", false, 15232645414707203371], [11693073011723388840, "raw_window_handle", false, 17436084257692388002], [13208667028893622512, "rand", false, 9325621976732195567], [16534286206067577747, "build_script_build", false, 18344065766114155944], [17174345729924723953, "serde", false, 10483066152181423585]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-494f89217607934b\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}