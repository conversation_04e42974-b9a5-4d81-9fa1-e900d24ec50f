# 增强下载系统所需的 Cargo.toml 依赖更新
# 将这些依赖添加到 teralaunch/src-tauri/Cargo.toml 中

[dependencies]
# 现有依赖保持不变...
tauri = { version = "1", features = [ "api-all"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
teralib = { path = "../../teralib" }
parking_lot = "0.12.1"
tokio = { version = "1.37.0", features = ["full"] }
tokio-macros = "2.2.0"
log = "0.4.22"
reqwest = { version = "0.12.7", features = ["json", "stream"] }
lazy_static = "1.4.0"
rust-ini = "0.21.0"
sha2 = "0.10.8"
futures-util = "0.3"
indicatif = "0.17.8"
walkdir = "2.5.0"
rayon = "1.10.0"
thiserror = "1.0.63"
env_logger = "0.10.0"
devtools = "0.3.3"
tracing = "0.1"
dotenv = "0.15.0"

# 新增依赖 - 增强下载系统
async-trait = "0.1.74"           # 异步 trait 支持
bytes = "1.5.0"                  # 字节处理
uuid = { version = "1.6.1", features = ["v4"] }  # UUID 生成

# 可选依赖 - 根据选择的下载引擎启用

# Option 1: libcurl 集成 (推荐用于高性能场景)
curl = { version = "0.4.44", optional = true }

# Option 2: Aria2 集成 (需要外部 aria2c 可执行文件)
# 无需额外依赖，通过 RPC 调用

# Option 3: 其他下载库选项
# ureq = { version = "2.8.0", optional = true }     # 轻量级 HTTP 客户端
# isahc = { version = "1.7.2", optional = true }   # 基于 libcurl 的异步客户端

# 压缩和解压缩支持 (用于增量更新)
flate2 = "1.0.28"               # gzip/deflate 压缩
lz4_flex = "0.11.1"             # LZ4 压缩
zstd = "0.13.0"                 # Zstandard 压缩

# 二进制差分和补丁 (用于增量更新)
# bsdiff = "1.0.0"              # BSD diff/patch 算法
# xdelta3 = "0.1.0"             # XDelta3 算法 (如果有 Rust 绑定)

# 数据库支持 (用于缓存和版本管理)
rusqlite = { version = "0.30.0", features = ["bundled"] }

# 配置管理增强
config = "0.13.4"               # 配置文件管理
toml = "0.8.8"                  # TOML 格式支持

# 网络和协议增强
url = "2.5.0"                   # URL 解析和处理
mime = "0.3.17"                 # MIME 类型处理

# 系统信息和监控
sysinfo = "0.30.5"              # 系统信息获取
disk-usage = "1.0.0"            # 磁盘使用情况

# 特性标志定义
[features]
default = ["enhanced-reqwest"]

# 下载引擎特性
enhanced-reqwest = []           # 增强的 reqwest 引擎 (默认)
curl-engine = ["curl"]          # libcurl 引擎
aria2-engine = []               # Aria2 引擎 (需要外部 aria2c)

# 压缩算法特性
compression-gzip = ["flate2"]
compression-lz4 = ["lz4_flex"]
compression-zstd = ["zstd"]

# 增量更新特性
delta-updates = []              # 增量更新支持
# delta-bsdiff = ["bsdiff"]     # BSD diff 算法
# delta-xdelta = ["xdelta3"]    # XDelta3 算法

# 高级功能特性
database-cache = ["rusqlite"]   # 数据库缓存
system-monitoring = ["sysinfo", "disk-usage"]  # 系统监控
advanced-config = ["config", "toml"]  # 高级配置

# 开发和调试特性
dev-tools = ["devtools"]
debug-logging = ["tracing"]

# 示例 Cargo.toml 完整配置
[package]
name = "teralaunch"
version = "0.1.0"
description = "Enhanced Tera Launcher with Advanced Download System"
authors = ["TNC97"]
edition = "2021"

[build-dependencies]
tauri-build = { version = "1", features = [] }

# 编译优化配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
overflow-checks = true

# 平台特定配置
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3.9", features = ["wininet", "winhttp"] }

[target.'cfg(unix)'.dependencies]
libc = "0.2.150"

# 示例使用方法：
# 
# 1. 基础增强 (默认)：
#    cargo build --release
#
# 2. 启用 libcurl 引擎：
#    cargo build --release --features curl-engine
#
# 3. 启用所有功能：
#    cargo build --release --features "curl-engine,aria2-engine,compression-gzip,compression-lz4,delta-updates,database-cache,system-monitoring"
#
# 4. 开发模式：
#    cargo build --features "dev-tools,debug-logging"

# 注意事项：
# 1. libcurl 需要系统安装 libcurl 开发库
# 2. Aria2 引擎需要系统安装 aria2c 可执行文件
# 3. 某些压缩算法可能需要额外的系统库
# 4. 在 Windows 上可能需要 Visual Studio Build Tools
