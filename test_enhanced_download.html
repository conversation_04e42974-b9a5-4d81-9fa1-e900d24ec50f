<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tera Launcher 增强下载系统测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }

        .test-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }

        .log-entry {
            margin-bottom: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Tera Launcher 增强下载系统测试</h1>
        
        <div class="controls">
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testConfiguration()">测试配置</button>
            <button onclick="testNetworkConnectivity()">测试网络连接</button>
            <button onclick="testDownloadSimulation()">测试下载模拟</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="test-section">
            <h2>📋 测试1: 配置验证</h2>
            <div class="test-item">
                <strong>增强下载配置创建</strong>
                <div id="config-test-result" class="test-result info">等待测试...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 测试2: 网络连接</h2>
            <div class="test-item">
                <strong>基础网络连接测试</strong>
                <div id="network-test-result" class="test-result info">等待测试...</div>
            </div>
            <div class="test-item">
                <strong>Gitee连接测试</strong>
                <div id="gitee-test-result" class="test-result info">等待测试...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📥 测试3: 下载功能模拟</h2>
            <div class="test-item">
                <strong>下载进度模拟</strong>
                <div class="progress-bar">
                    <div id="download-progress" class="progress-fill" style="width: 0%"></div>
                </div>
                <div id="download-test-result" class="test-result info">等待测试...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 测试4: 错误处理</h2>
            <div class="test-item">
                <strong>故障切换模拟</strong>
                <div id="failover-test-result" class="test-result info">等待测试...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试5: 性能指标</h2>
            <div class="test-item">
                <strong>性能基准测试</strong>
                <div id="performance-test-result" class="test-result info">等待测试...</div>
            </div>
        </div>

        <div class="log-container">
            <h3>📋 测试日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        // 测试日志系统
        function addLog(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span style="color: #666;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('log-output').innerHTML = '';
            addLog('日志已清空');
        }

        // 更新测试结果
        function updateTestResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        // 测试1: 配置验证
        async function testConfiguration() {
            addLog('开始配置验证测试...');
            
            try {
                // 模拟增强下载配置
                const config = {
                    gitee_url: "https://gitee.com/mango_mu/test",
                    weibo_url: "",
                    retry_interval_seconds: 30,
                    address_switch_interval_seconds: 30,
                    max_retry_attempts: 5,
                    download_timeout_seconds: 300,
                    chunk_size: 1024 * 1024,
                    max_concurrent_downloads: 4,
                    enable_resume: true,
                    enable_chunked_download: true,
                    speed_limit: null
                };

                // 验证配置
                if (config.gitee_url && config.max_concurrent_downloads > 0 && config.chunk_size > 0) {
                    updateTestResult('config-test-result', 
                        `✅ 配置验证成功\n- Gitee URL: ${config.gitee_url}\n- 最大并发: ${config.max_concurrent_downloads}\n- 块大小: ${config.chunk_size / (1024 * 1024)}MB`, 
                        'success');
                    addLog('配置验证测试通过');
                    return true;
                } else {
                    throw new Error('配置验证失败');
                }
            } catch (error) {
                updateTestResult('config-test-result', `❌ 配置验证失败: ${error.message}`, 'error');
                addLog(`配置验证测试失败: ${error.message}`);
                return false;
            }
        }

        // 测试2: 网络连接
        async function testNetworkConnectivity() {
            addLog('开始网络连接测试...');
            
            // 基础网络连接测试
            try {
                const response = await fetch('https://httpbin.org/status/200', {
                    method: 'HEAD',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    updateTestResult('network-test-result', 
                        `✅ 基础网络连接正常\n- 状态码: ${response.status}\n- 响应时间: ${Date.now() - performance.now()}ms`, 
                        'success');
                    addLog('基础网络连接测试通过');
                } else {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
            } catch (error) {
                updateTestResult('network-test-result', `❌ 网络连接失败: ${error.message}`, 'error');
                addLog(`网络连接测试失败: ${error.message}`);
            }

            // Gitee连接测试
            try {
                const startTime = performance.now();
                const response = await fetch('https://gitee.com', {
                    method: 'HEAD',
                    mode: 'no-cors' // 避免CORS问题
                });
                const endTime = performance.now();
                
                updateTestResult('gitee-test-result', 
                    `✅ Gitee连接测试完成\n- 响应时间: ${Math.round(endTime - startTime)}ms\n- 连接状态: 可达`, 
                    'success');
                addLog('Gitee连接测试通过');
            } catch (error) {
                updateTestResult('gitee-test-result', `⚠️ Gitee连接测试: ${error.message}`, 'warning');
                addLog(`Gitee连接测试警告: ${error.message}`);
            }
        }

        // 测试3: 下载功能模拟
        async function testDownloadSimulation() {
            addLog('开始下载功能模拟测试...');
            
            const progressBar = document.getElementById('download-progress');
            let progress = 0;
            const totalSize = 10 * 1024 * 1024; // 10MB模拟
            let downloadedSize = 0;
            
            updateTestResult('download-test-result', '🔄 开始模拟下载...', 'info');
            
            const downloadInterval = setInterval(() => {
                // 模拟下载进度
                const chunkSize = Math.random() * 512 * 1024; // 随机块大小
                downloadedSize += chunkSize;
                progress = Math.min((downloadedSize / totalSize) * 100, 100);
                
                progressBar.style.width = `${progress}%`;
                
                const speed = chunkSize / 1024; // KB/s
                const eta = progress < 100 ? Math.round((totalSize - downloadedSize) / (chunkSize * 10)) : 0;
                
                updateTestResult('download-test-result', 
                    `📥 下载进度: ${progress.toFixed(1)}%\n- 已下载: ${(downloadedSize / (1024 * 1024)).toFixed(2)}MB\n- 速度: ${speed.toFixed(1)}KB/s\n- 剩余时间: ${eta}s`, 
                    'info');
                
                if (progress >= 100) {
                    clearInterval(downloadInterval);
                    updateTestResult('download-test-result', 
                        `✅ 下载模拟完成\n- 总大小: ${(totalSize / (1024 * 1024)).toFixed(2)}MB\n- 平均速度: ${((totalSize / 1024) / 10).toFixed(1)}KB/s`, 
                        'success');
                    addLog('下载功能模拟测试完成');
                }
            }, 100);
        }

        // 测试4: 故障切换模拟
        async function testFailoverSimulation() {
            addLog('开始故障切换模拟测试...');
            
            const urls = [
                'https://source1.example.com/file.zip',
                'https://source2.example.com/file.zip',
                'https://source3.example.com/file.zip',
                'https://source4.example.com/file.zip',
                'https://source5.example.com/file.zip'
            ];
            
            let currentUrlIndex = 0;
            let retryCount = 0;
            const maxRetries = 3;
            
            function simulateDownloadAttempt() {
                const currentUrl = urls[currentUrlIndex];
                const success = Math.random() > 0.7; // 30%成功率模拟
                
                if (success) {
                    updateTestResult('failover-test-result', 
                        `✅ 故障切换成功\n- 成功URL: ${currentUrl}\n- 尝试次数: ${currentUrlIndex + 1}\n- 重试次数: ${retryCount}`, 
                        'success');
                    addLog(`故障切换测试成功，使用URL ${currentUrlIndex + 1}`);
                    return true;
                } else {
                    addLog(`URL ${currentUrlIndex + 1} 失败，切换到下一个源`);
                    currentUrlIndex++;
                    
                    if (currentUrlIndex >= urls.length) {
                        retryCount++;
                        if (retryCount >= maxRetries) {
                            updateTestResult('failover-test-result', 
                                `❌ 故障切换失败\n- 所有URL都失败\n- 重试次数: ${retryCount}`, 
                                'error');
                            addLog('故障切换测试失败：所有源都不可用');
                            return false;
                        }
                        currentUrlIndex = 0;
                        addLog(`重试第 ${retryCount} 次，重新开始URL轮询`);
                    }
                    
                    setTimeout(simulateDownloadAttempt, 500);
                    return false;
                }
            }
            
            updateTestResult('failover-test-result', '🔄 模拟故障切换...', 'info');
            simulateDownloadAttempt();
        }

        // 测试5: 性能基准
        async function testPerformanceBenchmark() {
            addLog('开始性能基准测试...');
            
            const startTime = performance.now();
            
            // 模拟性能测试
            const tests = [
                { name: '配置加载', duration: Math.random() * 50 + 10 },
                { name: '网络连接建立', duration: Math.random() * 200 + 50 },
                { name: '下载速度', duration: Math.random() * 100 + 20 },
                { name: '文件验证', duration: Math.random() * 80 + 15 },
                { name: '错误恢复', duration: Math.random() * 150 + 30 }
            ];
            
            let results = [];
            
            for (const test of tests) {
                await new Promise(resolve => setTimeout(resolve, test.duration));
                results.push(`${test.name}: ${test.duration.toFixed(1)}ms`);
                addLog(`性能测试 - ${test.name}: ${test.duration.toFixed(1)}ms`);
            }
            
            const totalTime = performance.now() - startTime;
            const avgTime = totalTime / tests.length;
            
            updateTestResult('performance-test-result', 
                `✅ 性能基准测试完成\n${results.join('\n')}\n- 总时间: ${totalTime.toFixed(1)}ms\n- 平均时间: ${avgTime.toFixed(1)}ms`, 
                'success');
            
            addLog(`性能基准测试完成，总时间: ${totalTime.toFixed(1)}ms`);
        }

        // 运行所有测试
        async function runAllTests() {
            addLog('🚀 开始运行所有测试...');
            
            const tests = [
                { name: '配置验证', func: testConfiguration },
                { name: '网络连接', func: testNetworkConnectivity },
                { name: '下载模拟', func: testDownloadSimulation },
                { name: '故障切换', func: testFailoverSimulation },
                { name: '性能基准', func: testPerformanceBenchmark }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                try {
                    addLog(`开始测试: ${test.name}`);
                    const result = await test.func();
                    if (result !== false) {
                        passedTests++;
                    }
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 测试间隔
                } catch (error) {
                    addLog(`测试失败 - ${test.name}: ${error.message}`);
                }
            }
            
            const successRate = (passedTests / tests.length) * 100;
            addLog(`🎉 所有测试完成！通过率: ${successRate.toFixed(1)}% (${passedTests}/${tests.length})`);
            
            if (successRate >= 80) {
                addLog('✅ 增强下载系统测试通过，系统准备就绪！');
            } else {
                addLog('⚠️ 部分测试失败，请检查系统配置');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🧪 增强下载系统测试页面已加载');
            addLog('点击"运行所有测试"开始完整测试，或选择单个测试项目');
        });
    </script>
</body>
</html>
