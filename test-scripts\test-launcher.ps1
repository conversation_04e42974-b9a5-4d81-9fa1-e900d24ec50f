# Tera Rust Launcher 自动化测试脚本
# 测试本地服务器和启动器的各项功能

param(
    [string]$ServerUrl = "http://localhost:8080",
    [string]$TestUser = "testuser",
    [string]$TestPassword = "testpass"
)

Write-Host "🚀 Tera Rust Launcher 自动化测试" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# 测试结果统计
$TestResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Errors = @()
}

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Body = @{},
        [string]$ContentType = "application/json",
        [string]$ExpectedContent = $null
    )
    
    $TestResults.Total++
    Write-Host "🔍 测试: $Name" -ForegroundColor Yellow
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            ErrorAction = "Stop"
        }
        
        if ($Method -eq "POST" -and $Body.Count -gt 0) {
            if ($ContentType -eq "application/x-www-form-urlencoded") {
                $bodyString = ($Body.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
                $params.Body = $bodyString
                $params.ContentType = $ContentType
            } else {
                $params.Body = $Body | ConvertTo-Json
                $params.ContentType = $ContentType
            }
        }
        
        $response = Invoke-WebRequest @params
        
        if ($response.StatusCode -eq 200) {
            if ($ExpectedContent -and $response.Content -notlike "*$ExpectedContent*") {
                throw "响应内容不符合预期"
            }
            Write-Host "   ✅ 通过 (状态码: $($response.StatusCode))" -ForegroundColor Green
            $TestResults.Passed++
            return $true
        } else {
            throw "HTTP 状态码: $($response.StatusCode)"
        }
    }
    catch {
        Write-Host "   ❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
        $TestResults.Failed++
        $TestResults.Errors += "$Name : $($_.Exception.Message)"
        return $false
    }
}

function Test-FileExists {
    param(
        [string]$Name,
        [string]$Path
    )
    
    $TestResults.Total++
    Write-Host "🔍 测试: $Name" -ForegroundColor Yellow
    
    if (Test-Path $Path) {
        Write-Host "   ✅ 通过 (文件存在: $Path)" -ForegroundColor Green
        $TestResults.Passed++
        return $true
    } else {
        Write-Host "   ❌ 失败: 文件不存在 $Path" -ForegroundColor Red
        $TestResults.Failed++
        $TestResults.Errors += "$Name : 文件不存在 $Path"
        return $false
    }
}

# 开始测试
Write-Host "📋 开始功能测试..." -ForegroundColor Cyan
Write-Host ""

# 1. 测试服务器健康检查
Test-Endpoint -Name "服务器健康检查" -Url "$ServerUrl/health" -ExpectedContent "ok"

# 2. 测试哈希文件 API
Test-Endpoint -Name "哈希文件获取" -Url "$ServerUrl/tera/launcher/hash-file.json" -ExpectedContent "files"

# 3. 测试服务器列表 API
Test-Endpoint -Name "服务器列表获取" -Url "$ServerUrl/tera/ServerList.json" -ExpectedContent "servers"

# 4. 测试登录 API - 正确凭据
$loginBody = @{
    login = $TestUser
    password = $TestPassword
}
Test-Endpoint -Name "用户登录 (正确凭据)" -Url "$ServerUrl/tera/LauncherLoginAction" -Method "POST" -Body $loginBody -ContentType "application/x-www-form-urlencoded" -ExpectedContent "OK"

# 5. 测试登录 API - 错误凭据
$wrongLoginBody = @{
    login = "wronguser"
    password = "wrongpass"
}
Write-Host "🔍 测试: 用户登录 (错误凭据)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/tera/LauncherLoginAction" -Method POST -Body "login=wronguser&password=wrongpass" -ContentType "application/x-www-form-urlencoded" -ErrorAction Stop
    Write-Host "   ❌ 失败: 应该返回错误但返回了成功" -ForegroundColor Red
    $TestResults.Failed++
    $TestResults.Errors += "用户登录 (错误凭据) : 应该返回错误但返回了成功"
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "   ✅ 通过 (正确返回 401 未授权)" -ForegroundColor Green
        $TestResults.Passed++
    } else {
        Write-Host "   ❌ 失败: 预期 401 但得到 $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        $TestResults.Failed++
        $TestResults.Errors += "用户登录 (错误凭据) : 预期 401 但得到 $($_.Exception.Response.StatusCode)"
    }
}
$TestResults.Total++

# 6. 测试静态文件服务
Test-Endpoint -Name "静态文件服务 (Tera.exe)" -Url "$ServerUrl/public/Tera.exe"
Test-Endpoint -Name "静态文件服务 (ReleaseRevision.txt)" -Url "$ServerUrl/public/ReleaseRevision.txt" -ExpectedContent "Tera Online Test Server"

# 7. 测试启动器文件
Write-Host ""
Write-Host "📁 测试启动器文件..." -ForegroundColor Cyan
Test-FileExists -Name "启动器可执行文件" -Path "teralaunch\src-tauri\target\release\teralaunch.exe"
Test-FileExists -Name "配置文件" -Path "teralib\src\config\config.json"
Test-FileExists -Name "游戏配置文件" -Path "teralaunch\tera_config.ini"

# 8. 测试配置文件内容
Write-Host ""
Write-Host "⚙️  测试配置文件内容..." -ForegroundColor Cyan

$TestResults.Total++
Write-Host "🔍 测试: 配置文件 JSON 格式" -ForegroundColor Yellow
try {
    $config = Get-Content "teralib\src\config\config.json" | ConvertFrom-Json
    if ($config.LOGIN_ACTION_URL -like "*localhost:8080*") {
        Write-Host "   ✅ 通过 (配置指向本地服务器)" -ForegroundColor Green
        $TestResults.Passed++
    } else {
        throw "配置未指向本地服务器"
    }
} catch {
    Write-Host "   ❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
    $TestResults.Failed++
    $TestResults.Errors += "配置文件 JSON 格式 : $($_.Exception.Message)"
}

# 9. 测试游戏目录
Write-Host ""
Write-Host "🎮 测试游戏目录..." -ForegroundColor Cyan
Test-FileExists -Name "游戏目录存在" -Path "C:\tera"

# 输出测试结果
Write-Host ""
Write-Host "📊 测试结果汇总" -ForegroundColor Magenta
Write-Host "================" -ForegroundColor Magenta
Write-Host "总测试数: $($TestResults.Total)" -ForegroundColor White
Write-Host "通过: $($TestResults.Passed)" -ForegroundColor Green
Write-Host "失败: $($TestResults.Failed)" -ForegroundColor Red

if ($TestResults.Failed -gt 0) {
    Write-Host ""
    Write-Host "❌ 失败的测试:" -ForegroundColor Red
    foreach ($error in $TestResults.Errors) {
        Write-Host "   • $error" -ForegroundColor Red
    }
}

$successRate = [math]::Round(($TestResults.Passed / $TestResults.Total) * 100, 2)
Write-Host ""
Write-Host "成功率: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

if ($TestResults.Failed -eq 0) {
    Write-Host ""
    Write-Host "🎉 所有测试通过！Tera Rust Launcher 准备就绪！" -ForegroundColor Green
    exit 0
} else {
    Write-Host ""
    Write-Host "⚠️  部分测试失败，请检查上述错误。" -ForegroundColor Yellow
    exit 1
}
