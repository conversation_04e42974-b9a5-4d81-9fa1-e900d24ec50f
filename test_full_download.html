<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全量下载测试 - <PERSON>ra Launcher</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .progress-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 10px 0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
            border-radius: 12px;
            position: relative;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }

        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-info { color: #0c5460; }
        .log-success { color: #155724; }
        .log-warning { color: #856404; }
        .log-error { color: #721c24; }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-idle { background: #6c757d; }
        .status-running { background: #007bff; animation: pulse 1s infinite; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .test-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }

        .test-info h4 {
            margin-top: 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Tera Launcher 全量下载测试</h1>
        
        <div class="test-info">
            <h4>📋 测试说明</h4>
            <p>此测试将验证从Gitee获取加密内容、解密网盘直链、并进行全量下载的完整流程。</p>
            <p><strong>测试流程</strong>: Gitee页面 → Base64解密 → 网盘解析 → 直接下载</p>
        </div>

        <div class="test-section">
            <h2>🔧 测试配置</h2>
            
            <div class="input-group">
                <label for="gitee-url">Gitee URL (包含Base64加密内容):</label>
                <input type="text" id="gitee-url" value="https://gitee.com/mangomu" 
                       placeholder="输入包含 [[ Base64内容 ]] 的Gitee页面URL">
            </div>
            
            <div class="input-group">
                <label for="output-path">下载保存路径:</label>
                <input type="text" id="output-path" value="./downloads/tera-client.zip" 
                       placeholder="输入文件保存路径">
            </div>
            
            <div class="controls">
                <button id="start-test-btn" onclick="startFullDownloadTest()">
                    🚀 开始全量下载测试
                </button>
                <button onclick="stopDownload()" disabled id="stop-btn">
                    ⏹️ 停止下载
                </button>
                <button onclick="clearLogs()">
                    🗑️ 清空日志
                </button>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 下载进度</h2>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="download-progress" class="progress-fill" style="width: 0%">
                        <div class="progress-text" id="progress-text">0%</div>
                    </div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-label">下载状态</div>
                        <div class="stat-value">
                            <span id="status-indicator" class="status-indicator status-idle"></span>
                            <span id="download-status">等待开始</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">已下载</div>
                        <div class="stat-value" id="downloaded-size">0 MB</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">总大小</div>
                        <div class="stat-value" id="total-size">未知</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">下载速度</div>
                        <div class="stat-value" id="download-speed">0 KB/s</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">剩余时间</div>
                        <div class="stat-value" id="eta">未知</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">当前URL</div>
                        <div class="stat-value" id="current-url" style="font-size: 12px; word-break: break-all;">未知</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 测试日志</h2>
            <div id="log-container" class="log-container"></div>
        </div>
    </div>

    <script>
        let isDownloading = false;
        let currentTaskId = null;

        // 模拟Tauri invoke函数（用于测试）
        function mockInvoke(command, args) {
            console.log('Mock invoke:', command, args);
            
            if (command === 'test_full_download_with_gitee_content') {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({
                            success: true,
                            step: "download_started",
                            message: "Full download started successfully",
                            update_info: {
                                version: "1.0.0",
                                download_urls_count: 1,
                                checksum: ""
                            }
                        });
                    }, 1000);
                });
            }
            
            return Promise.reject(new Error('Unknown command'));
        }

        // 使用真实的Tauri invoke或模拟函数
        const invoke = window.__TAURI__ ? window.__TAURI__.invoke : mockInvoke;
        const listen = window.__TAURI__ ? window.__TAURI__.event.listen : mockListen;

        function mockListen(eventName, callback) {
            console.log('Mock listen for event:', eventName);
            // 模拟进度事件
            if (eventName === 'full_download_progress') {
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    
                    callback({
                        payload: {
                            type: "full_download_progress",
                            task_id: "mock_task",
                            downloaded_bytes: progress * 1024 * 1024,
                            total_bytes: 100 * 1024 * 1024,
                            speed_bytes_per_sec: 1024 * 1024,
                            eta_seconds: (100 - progress) * 60 / 100,
                            current_url: "https://example.com/mock-download.zip",
                            progress_percentage: progress
                        }
                    });
                }, 500);
            }
            return Promise.resolve(() => {});
        }

        async function startFullDownloadTest() {
            const giteeUrl = document.getElementById('gitee-url').value.trim();
            const outputPath = document.getElementById('output-path').value.trim();
            
            if (!giteeUrl) {
                alert('请输入Gitee URL');
                return;
            }
            
            if (!outputPath) {
                alert('请输入下载保存路径');
                return;
            }
            
            isDownloading = true;
            updateDownloadStatus('running', '正在初始化...');
            
            document.getElementById('start-test-btn').disabled = true;
            document.getElementById('stop-btn').disabled = false;
            
            addLog('🚀 开始全量下载测试', 'info');
            addLog(`📍 Gitee URL: ${giteeUrl}`, 'info');
            addLog(`💾 保存路径: ${outputPath}`, 'info');
            
            try {
                // 监听下载进度事件
                await listen('full_download_progress', (event) => {
                    const progress = event.payload;
                    updateDownloadProgress(progress);
                });
                
                // 监听下载完成事件
                await listen('full_download_complete', (event) => {
                    const result = event.payload;
                    handleDownloadComplete(result);
                });
                
                // 开始下载
                const result = await invoke('test_full_download_with_gitee_content', {
                    giteeUrl: giteeUrl,
                    outputPath: outputPath
                });
                
                if (result.success) {
                    addLog('✅ 下载任务启动成功', 'success');
                    addLog(`📋 更新信息: 版本 ${result.update_info.version}, ${result.update_info.download_urls_count} 个下载源`, 'info');
                    currentTaskId = result.task_id;
                } else {
                    throw new Error(result.error || '下载启动失败');
                }
                
            } catch (error) {
                addLog(`❌ 下载测试失败: ${error.message}`, 'error');
                updateDownloadStatus('error', '下载失败');
                resetDownloadUI();
            }
        }

        function updateDownloadProgress(progress) {
            const percentage = progress.progress_percentage || 0;
            const downloadedMB = (progress.downloaded_bytes / (1024 * 1024)).toFixed(2);
            const totalMB = progress.total_bytes > 0 ? (progress.total_bytes / (1024 * 1024)).toFixed(2) : '未知';
            const speedKB = (progress.speed_bytes_per_sec / 1024).toFixed(1);
            const eta = progress.eta_seconds ? formatTime(progress.eta_seconds) : '未知';
            
            // 更新进度条
            document.getElementById('download-progress').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = `${percentage.toFixed(1)}%`;
            
            // 更新统计信息
            document.getElementById('downloaded-size').textContent = `${downloadedMB} MB`;
            document.getElementById('total-size').textContent = `${totalMB} MB`;
            document.getElementById('download-speed').textContent = `${speedKB} KB/s`;
            document.getElementById('eta').textContent = eta;
            document.getElementById('current-url').textContent = progress.current_url || '未知';
            
            updateDownloadStatus('running', `下载中 ${percentage.toFixed(1)}%`);
            
            // 添加进度日志（每10%记录一次）
            if (percentage % 10 < 1) {
                addLog(`📥 下载进度: ${percentage.toFixed(1)}% (${downloadedMB}MB/${totalMB}MB)`, 'info');
            }
        }

        function handleDownloadComplete(result) {
            isDownloading = false;
            
            if (result.success) {
                addLog('🎉 下载完成！', 'success');
                addLog(`📊 下载统计: ${(result.downloaded_bytes / (1024 * 1024)).toFixed(2)}MB, 耗时 ${result.duration_seconds.toFixed(1)}s`, 'success');
                addLog(`⚡ 平均速度: ${(result.average_speed / 1024).toFixed(1)} KB/s`, 'success');
                updateDownloadStatus('success', '下载完成');
            } else {
                addLog(`❌ 下载失败: ${result.error}`, 'error');
                updateDownloadStatus('error', '下载失败');
            }
            
            resetDownloadUI();
        }

        function stopDownload() {
            if (isDownloading) {
                addLog('⏹️ 用户取消下载', 'warning');
                updateDownloadStatus('idle', '已取消');
                resetDownloadUI();
            }
        }

        function updateDownloadStatus(status, text) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('download-status');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function resetDownloadUI() {
            document.getElementById('start-test-btn').disabled = false;
            document.getElementById('stop-btn').disabled = true;
            isDownloading = false;
            currentTaskId = null;
        }

        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span style="color: #666;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
            addLog('📋 日志已清空', 'info');
        }

        function formatTime(seconds) {
            if (seconds < 60) {
                return `${Math.round(seconds)}秒`;
            } else if (seconds < 3600) {
                return `${Math.round(seconds / 60)}分钟`;
            } else {
                return `${Math.round(seconds / 3600)}小时`;
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🧪 全量下载测试页面已加载', 'info');
            
            if (window.__TAURI__) {
                addLog('✅ 检测到Tauri环境，将使用真实API', 'success');
            } else {
                addLog('⚠️ 未检测到Tauri环境，将使用模拟数据进行测试', 'warning');
            }
        });
    </script>
</body>
</html>
