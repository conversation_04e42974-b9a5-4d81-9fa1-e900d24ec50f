// 增强的更新系统前端实现
// 这个文件展示了如何在现有前端代码基础上实现全量/增量更新UI

// ============================================================================
// 1. 更新模式选择器
// ============================================================================

class UpdateModeSelector {
    constructor(app) {
        this.app = app;
        this.currentUpdateInfo = null;
    }

    /**
     * 显示更新选择对话框
     * @param {Object} updateInfo - 更新信息
     * @returns {Promise<string>} 用户选择的更新模式
     */
    async showUpdateOptions(updateInfo) {
        this.currentUpdateInfo = updateInfo;
        
        const options = this.calculateUpdateOptions(updateInfo);
        
        return new Promise((resolve) => {
            this.createUpdateDialog(options, resolve);
        });
    }

    calculateUpdateOptions(updateInfo) {
        const {
            localVersion,
            serverVersion,
            filesStatus,
            filesToUpdate,
            estimatedSpeeds
        } = updateInfo;

        const incrementalSize = filesToUpdate.reduce((sum, file) => sum + file.size, 0);
        const fullSize = updateInfo.totalGameSize || incrementalSize * 3; // 估算
        const deltaSize = filesToUpdate
            .filter(f => f.delta_info)
            .reduce((sum, file) => sum + (file.delta_info?.delta_size || file.size), 0);

        return {
            incremental: {
                title: this.app.t("UPDATE_MODE_INCREMENTAL"),
                subtitle: this.app.t("UPDATE_MODE_INCREMENTAL_DESC"),
                icon: "🔄",
                stats: {
                    files: filesToUpdate.length,
                    size: this.formatBytes(incrementalSize),
                    time: this.estimateTime(incrementalSize, estimatedSpeeds.incremental),
                    bandwidth: this.formatBytes(incrementalSize)
                },
                pros: [
                    this.app.t("UPDATE_PRO_FASTER"),
                    this.app.t("UPDATE_PRO_BANDWIDTH"),
                    this.app.t("UPDATE_PRO_SETTINGS")
                ],
                cons: [
                    this.app.t("UPDATE_CON_COMPATIBILITY")
                ],
                recommended: filesStatus.health_score > 0.7,
                available: true
            },
            delta: {
                title: this.app.t("UPDATE_MODE_DELTA"),
                subtitle: this.app.t("UPDATE_MODE_DELTA_DESC"),
                icon: "⚡",
                stats: {
                    files: filesToUpdate.filter(f => f.delta_info).length,
                    size: this.formatBytes(deltaSize),
                    time: this.estimateTime(deltaSize, estimatedSpeeds.delta),
                    bandwidth: this.formatBytes(deltaSize)
                },
                pros: [
                    this.app.t("UPDATE_PRO_FASTEST"),
                    this.app.t("UPDATE_PRO_MINIMAL_BANDWIDTH"),
                    this.app.t("UPDATE_PRO_SMART")
                ],
                cons: [
                    this.app.t("UPDATE_CON_REQUIRES_BASE")
                ],
                recommended: filesStatus.health_score > 0.8 && deltaSize < incrementalSize * 0.3,
                available: filesToUpdate.some(f => f.delta_info)
            },
            full: {
                title: this.app.t("UPDATE_MODE_FULL"),
                subtitle: this.app.t("UPDATE_MODE_FULL_DESC"),
                icon: "🔄",
                stats: {
                    files: updateInfo.totalFiles || filesToUpdate.length * 2,
                    size: this.formatBytes(fullSize),
                    time: this.estimateTime(fullSize, estimatedSpeeds.full),
                    bandwidth: this.formatBytes(fullSize)
                },
                pros: [
                    this.app.t("UPDATE_PRO_COMPLETE"),
                    this.app.t("UPDATE_PRO_RELIABLE"),
                    this.app.t("UPDATE_PRO_CLEAN")
                ],
                cons: [
                    this.app.t("UPDATE_CON_SLOW"),
                    this.app.t("UPDATE_CON_BANDWIDTH_HEAVY")
                ],
                recommended: filesStatus.health_score < 0.5 || serverVersion.force_full_update,
                available: true
            }
        };
    }

    createUpdateDialog(options, resolve) {
        const dialog = document.createElement('div');
        dialog.className = 'update-mode-dialog-overlay';
        dialog.innerHTML = `
            <div class="update-mode-dialog">
                <div class="dialog-header">
                    <h2>${this.app.t("UPDATE_MODE_SELECTION_TITLE")}</h2>
                    <p>${this.app.t("UPDATE_MODE_SELECTION_DESC")}</p>
                </div>
                
                <div class="update-options">
                    ${Object.entries(options).map(([mode, option]) => 
                        this.createOptionCard(mode, option)
                    ).join('')}
                </div>
                
                <div class="dialog-footer">
                    <button class="btn-secondary" id="update-cancel">
                        ${this.app.t("CANCEL")}
                    </button>
                    <button class="btn-primary" id="update-confirm" disabled>
                        ${this.app.t("START_UPDATE")}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
        this.setupDialogEvents(dialog, resolve);
    }

    createOptionCard(mode, option) {
        const recommendedBadge = option.recommended ? 
            `<span class="recommended-badge">${this.app.t("RECOMMENDED")}</span>` : '';
        
        const disabledClass = !option.available ? 'disabled' : '';
        
        return `
            <div class="update-option ${disabledClass}" data-mode="${mode}">
                <div class="option-header">
                    <span class="option-icon">${option.icon}</span>
                    <div class="option-title">
                        <h3>${option.title}</h3>
                        ${recommendedBadge}
                    </div>
                </div>
                
                <p class="option-subtitle">${option.subtitle}</p>
                
                <div class="option-stats">
                    <div class="stat">
                        <span class="stat-label">${this.app.t("FILES")}</span>
                        <span class="stat-value">${option.stats.files}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">${this.app.t("SIZE")}</span>
                        <span class="stat-value">${option.stats.size}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">${this.app.t("TIME")}</span>
                        <span class="stat-value">${option.stats.time}</span>
                    </div>
                </div>
                
                <div class="option-pros-cons">
                    <div class="pros">
                        <h4>${this.app.t("ADVANTAGES")}</h4>
                        <ul>
                            ${option.pros.map(pro => `<li>✅ ${pro}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="cons">
                        <h4>${this.app.t("CONSIDERATIONS")}</h4>
                        <ul>
                            ${option.cons.map(con => `<li>⚠️ ${con}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    setupDialogEvents(dialog, resolve) {
        let selectedMode = null;

        // 选项选择
        dialog.querySelectorAll('.update-option:not(.disabled)').forEach(option => {
            option.addEventListener('click', () => {
                dialog.querySelectorAll('.update-option').forEach(opt => 
                    opt.classList.remove('selected'));
                option.classList.add('selected');
                selectedMode = option.dataset.mode;
                dialog.querySelector('#update-confirm').disabled = false;
            });
        });

        // 确认按钮
        dialog.querySelector('#update-confirm').addEventListener('click', () => {
            if (selectedMode) {
                document.body.removeChild(dialog);
                resolve(selectedMode);
            }
        });

        // 取消按钮
        dialog.querySelector('#update-cancel').addEventListener('click', () => {
            document.body.removeChild(dialog);
            resolve('cancel');
        });

        // 默认选择推荐选项
        const recommendedOption = dialog.querySelector('.update-option.recommended');
        if (recommendedOption) {
            recommendedOption.click();
        }
    }

    formatBytes(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    estimateTime(bytes, speed) {
        const seconds = bytes / speed;
        if (seconds < 60) {
            return `${Math.ceil(seconds)}s`;
        } else if (seconds < 3600) {
            return `${Math.ceil(seconds / 60)}m`;
        } else {
            return `${Math.ceil(seconds / 3600)}h`;
        }
    }
}

// ============================================================================
// 2. 增强的进度显示
// ============================================================================

class EnhancedProgressDisplay {
    constructor(app) {
        this.app = app;
        this.currentUpdateMethod = null;
        this.startTime = null;
        this.lastProgressUpdate = null;
    }

    initializeProgress(updateMethod, totalFiles, totalSize) {
        this.currentUpdateMethod = updateMethod;
        this.startTime = Date.now();
        this.lastProgressUpdate = Date.now();

        this.updateProgressHeader(updateMethod);
        this.resetProgressStats();
    }

    updateProgressHeader(updateMethod) {
        const headerElement = document.getElementById('update-progress-header');
        if (!headerElement) return;

        const methodInfo = {
            'full': {
                title: this.app.t("UPDATE_PROGRESS_FULL"),
                icon: "🔄",
                description: this.app.t("UPDATE_PROGRESS_FULL_DESC")
            },
            'incremental': {
                title: this.app.t("UPDATE_PROGRESS_INCREMENTAL"),
                icon: "⚡",
                description: this.app.t("UPDATE_PROGRESS_INCREMENTAL_DESC")
            },
            'delta': {
                title: this.app.t("UPDATE_PROGRESS_DELTA"),
                icon: "🚀",
                description: this.app.t("UPDATE_PROGRESS_DELTA_DESC")
            }
        };

        const info = methodInfo[updateMethod] || methodInfo['incremental'];
        
        headerElement.innerHTML = `
            <div class="progress-method-info">
                <span class="method-icon">${info.icon}</span>
                <div class="method-text">
                    <h3>${info.title}</h3>
                    <p>${info.description}</p>
                </div>
            </div>
        `;
    }

    updateProgress(progressData) {
        const {
            updateMethod,
            currentPhase,
            fileProgress,
            overallProgress,
            estimatedTime,
            currentSpeed,
            filesProcessed,
            totalFiles,
            currentFileName,
            bytesDownloaded,
            totalBytes
        } = progressData;

        this.updateProgressBars(fileProgress, overallProgress);
        this.updateProgressStats(progressData);
        this.updateCurrentFile(currentFileName, filesProcessed, totalFiles);
        this.updateSpeedAndTime(currentSpeed, estimatedTime);

        // 根据更新方法显示特定信息
        switch (updateMethod) {
            case 'incremental':
                this.updateIncrementalInfo(progressData);
                break;
            case 'delta':
                this.updateDeltaInfo(progressData);
                break;
            case 'full':
                this.updateFullUpdateInfo(progressData);
                break;
        }
    }

    updateIncrementalInfo(progressData) {
        const infoElement = document.getElementById('incremental-info');
        if (!infoElement) return;

        const {
            filesSkipped = 0,
            bandwidthSaved = 0,
            integrityChecks = 0
        } = progressData;

        infoElement.innerHTML = `
            <div class="update-info-grid">
                <div class="info-item">
                    <span class="info-label">${this.app.t("FILES_SKIPPED")}</span>
                    <span class="info-value">${filesSkipped}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("BANDWIDTH_SAVED")}</span>
                    <span class="info-value">${this.formatBytes(bandwidthSaved)}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("INTEGRITY_CHECKS")}</span>
                    <span class="info-value">${integrityChecks}</span>
                </div>
            </div>
        `;
    }

    updateDeltaInfo(progressData) {
        const infoElement = document.getElementById('delta-info');
        if (!infoElement) return;

        const {
            patchesApplied = 0,
            compressionRatio = 0,
            patchingSpeed = 0
        } = progressData;

        infoElement.innerHTML = `
            <div class="update-info-grid">
                <div class="info-item">
                    <span class="info-label">${this.app.t("PATCHES_APPLIED")}</span>
                    <span class="info-value">${patchesApplied}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("COMPRESSION_RATIO")}</span>
                    <span class="info-value">${(compressionRatio * 100).toFixed(1)}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("PATCHING_SPEED")}</span>
                    <span class="info-value">${this.formatBytes(patchingSpeed)}/s</span>
                </div>
            </div>
        `;
    }

    updateFullUpdateInfo(progressData) {
        const infoElement = document.getElementById('full-update-info');
        if (!infoElement) return;

        const {
            verificationsPassed = 0,
            backupsCreated = 0,
            cleanupOperations = 0
        } = progressData;

        infoElement.innerHTML = `
            <div class="update-info-grid">
                <div class="info-item">
                    <span class="info-label">${this.app.t("VERIFICATIONS_PASSED")}</span>
                    <span class="info-value">${verificationsPassed}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("BACKUPS_CREATED")}</span>
                    <span class="info-value">${backupsCreated}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">${this.app.t("CLEANUP_OPERATIONS")}</span>
                    <span class="info-value">${cleanupOperations}</span>
                </div>
            </div>
        `;
    }

    // ... 其他辅助方法
    formatBytes(bytes) {
        // 与 UpdateModeSelector 中的实现相同
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }
}

// ============================================================================
// 3. 集成到现有 App 对象
// ============================================================================

// 扩展现有的 App 对象
Object.assign(App, {
    updateModeSelector: null,
    enhancedProgressDisplay: null,

    initializeEnhancedUpdateSystem() {
        this.updateModeSelector = new UpdateModeSelector(this);
        this.enhancedProgressDisplay = new EnhancedProgressDisplay(this);
    },

    async showUpdateModeSelection(updateInfo) {
        if (!this.updateModeSelector) {
            this.initializeEnhancedUpdateSystem();
        }
        
        return await this.updateModeSelector.showUpdateOptions(updateInfo);
    },

    // 重写现有的 handleDownloadProgress 方法
    handleDownloadProgress(event) {
        // 调用原有逻辑
        const originalMethod = this.constructor.prototype.handleDownloadProgress;
        if (originalMethod) {
            originalMethod.call(this, event);
        }

        // 添加增强的进度显示
        if (this.enhancedProgressDisplay) {
            this.enhancedProgressDisplay.updateProgress(event.payload);
        }
    }
});

// 在应用初始化时调用
document.addEventListener('DOMContentLoaded', () => {
    App.initializeEnhancedUpdateSystem();
});
