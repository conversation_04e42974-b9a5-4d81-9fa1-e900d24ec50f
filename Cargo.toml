[package]
name = "tera-enhanced-download-test"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "test_enhanced_download"
path = "test_enhanced_download.rs"

[dependencies]
tokio = { version = "1.37.0", features = ["full"] }
reqwest = { version = "0.12.7", features = ["json"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
futures-util = "0.3"

[dev-dependencies]
tokio-test = "0.4"
