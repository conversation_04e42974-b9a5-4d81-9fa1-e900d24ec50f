# 测试登录修复
Write-Host "🔧 测试登录 API 修复" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host ""

$ServerUrl = "http://localhost:8080"

# 测试正确的登录
Write-Host "🔍 测试正确的登录凭据..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/tera/LauncherLoginAction" -Method POST -Body "login=admin&password=admin123" -ContentType "application/x-www-form-urlencoded"
    $json = $response.Content | ConvertFrom-Json
    
    Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor White
    Write-Host "   返回值: $($json.Return)" -ForegroundColor White
    Write-Host "   消息: $($json.Msg)" -ForegroundColor White
    Write-Host "   用户名: $($json.UserName)" -ForegroundColor White
    Write-Host "   角色数量: $($json.CharacterCount)" -ForegroundColor White
    Write-Host "   权限级别: $($json.Permission)" -ForegroundColor White
    Write-Host "   认证密钥: $($json.AuthKey)" -ForegroundColor White
    
    if ($json.Return -eq $true -and $json.Msg -eq "success") {
        Write-Host "   ✅ 登录 API 格式正确！" -ForegroundColor Green
    } else {
        Write-Host "   ❌ 登录响应格式有问题" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ 登录测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 测试错误的登录
Write-Host "🔍 测试错误的登录凭据..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/tera/LauncherLoginAction" -Method POST -Body "login=wronguser&password=wrongpass" -ContentType "application/x-www-form-urlencoded" -ErrorAction Stop
    Write-Host "   ❌ 错误：应该返回错误但返回了成功" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        try {
            $errorContent = $_.Exception.Response | Get-Content | ConvertFrom-Json
            Write-Host "   状态码: 401 (正确)" -ForegroundColor Green
            Write-Host "   返回值: $($errorContent.Return)" -ForegroundColor White
            Write-Host "   消息: $($errorContent.Msg)" -ForegroundColor White
            Write-Host "   ✅ 错误处理正确！" -ForegroundColor Green
        } catch {
            Write-Host "   ✅ 返回 401 错误 (正确)" -ForegroundColor Green
        }
    } else {
        Write-Host "   ❌ 预期 401 但得到 $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host ""

# 测试其他 API
Write-Host "🔍 测试其他 API..." -ForegroundColor Yellow

# 健康检查
try {
    $health = Invoke-WebRequest -Uri "$ServerUrl/health"
    Write-Host "   ✅ 健康检查: $($health.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 健康检查失败" -ForegroundColor Red
}

# 哈希文件
try {
    $hash = Invoke-WebRequest -Uri "$ServerUrl/tera/launcher/hash-file.json"
    Write-Host "   ✅ 哈希文件: $($hash.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ 哈希文件失败" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 测试完成！现在可以尝试在启动器中登录：" -ForegroundColor Cyan
Write-Host "   用户名: admin" -ForegroundColor White
Write-Host "   密码: admin123" -ForegroundColor White
Write-Host ""
Write-Host "   或者:" -ForegroundColor White
Write-Host "   用户名: testuser" -ForegroundColor White
Write-Host "   密码: testpass" -ForegroundColor White
