# Tera Rust Launcher Quick Start Script
Write-Host "Tera Rust Launcher Quick Start" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green
Write-Host ""

# Check if test server is running
Write-Host "Checking test server..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Test server is running" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Test server is not running. Starting it now..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please run the following command in a separate PowerShell window:" -ForegroundColor Cyan
    Write-Host "cd test-server && node server.js" -ForegroundColor White
    Write-Host ""
    Write-Host "Then press any key to continue..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

Write-Host ""

# Verify all files are in place
Write-Host "Verifying setup..." -ForegroundColor Cyan

$checks = @(
    @{ Path = "teralaunch\src-tauri\target\release\teralaunch.exe"; Name = "Launcher" },
    @{ Path = "teralaunch\src-tauri\target\release\tera_config.ini"; Name = "Config" },
    @{ Path = "C:\tera\Binaries\Tera.exe"; Name = "Game Files" }
)

$allGood = $true
foreach ($check in $checks) {
    if (Test-Path $check.Path) {
        Write-Host "✅ $($check.Name)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($check.Name) missing" -ForegroundColor Red
        $allGood = $false
    }
}

if (!$allGood) {
    Write-Host ""
    Write-Host "❌ Setup incomplete. Please run the full test script first:" -ForegroundColor Red
    Write-Host ".\test-setup-simple.ps1" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "🚀 Starting Tera Launcher..." -ForegroundColor Green
Write-Host ""
Write-Host "Login credentials:" -ForegroundColor Cyan
Write-Host "  Username: admin" -ForegroundColor White
Write-Host "  Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "  Or:" -ForegroundColor White
Write-Host "  Username: testuser" -ForegroundColor White
Write-Host "  Password: testpass" -ForegroundColor White
Write-Host ""

# Start the launcher
Start-Process -FilePath ".\teralaunch\src-tauri\target\release\teralaunch.exe" -WorkingDirectory (Get-Location)

Write-Host "✅ Launcher started!" -ForegroundColor Green
