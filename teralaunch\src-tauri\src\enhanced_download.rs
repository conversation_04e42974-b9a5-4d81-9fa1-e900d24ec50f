// 增强下载系统 - 生产版本
// 基于三Agent协作设计的最优下载系统

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use reqwest::Client;
use sha2::{Sha256, Digest};
use async_trait::async_trait;

// ============================================================================
// 1. 核心数据结构
// ============================================================================

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EnhancedDownloadConfig {
    pub gitee_url: String,
    pub weibo_url: String,
    pub retry_interval_seconds: u64,
    pub address_switch_interval_seconds: u64,
    pub max_retry_attempts: u32,
    pub download_timeout_seconds: u64,
    pub chunk_size: usize,
    pub max_concurrent_downloads: usize,
    pub enable_resume: bool,
    pub enable_chunked_download: bool,
    pub speed_limit: Option<u64>,
}

impl Default for EnhancedDownloadConfig {
    fn default() -> Self {
        Self {
            gitee_url: "https://gitee.com/mango_mu/test".to_string(),
            weibo_url: "".to_string(),
            retry_interval_seconds: 30,
            address_switch_interval_seconds: 30,
            max_retry_attempts: 5,
            download_timeout_seconds: 300,
            chunk_size: 1024 * 1024, // 1MB
            max_concurrent_downloads: 4,
            enable_resume: true,
            enable_chunked_download: true,
            speed_limit: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedUpdateInfo {
    pub encrypted_data: String,
    pub version: String,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DecryptedUpdateInfo {
    pub version: String,
    pub full_download_urls: Vec<String>,
    pub incremental_server_url: String,
    pub file_list_url: String,
    pub checksum: String,
}

#[derive(Debug, Clone)]
pub enum UpdateType {
    Full,        // 全量下载（ZIP压缩包）
    Incremental, // 增量下载（服务器文件）
}

#[derive(Debug, Clone)]
pub struct EnhancedDownloadTask {
    pub task_id: String,
    pub update_type: UpdateType,
    pub urls: Vec<String>,
    pub output_path: PathBuf,
    pub expected_checksum: Option<String>,
    pub current_url_index: usize,
    pub retry_count: u32,
    pub last_attempt: Option<Instant>,
}

#[derive(Debug, Clone, Serialize)]
pub struct EnhancedDownloadProgress {
    pub task_id: String,
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub speed_bytes_per_sec: u64,
    pub eta_seconds: Option<u64>,
    pub current_url: String,
    pub status: String,
}

#[derive(Debug, Clone, Serialize)]
pub struct EnhancedDownloadResult {
    pub task_id: String,
    pub success: bool,
    pub downloaded_bytes: u64,
    pub duration_seconds: f64,
    pub average_speed: u64,
    pub error: Option<String>,
}

// ============================================================================
// 2. 网页内容获取和解密模块
// ============================================================================

pub struct WebContentFetcher {
    client: Client,
    config: EnhancedDownloadConfig,
}

impl WebContentFetcher {
    pub fn new(config: EnhancedDownloadConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.download_timeout_seconds))
            .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .build()
            .expect("Failed to create HTTP client");

        Self { client, config }
    }

    /// 从 Gitee 或微博获取加密的更新信息
    pub async fn fetch_encrypted_update_info(&self) -> Result<EncryptedUpdateInfo, String> {
        // 首先尝试 Gitee
        match self.fetch_from_url(&self.config.gitee_url).await {
            Ok(content) => {
                if let Ok(info) = self.parse_encrypted_content(&content) {
                    return Ok(info);
                }
            }
            Err(e) => {
                log::warn!("Failed to fetch from Gitee: {}", e);
            }
        }

        // 如果 Gitee 失败，尝试微博
        if !self.config.weibo_url.is_empty() {
            match self.fetch_from_url(&self.config.weibo_url).await {
                Ok(content) => {
                    if let Ok(info) = self.parse_encrypted_content(&content) {
                        return Ok(info);
                    }
                }
                Err(e) => {
                    log::warn!("Failed to fetch from Weibo: {}", e);
                }
            }
        }

        Err("Failed to fetch update info from all sources".to_string())
    }

    async fn fetch_from_url(&self, url: &str) -> Result<String, String> {
        let response = self.client
            .get(url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if !response.status().is_success() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let content = response
            .text()
            .await
            .map_err(|e| format!("Failed to read response: {}", e))?;

        Ok(content)
    }

    fn parse_encrypted_content(&self, content: &str) -> Result<EncryptedUpdateInfo, String> {
        // 解析网页内容，提取加密的更新信息
        if let Some(start) = content.find("<!-- UPDATE_INFO_START -->") {
            if let Some(end) = content.find("<!-- UPDATE_INFO_END -->") {
                let encrypted_data = content[start + 26..end].trim().to_string();
                
                return Ok(EncryptedUpdateInfo {
                    encrypted_data,
                    version: "1.0.0".to_string(), // 需要从内容中解析
                    timestamp: chrono::Utc::now().timestamp() as u64,
                });
            }
        }

        Err("Failed to parse encrypted content".to_string())
    }

    /// 解密更新信息
    pub fn decrypt_update_info(&self, encrypted_info: &EncryptedUpdateInfo) -> Result<DecryptedUpdateInfo, String> {
        // 实现解密逻辑
        let decoded = base64::decode(&encrypted_info.encrypted_data)
            .map_err(|e| format!("Failed to decode: {}", e))?;
        
        let decrypted_str = String::from_utf8(decoded)
            .map_err(|e| format!("Invalid UTF-8: {}", e))?;
        
        let decrypted_info: DecryptedUpdateInfo = serde_json::from_str(&decrypted_str)
            .map_err(|e| format!("Failed to parse JSON: {}", e))?;
        
        Ok(decrypted_info)
    }
}

// ============================================================================
// 3. 智能下载管理器
// ============================================================================

pub struct EnhancedDownloadManager {
    client: Client,
    config: EnhancedDownloadConfig,
    web_fetcher: WebContentFetcher,
    active_tasks: HashMap<String, EnhancedDownloadTask>,
}

impl EnhancedDownloadManager {
    pub fn new(config: EnhancedDownloadConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.download_timeout_seconds))
            .user_agent("Tera-Launcher/1.0")
            .build()
            .expect("Failed to create download client");

        let web_fetcher = WebContentFetcher::new(config.clone());

        Self {
            client,
            config,
            web_fetcher,
            active_tasks: HashMap::new(),
        }
    }

    /// 执行全量下载（ZIP压缩包）
    pub async fn download_full_client(
        &mut self,
        output_path: PathBuf,
        progress_callback: impl Fn(EnhancedDownloadProgress) + Send + Sync + 'static,
    ) -> Result<EnhancedDownloadResult, String> {
        log::info!("Starting enhanced full client download");

        // 获取并解密更新信息
        let update_info = self.get_latest_update_info().await?;
        
        let task = EnhancedDownloadTask {
            task_id: "full_download".to_string(),
            update_type: UpdateType::Full,
            urls: update_info.full_download_urls,
            output_path,
            expected_checksum: Some(update_info.checksum),
            current_url_index: 0,
            retry_count: 0,
            last_attempt: None,
        };

        self.active_tasks.insert(task.task_id.clone(), task.clone());
        
        self.execute_download_with_fallback(task, progress_callback).await
    }

    /// 执行增量下载
    pub async fn download_incremental_updates(
        &mut self,
        files_to_update: Vec<crate::FileInfo>,
        progress_callback: impl Fn(EnhancedDownloadProgress) + Send + Sync + 'static,
    ) -> Result<Vec<EnhancedDownloadResult>, String> {
        log::info!("Starting enhanced incremental download for {} files", files_to_update.len());

        let update_info = self.get_latest_update_info().await?;
        let mut results = Vec::new();
        
        for (index, file_info) in files_to_update.iter().enumerate() {
            let task = EnhancedDownloadTask {
                task_id: format!("incremental_{}", index),
                update_type: UpdateType::Incremental,
                urls: vec![file_info.url.clone()],
                output_path: {
                    let game_path = crate::get_game_path()?;
                    game_path.join(&file_info.path)
                },
                expected_checksum: Some(file_info.hash.clone()),
                current_url_index: 0,
                retry_count: 0,
                last_attempt: None,
            };

            let result = self.execute_download_with_fallback(task, &progress_callback).await?;
            results.push(result);
        }

        Ok(results)
    }

    async fn get_latest_update_info(&self) -> Result<DecryptedUpdateInfo, String> {
        let encrypted_info = self.web_fetcher.fetch_encrypted_update_info().await?;
        self.web_fetcher.decrypt_update_info(&encrypted_info)
    }

    pub async fn execute_download_with_fallback(
        &mut self,
        mut task: EnhancedDownloadTask,
        progress_callback: impl Fn(EnhancedDownloadProgress) + Send + Sync,
    ) -> Result<EnhancedDownloadResult, String> {
        let start_time = Instant::now();
        
        loop {
            // 检查是否需要重新获取下载地址
            if task.current_url_index >= task.urls.len() {
                if task.retry_count >= self.config.max_retry_attempts {
                    return Ok(EnhancedDownloadResult {
                        task_id: task.task_id.clone(),
                        success: false,
                        downloaded_bytes: 0,
                        duration_seconds: start_time.elapsed().as_secs_f64(),
                        average_speed: 0,
                        error: Some("All download URLs failed and max retries exceeded".to_string()),
                    });
                }

                log::info!("All URLs failed, refreshing download addresses...");
                sleep(Duration::from_secs(self.config.retry_interval_seconds)).await;
                
                // 重新获取下载地址
                match self.get_latest_update_info().await {
                    Ok(update_info) => {
                        match task.update_type {
                            UpdateType::Full => {
                                task.urls = update_info.full_download_urls;
                            }
                            UpdateType::Incremental => {
                                // 增量下载的 URL 通常不变
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("Failed to refresh download addresses: {}", e);
                    }
                }
                
                task.current_url_index = 0;
                task.retry_count += 1;
                continue;
            }

            let current_url = &task.urls[task.current_url_index];
            log::info!("Attempting download from URL {}: {}", task.current_url_index + 1, current_url);

            match self.download_from_url(current_url, &task.output_path, &progress_callback, &task.task_id).await {
                Ok(downloaded_bytes) => {
                    // 验证下载文件
                    if let Some(expected_checksum) = &task.expected_checksum {
                        if let Ok(actual_checksum) = self.calculate_file_checksum(&task.output_path).await {
                            if actual_checksum == *expected_checksum {
                                log::info!("Download completed and verified successfully");
                                return Ok(EnhancedDownloadResult {
                                    task_id: task.task_id.clone(),
                                    success: true,
                                    downloaded_bytes,
                                    duration_seconds: start_time.elapsed().as_secs_f64(),
                                    average_speed: if start_time.elapsed().as_secs() > 0 {
                                        downloaded_bytes / start_time.elapsed().as_secs()
                                    } else {
                                        0
                                    },
                                    error: None,
                                });
                            } else {
                                log::warn!("Checksum mismatch, trying next URL");
                            }
                        }
                    } else {
                        log::info!("Download completed (no checksum verification)");
                        return Ok(EnhancedDownloadResult {
                            task_id: task.task_id.clone(),
                            success: true,
                            downloaded_bytes,
                            duration_seconds: start_time.elapsed().as_secs_f64(),
                            average_speed: if start_time.elapsed().as_secs() > 0 {
                                downloaded_bytes / start_time.elapsed().as_secs()
                            } else {
                                0
                            },
                            error: None,
                        });
                    }
                }
                Err(e) => {
                    log::warn!("Download failed from URL {}: {}", task.current_url_index + 1, e);
                }
            }

            // 切换到下一个 URL
            task.current_url_index += 1;
            task.last_attempt = Some(Instant::now());

            // 如果不是最后一个 URL，等待切换间隔
            if task.current_url_index < task.urls.len() {
                sleep(Duration::from_secs(self.config.address_switch_interval_seconds)).await;
            }
        }
    }

    async fn download_from_url(
        &self,
        url: &str,
        output_path: &PathBuf,
        progress_callback: &impl Fn(EnhancedDownloadProgress),
        task_id: &str,
    ) -> Result<u64, String> {
        // 创建输出目录
        if let Some(parent) = output_path.parent() {
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| format!("Failed to create directory: {}", e))?;
        }

        let response = self.client
            .get(url)
            .send()
            .await
            .map_err(|e| format!("Request failed: {}", e))?;

        if !response.status().is_success() {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let total_size = response.content_length().unwrap_or(0);
        let mut file = tokio::fs::File::create(output_path).await
            .map_err(|e| format!("Failed to create file: {}", e))?;

        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        let start_time = Instant::now();

        while let Some(chunk_result) = futures_util::StreamExt::next(&mut stream).await {
            let chunk = chunk_result
                .map_err(|e| format!("Stream error: {}", e))?;

            tokio::io::AsyncWriteExt::write_all(&mut file, &chunk).await
                .map_err(|e| format!("Write error: {}", e))?;

            downloaded += chunk.len() as u64;

            // 更新进度
            let elapsed = start_time.elapsed().as_secs_f64();
            let speed = if elapsed > 0.0 { downloaded as f64 / elapsed } else { 0.0 };
            let eta = if speed > 0.0 && total_size > downloaded {
                Some(((total_size - downloaded) as f64 / speed) as u64)
            } else {
                None
            };

            progress_callback(EnhancedDownloadProgress {
                task_id: task_id.to_string(),
                downloaded_bytes: downloaded,
                total_bytes: total_size,
                speed_bytes_per_sec: speed as u64,
                eta_seconds: eta,
                current_url: url.to_string(),
                status: "downloading".to_string(),
            });
        }

        tokio::io::AsyncWriteExt::flush(&mut file).await
            .map_err(|e| format!("Flush error: {}", e))?;

        Ok(downloaded)
    }

    async fn calculate_file_checksum(&self, file_path: &PathBuf) -> Result<String, String> {
        let mut file = tokio::fs::File::open(file_path).await
            .map_err(|e| format!("Failed to open file: {}", e))?;

        let mut hasher = Sha256::new();
        let mut buffer = vec![0u8; self.config.chunk_size];

        loop {
            let bytes_read = tokio::io::AsyncReadExt::read(&mut file, &mut buffer).await
                .map_err(|e| format!("Failed to read file: {}", e))?;

            if bytes_read == 0 {
                break;
            }

            hasher.update(&buffer[..bytes_read]);
        }

        Ok(format!("{:x}", hasher.finalize()))
    }
}
