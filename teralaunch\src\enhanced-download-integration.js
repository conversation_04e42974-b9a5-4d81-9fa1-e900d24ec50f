// 增强下载系统前端集成
// 将增强下载功能集成到现有的前端代码中

import { invoke } from '@tauri-apps/api/tauri';
import { listen } from '@tauri-apps/api/event';

// ============================================================================
// 1. 增强下载系统管理类
// ============================================================================

class EnhancedDownloadManager {
    constructor() {
        this.isInitialized = false;
        this.currentDownloads = new Map();
        this.progressCallbacks = new Map();
        this.config = null;
        
        // 设置事件监听器
        this.setupEventListeners();
    }

    // 初始化增强下载系统
    async initialize(config = {}) {
        try {
            const result = await invoke('initialize_enhanced_download_system', {
                giteeUrl: config.giteeUrl || 'https://gitee.com/mango_mu/test',
                weiboUrl: config.weiboUrl || '',
                maxConcurrent: config.maxConcurrent || 4,
                enableResume: config.enableResume !== false
            });

            this.isInitialized = true;
            console.log('✅ Enhanced download system initialized:', result);
            
            // 获取当前配置
            this.config = await this.getConfig();
            
            return { success: true, message: result };
        } catch (error) {
            console.error('❌ Failed to initialize enhanced download system:', error);
            return { success: false, error: error.toString() };
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听增强下载进度
        listen('enhanced_download_progress', (event) => {
            const progress = event.payload;
            this.handleProgressUpdate(progress);
        });

        // 监听增强下载完成
        listen('enhanced_download_complete', (event) => {
            const result = event.payload;
            this.handleDownloadComplete(result);
        });

        // 监听单文件下载进度
        listen('enhanced_single_file_progress', (event) => {
            const progress = event.payload;
            this.handleSingleFileProgress(progress);
        });
    }

    // 处理进度更新
    handleProgressUpdate(progress) {
        const { task_id, downloaded_bytes, total_bytes, speed_bytes_per_sec, eta_seconds, progress_percentage } = progress;
        
        // 更新当前下载状态
        this.currentDownloads.set(task_id, {
            ...progress,
            lastUpdate: Date.now()
        });

        // 调用注册的回调函数
        const callback = this.progressCallbacks.get(task_id);
        if (callback) {
            callback({
                taskId: task_id,
                downloadedBytes: downloaded_bytes,
                totalBytes: total_bytes,
                speedBytesPerSec: speed_bytes_per_sec,
                etaSeconds: eta_seconds,
                progressPercentage: progress_percentage,
                formattedSpeed: this.formatSpeed(speed_bytes_per_sec),
                formattedEta: this.formatEta(eta_seconds),
                formattedSize: this.formatBytes(downloaded_bytes, total_bytes)
            });
        }

        // 触发全局进度事件
        this.dispatchEvent('enhanced-download-progress', progress);
    }

    // 处理下载完成
    handleDownloadComplete(result) {
        console.log('📥 Enhanced download completed:', result);
        
        if (result.success) {
            console.log(`✅ Successfully downloaded ${result.successful_downloads}/${result.total_files} files`);
        } else {
            console.error(`❌ Download failed: ${result.error || 'Unknown error'}`);
        }

        // 清理完成的任务
        if (result.results) {
            result.results.forEach(taskResult => {
                this.currentDownloads.delete(taskResult.task_id);
                this.progressCallbacks.delete(taskResult.task_id);
            });
        }

        // 触发全局完成事件
        this.dispatchEvent('enhanced-download-complete', result);
    }

    // 处理单文件下载进度
    handleSingleFileProgress(progress) {
        console.log(`📄 Single file progress: ${progress.file_name} - ${progress.progress_percentage.toFixed(1)}%`);
        this.dispatchEvent('enhanced-single-file-progress', progress);
    }

    // 开始增强下载所有文件
    async downloadAllFiles(filesToUpdate, progressCallback) {
        if (!this.isInitialized) {
            throw new Error('Enhanced download system not initialized');
        }

        try {
            // 注册进度回调
            if (progressCallback) {
                filesToUpdate.forEach((file, index) => {
                    const taskId = `incremental_${index}`;
                    this.progressCallbacks.set(taskId, progressCallback);
                });
            }

            const result = await invoke('enhanced_download_all_files', {
                filesToUpdate: filesToUpdate
            });

            console.log('🚀 Enhanced download started:', result);
            return { success: true, message: result };
        } catch (error) {
            console.error('❌ Failed to start enhanced download:', error);
            return { success: false, error: error.toString() };
        }
    }

    // 下载单个文件
    async downloadSingleFile(fileInfo, progressCallback) {
        if (!this.isInitialized) {
            throw new Error('Enhanced download system not initialized');
        }

        try {
            // 注册进度回调
            if (progressCallback) {
                const taskId = `single_file_${Date.now()}`;
                this.progressCallbacks.set(taskId, progressCallback);
            }

            const result = await invoke('enhanced_update_single_file', {
                fileInfo: fileInfo
            });

            console.log('📄 Enhanced single file download result:', result);
            return result;
        } catch (error) {
            console.error('❌ Failed to download single file:', error);
            throw error;
        }
    }

    // 获取更新信息
    async fetchUpdateInfo() {
        try {
            const result = await invoke('fetch_enhanced_update_info');
            console.log('📋 Enhanced update info:', result);
            return result;
        } catch (error) {
            console.error('❌ Failed to fetch enhanced update info:', error);
            throw error;
        }
    }

    // 测试直连下载URL
    async testDirectDownloadUrl(url) {
        try {
            const result = await invoke('test_enhanced_direct_download_url', { url });
            console.log('🔗 Direct URL test result:', result);
            return result;
        } catch (error) {
            console.error('❌ Failed to test direct URL:', error);
            throw error;
        }
    }

    // 解析网盘直链
    async parseNetdiskDirectLink(shareLink, card, netdisk) {
        try {
            const result = await invoke('parse_enhanced_netdisk_direct_link', {
                shareLink,
                card,
                netdisk
            });
            console.log('🔗 Parsed direct link:', result);
            return result;
        } catch (error) {
            console.error('❌ Failed to parse netdisk link:', error);
            throw error;
        }
    }

    // 获取配置
    async getConfig() {
        try {
            const config = await invoke('get_enhanced_download_config');
            this.config = config;
            return config;
        } catch (error) {
            console.error('❌ Failed to get config:', error);
            throw error;
        }
    }

    // 更新配置
    async updateConfig(newConfig) {
        try {
            const result = await invoke('update_enhanced_download_config', {
                newConfig
            });
            console.log('⚙️ Config updated:', result);
            
            // 刷新本地配置
            this.config = await this.getConfig();
            
            return { success: true, message: result };
        } catch (error) {
            console.error('❌ Failed to update config:', error);
            return { success: false, error: error.toString() };
        }
    }

    // 获取下载统计
    async getStatistics() {
        try {
            const stats = await invoke('get_enhanced_download_statistics');
            return stats;
        } catch (error) {
            console.error('❌ Failed to get statistics:', error);
            throw error;
        }
    }

    // 工具方法：格式化速度
    formatSpeed(bytesPerSec) {
        if (bytesPerSec === 0) return '0 B/s';
        
        const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        const k = 1024;
        const i = Math.floor(Math.log(bytesPerSec) / Math.log(k));
        
        return parseFloat((bytesPerSec / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
    }

    // 工具方法：格式化ETA
    formatEta(seconds) {
        if (!seconds || seconds === 0) return '--';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    // 工具方法：格式化字节大小
    formatBytes(downloaded, total) {
        const formatSize = (bytes) => {
            if (bytes === 0) return '0 B';
            
            const units = ['B', 'KB', 'MB', 'GB'];
            const k = 1024;
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + units[i];
        };

        return `${formatSize(downloaded)} / ${formatSize(total)}`;
    }

    // 触发自定义事件
    dispatchEvent(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        window.dispatchEvent(event);
    }

    // 获取当前活动下载
    getActiveDownloads() {
        return Array.from(this.currentDownloads.values());
    }

    // 清理资源
    cleanup() {
        this.currentDownloads.clear();
        this.progressCallbacks.clear();
        this.isInitialized = false;
    }
}

// ============================================================================
// 2. 全局实例和导出
// ============================================================================

// 创建全局增强下载管理器实例
const enhancedDownloadManager = new EnhancedDownloadManager();

// 导出管理器和相关函数
export { enhancedDownloadManager };

// 便捷函数导出
export const initializeEnhancedDownload = (config) => enhancedDownloadManager.initialize(config);
export const downloadAllFilesEnhanced = (files, callback) => enhancedDownloadManager.downloadAllFiles(files, callback);
export const downloadSingleFileEnhanced = (file, callback) => enhancedDownloadManager.downloadSingleFile(file, callback);
export const fetchEnhancedUpdateInfo = () => enhancedDownloadManager.fetchUpdateInfo();
export const testDirectUrl = (url) => enhancedDownloadManager.testDirectDownloadUrl(url);
export const parseNetdiskLink = (shareLink, card, netdisk) => enhancedDownloadManager.parseNetdiskDirectLink(shareLink, card, netdisk);

// ============================================================================
// 3. 与现有系统的集成示例
// ============================================================================

// 示例：替换现有的下载函数
export async function enhancedDownloadAllFiles(filesToUpdate, progressCallback) {
    try {
        // 首先尝试使用增强下载系统
        if (enhancedDownloadManager.isInitialized) {
            console.log('🚀 Using enhanced download system');
            return await enhancedDownloadManager.downloadAllFiles(filesToUpdate, progressCallback);
        } else {
            // 如果增强系统未初始化，尝试初始化
            console.log('🔄 Initializing enhanced download system...');
            const initResult = await enhancedDownloadManager.initialize();
            
            if (initResult.success) {
                return await enhancedDownloadManager.downloadAllFiles(filesToUpdate, progressCallback);
            } else {
                console.warn('⚠️ Enhanced download system initialization failed, falling back to original system');
                // 这里可以调用原始的下载函数作为后备
                throw new Error('Enhanced download system not available');
            }
        }
    } catch (error) {
        console.error('❌ Enhanced download failed:', error);
        throw error;
    }
}

// 示例：在现有的更新检查中集成增强功能
export async function checkForUpdatesEnhanced() {
    try {
        // 使用增强系统获取更新信息
        const updateInfo = await fetchEnhancedUpdateInfo();
        
        if (updateInfo.success) {
            console.log('📋 Enhanced update info retrieved:', updateInfo);
            return updateInfo;
        } else {
            console.warn('⚠️ Enhanced update info failed, falling back to original method');
            // 这里可以调用原始的更新检查函数
            throw new Error('Enhanced update check not available');
        }
    } catch (error) {
        console.error('❌ Enhanced update check failed:', error);
        throw error;
    }
}

// 默认导出
export default enhancedDownloadManager;
