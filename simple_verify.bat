@echo off
echo ========================================
echo Tera Launcher 增强下载系统验证
echo ========================================
echo.

echo [1/5] 检查核心文件...
if exist "teralaunch\src-tauri\src\enhanced_download.rs" (
    echo ✅ enhanced_download.rs 存在
) else (
    echo ❌ enhanced_download.rs 缺失
    goto :error
)

if exist "teralaunch\src-tauri\src\enhanced_tauri_commands.rs" (
    echo ✅ enhanced_tauri_commands.rs 存在
) else (
    echo ❌ enhanced_tauri_commands.rs 缺失
    goto :error
)

if exist "teralaunch\src\enhanced-download-integration.js" (
    echo ✅ enhanced-download-integration.js 存在
) else (
    echo ❌ enhanced-download-integration.js 缺失
    goto :error
)

echo.
echo [2/5] 检查配置文件...
if exist "teralaunch\src-tauri\enhanced_download_config.json" (
    echo ✅ 配置文件存在
) else (
    echo ⚠️  配置文件不存在，将使用默认配置
)

echo.
echo [3/5] 检查依赖配置...
findstr /C:"async-trait" "teralaunch\src-tauri\Cargo.toml" >nul
if %errorlevel%==0 (
    echo ✅ async-trait 依赖已配置
) else (
    echo ❌ async-trait 依赖缺失
    goto :error
)

findstr /C:"base64" "teralaunch\src-tauri\Cargo.toml" >nul
if %errorlevel%==0 (
    echo ✅ base64 依赖已配置
) else (
    echo ❌ base64 依赖缺失
    goto :error
)

findstr /C:"uuid" "teralaunch\src-tauri\Cargo.toml" >nul
if %errorlevel%==0 (
    echo ✅ uuid 依赖已配置
) else (
    echo ❌ uuid 依赖缺失
    goto :error
)

echo.
echo [4/5] 检查主程序集成...
findstr /C:"enhanced_download" "teralaunch\src-tauri\src\main.rs" >nul
if %errorlevel%==0 (
    echo ✅ 主程序已集成增强下载模块
) else (
    echo ❌ 主程序未集成增强下载模块
    goto :error
)

findstr /C:"EnhancedDownloadState" "teralaunch\src-tauri\src\main.rs" >nul
if %errorlevel%==0 (
    echo ✅ 增强下载状态管理已集成
) else (
    echo ❌ 增强下载状态管理未集成
    goto :error
)

echo.
echo [5/5] 检查测试文件...
if exist "tests\web_content_fetcher_tests.rs" (
    echo ✅ WebContentFetcher 测试存在
) else (
    echo ⚠️  WebContentFetcher 测试不存在
)

if exist "tests\smart_download_manager_tests.rs" (
    echo ✅ SmartDownloadManager 测试存在
) else (
    echo ⚠️  SmartDownloadManager 测试不存在
)

if exist "tests\tauri_integration_tests.rs" (
    echo ✅ Tauri集成测试存在
) else (
    echo ⚠️  Tauri集成测试不存在
)

echo.
echo ========================================
echo ✅ 验证完成！增强下载系统已正确部署
echo ========================================
echo.
echo 下一步操作：
echo 1. 编译项目: cd teralaunch\src-tauri ^&^& cargo build
echo 2. 运行测试: cargo test
echo 3. 启动应用: cargo run
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 验证失败！请检查上述错误
echo ========================================
echo.
exit /b 1

:end
echo 验证脚本执行完成
pause
