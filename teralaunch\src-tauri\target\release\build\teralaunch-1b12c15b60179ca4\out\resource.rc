#pragma code_page(65001)
1 VERSIONINFO
FILEVERSION 0, 0, 6, 0
PRODUCTVERSION 0, 0, 6, 0
FILETYPE 0x1
FILESUBTYPE 0x0
FILEFLAGS 0x0
FILEOS 0x40004
FILEFLAGSMASK 0x3f
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "FileVersion", "0.0.6"
VALUE "ProductName", "teralauncher"
VALUE "FileDescription", "A Tauri Launcher for Tera Pserver"
VALUE "LegalCopyright", "Tera Launcher"
VALUE "ProductVersion", "0.0.6"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}
32512 ICON "\\\\?\\D:\\Takumi\\Tool\\tera-rust-launcher-main\\teralaunch\\src-tauri\\icons\\tera_server.ico"
1 24
{
" <assembly xmlns=""urn:schemas-microsoft-com:asm.v1"" manifestVersion=""1.0""> "
" <dependency> "
" <dependentAssembly> "
" <assemblyIdentity "
" type=""win32"" "
" name=""Microsoft.Windows.Common-Controls"" "
" version=""*******"" "
" processorArchitecture=""*"" "
" publicKeyToken=""6595b64144ccf1df"" "
" language=""*"" "
" /> "
" </dependentAssembly> "
" </dependency> "
" </assembly> "
}

