# 测试专用的 Cargo.toml 配置
# 包含所有测试依赖和配置

[package]
name = "tera-launcher-tests"
version = "0.1.0"
edition = "2021"
authors = ["TNC97"]
description = "Comprehensive test suite for Tera Launcher Download System"

# 测试配置
[[bin]]
name = "test_runner"
path = "tests/test_runner.rs"

[dependencies]
# 核心依赖 - 与主项目保持一致
tokio = { version = "1.37.0", features = ["full"] }
reqwest = { version = "0.12.7", features = ["json", "stream"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
base64 = "0.21"
sha2 = "0.10.8"
futures-util = "0.3"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }
zip = "0.6"

# Tauri 依赖
tauri = { version = "1", features = ["api-all"] }

# 测试专用依赖
[dev-dependencies]
# 异步测试支持
tokio-test = "0.4"

# HTTP 模拟
mockito = "1.2"

# 临时文件和目录
tempfile = "3.8"

# 断言增强
assert_matches = "1.5"
pretty_assertions = "1.4"

# 并发测试
futures = "0.3"

# 日志和调试
env_logger = "0.10"
log = "0.4"
tracing = "0.1"
tracing-subscriber = "0.3"

# 性能测试
criterion = { version = "0.5", features = ["html_reports"] }

# 内存泄漏检测
leak-detect-allocator = "0.1"

# 代码覆盖率
tarpaulin = "0.27"

# 属性测试
proptest = "1.4"
quickcheck = "1.0"

# 测试数据生成
fake = { version = "2.9", features = ["derive"] }
rand = "0.8"

# 网络测试
wiremock = "0.5"

# 文件系统测试
tempdir = "0.3"

# 时间测试
mock_instant = "0.3"

# 特性标志
[features]
default = ["full-tests"]

# 测试类型特性
full-tests = ["unit-tests", "integration-tests", "performance-tests"]
unit-tests = []
integration-tests = []
performance-tests = ["criterion"]
coverage-tests = ["tarpaulin"]

# 测试环境特性
mock-network = ["mockito", "wiremock"]
real-network = []

# 调试特性
debug-tests = ["env_logger", "tracing-subscriber"]
verbose-output = []

# 性能分析特性
memory-profiling = ["leak-detect-allocator"]
benchmark = ["criterion"]

[profile.test]
# 测试优化配置
opt-level = 1
debug = true
overflow-checks = true
lto = false
codegen-units = 16

[profile.bench]
# 基准测试优化
opt-level = 3
debug = false
lto = true
codegen-units = 1

# 测试配置
[package.metadata.test]
# 测试超时设置
timeout = "300s"

# 并发测试数量
test-threads = 4

# 测试输出格式
format = "pretty"

# 失败时是否继续
no-fail-fast = true

# 测试覆盖率配置
[package.metadata.tarpaulin]
# 覆盖率报告格式
out = ["Html", "Xml", "Json"]

# 排除的文件
exclude = [
    "tests/*",
    "benches/*",
    "examples/*"
]

# 包含的文件
include = [
    "src/*"
]

# 最小覆盖率阈值
fail-under = 80

# 基准测试配置
[[bench]]
name = "download_performance"
harness = false
path = "benches/download_performance.rs"

[[bench]]
name = "concurrent_downloads"
harness = false
path = "benches/concurrent_downloads.rs"

# 示例配置
[[example]]
name = "test_example"
path = "examples/test_example.rs"

# 自定义测试命令
[package.metadata.scripts]
# 运行所有测试
test-all = "cargo test --all-features"

# 运行单元测试
test-unit = "cargo test --features unit-tests"

# 运行集成测试
test-integration = "cargo test --features integration-tests --test integration_tests"

# 运行性能测试
test-performance = "cargo test --features performance-tests"

# 生成覆盖率报告
coverage = "cargo tarpaulin --features coverage-tests --out Html"

# 运行基准测试
benchmark = "cargo bench --features benchmark"

# 内存泄漏检测
memory-check = "cargo test --features memory-profiling"

# 详细测试输出
test-verbose = "cargo test --features verbose-output -- --nocapture"

# 测试特定模块
test-web-fetcher = "cargo test web_content_fetcher_tests"
test-download-manager = "cargo test smart_download_manager_tests"
test-tauri = "cargo test tauri_integration_tests"

# 并发测试
test-concurrent = "cargo test --features integration-tests -- --test-threads=1"

# 快速测试（跳过慢速测试）
test-quick = "cargo test --features unit-tests -- --skip slow"

# 环境变量配置
[package.metadata.env]
# 测试环境变量
RUST_LOG = "debug"
RUST_BACKTRACE = "1"
TEST_TIMEOUT = "300"
MOCK_SERVER_PORT = "8080"

# 测试数据目录
TEST_DATA_DIR = "tests/data"
TEST_OUTPUT_DIR = "tests/output"

# 网络测试配置
ENABLE_NETWORK_TESTS = "false"
MOCK_GITEE_URL = "http://localhost:8080/gitee"
MOCK_WEIBO_URL = "http://localhost:8080/weibo"

# 性能测试配置
PERFORMANCE_TEST_ITERATIONS = "100"
CONCURRENT_DOWNLOAD_COUNT = "10"
MAX_FILE_SIZE_MB = "100"

# 文档测试配置
[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

# 依赖检查配置
[package.metadata.audit]
# 安全审计配置
ignore = []

# 许可证检查
[package.metadata.license]
allow = [
    "MIT",
    "Apache-2.0",
    "BSD-3-Clause",
    "ISC",
    "Unicode-DFS-2016"
]

# 构建脚本
[build-dependencies]
# 如果需要构建时生成测试数据
serde_json = "1"

# 工作空间配置（如果是工作空间的一部分）
[workspace]
members = [
    ".",
    "tests",
    "benches"
]

# 补丁依赖（用于测试特定版本）
[patch.crates-io]
# 如果需要使用特定版本的依赖进行测试
# tokio = { git = "https://github.com/tokio-rs/tokio.git", branch = "master" }

# 目标特定依赖
[target.'cfg(windows)'.dev-dependencies]
winapi = { version = "0.3", features = ["wininet"] }

[target.'cfg(unix)'.dev-dependencies]
libc = "0.2"

# 条件编译配置
[package.metadata.conditional-compilation]
# 根据操作系统启用不同的测试
windows = ["winapi"]
unix = ["libc"]

# 测试数据配置
[package.metadata.test-data]
# 测试文件路径
mock-zip-files = "tests/data/mock_files"
test-configs = "tests/data/configs"
expected-outputs = "tests/data/expected"

# CI/CD 配置
[package.metadata.ci]
# GitHub Actions 配置
github-actions = true
coverage-threshold = 80
performance-regression-threshold = "10%"

# 测试矩阵
test-matrix = [
    { rust-version = "1.70", os = "ubuntu-latest" },
    { rust-version = "1.70", os = "windows-latest" },
    { rust-version = "1.70", os = "macos-latest" },
    { rust-version = "stable", os = "ubuntu-latest" },
    { rust-version = "beta", os = "ubuntu-latest" },
    { rust-version = "nightly", os = "ubuntu-latest" }
]
