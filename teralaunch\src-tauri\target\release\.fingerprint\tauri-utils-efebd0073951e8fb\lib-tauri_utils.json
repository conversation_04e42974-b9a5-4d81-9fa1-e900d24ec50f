{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 14523740934585808576, "profile": 2040997289075261528, "path": 907919114797877544, "deps": [[561782849581144631, "html5ever", false, 11341267906366050353], [2455542577989222201, "json_patch", false, 16513480194839033448], [3129130049864710036, "memchr", false, 15400894219084788294], [3353796506826114049, "thiserror", false, 6822031306346486717], [3354820211247072069, "serde_with", false, 1475856706879779663], [3851720982022213547, "semver", false, 3107634668915628016], [4704408070981680310, "serde_json", false, 7911045850117905746], [6262254372177975231, "kuchiki", false, 4227424158751615082], [6594257425096240790, "windows_version", false, 5093504555776712725], [6997837210367702832, "infer", false, 10203183171956608265], [7314894124883917868, "log", false, 17595646964979077647], [8145704335312028338, "glob", false, 13760738684759537197], [10774583742866405328, "phf", false, 9330085059413291522], [11269248247346606853, "url", false, 15232645414707203371], [12378581237762097513, "brotli", false, 8732288573391350373], [14097227645922246194, "ctor", false, 8337483057473758990], [15622660310229662834, "walkdir", false, 3029533222499710698], [17174345729924723953, "serde", false, 10483066152181423585], [18053436020821374870, "dunce", false, 15069243742515159317]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-efebd0073951e8fb\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}