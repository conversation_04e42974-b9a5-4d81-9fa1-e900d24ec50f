{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 13418454777403878664, "profile": 15657897354478470176, "path": 16454806223936796563, "deps": [[1144798529435568546, "tauri_utils", false, 7903705833100539925], [3100833517036967723, "uuid", false, 3269919355752741088], [3353796506826114049, "thiserror", false, 6358471964182336296], [4381063397040571828, "webview2_com", false, 17950373685392314976], [4405182208873388884, "http", false, 7948823251931111416], [4704408070981680310, "serde_json", false, 11199486250301317617], [7653476968652377684, "windows", false, 6375946999918238853], [8866577183823226611, "http_range", false, 17802822684938098769], [11269248247346606853, "url", false, 12915646722162745668], [11693073011723388840, "raw_window_handle", false, 5676378102289126459], [13208667028893622512, "rand", false, 18054765264797586153], [16534286206067577747, "build_script_build", false, 11764551667148991480], [17174345729924723953, "serde", false, 2591028312899638256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-cfaccc2bdb62f613\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}