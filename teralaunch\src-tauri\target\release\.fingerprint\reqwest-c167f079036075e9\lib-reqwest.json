{"rustc": 1842507548689473721, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2040997289075261528, "path": 7565996450976943501, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [264090853244900308, "sync_wrapper", false, 15681497232691445498], [1076501750996383263, "once_cell", false, 3317331631347182004], [1133100163585637996, "tower_service", false, 2974626735743812269], [4405182208873388884, "http", false, 10162976284368013428], [4704408070981680310, "serde_json", false, 7911045850117905746], [4800206021143169329, "pin_project_lite", false, 17094343566241858788], [7314894124883917868, "log", false, 17595646964979077647], [8405603588346937335, "winreg", false, 2687170480553072622], [8858041990736880586, "tokio", false, 15737707881525506531], [8915503303801890683, "http_body", false, 7254496216998061372], [9351479248917069247, "encoding_rs", false, 3662838284785351967], [9648403166091088614, "native_tls_crate", false, 4956322636387755523], [10229185211513642314, "mime", false, 9651018063964816897], [11269248247346606853, "url", false, 15232645414707203371], [11510052217253957479, "hyper", false, 4481948226216532972], [11913130400938634928, "futures_util", false, 7705237049435048172], [12186126227181294540, "tokio_native_tls", false, 10247613043657730443], [12367227501898450486, "hyper_tls", false, 1071875151557495355], [12588177665552295757, "futures_core", false, 2275492903305144867], [13809605890706463735, "h2", false, 15784903947987165521], [15966749864389115859, "tokio_util", false, 1319105293102712446], [16227728351758841112, "bytes", false, 15932244784508788198], [16253538096806714255, "ipnet", false, 18393770253228454667], [16311359161338405624, "rustls_pemfile", false, 9697493388661030122], [16542808166767769916, "serde_urlencoded", false, 991891918213886282], [17174345729924723953, "serde", false, 10483066152181423585], [18066890886671768183, "base64", false, 10462734816124331761]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-c167f079036075e9\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}