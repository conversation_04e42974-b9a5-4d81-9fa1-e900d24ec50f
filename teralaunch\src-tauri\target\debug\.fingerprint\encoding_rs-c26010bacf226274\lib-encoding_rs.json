{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 13561321753160342926, "profile": 15657897354478470176, "path": 14457076074517640637, "deps": [[10411997081178400487, "cfg_if", false, 11343332648042595495]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-c26010bacf226274\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}