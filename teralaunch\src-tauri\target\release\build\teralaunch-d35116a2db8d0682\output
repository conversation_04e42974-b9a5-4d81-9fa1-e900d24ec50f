cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-cfg=desktop
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\Takumi\Tool\tera-rust-launcher-main\teralaunch\src-tauri\target\release\build\teralaunch-d35116a2db8d0682\out/resource.lib
cargo:rustc-link-search=native=D:\Takumi\Tool\tera-rust-launcher-main\teralaunch\src-tauri\target\release\build\teralaunch-d35116a2db8d0682\out
cargo:rustc-link-arg=/NODEFAULTLIB:libvcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntime.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libcmtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrtd.lib
cargo:rustc-link-arg=/DEFAULTLIB:libcmt.lib
cargo:rustc-link-arg=/DEFAULTLIB:libvcruntime.lib
cargo:rustc-link-arg=/DEFAULTLIB:ucrt.lib
