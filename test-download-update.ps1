# Tera Rust Launcher 下载更新功能测试脚本
Write-Host "Tera Rust Launcher Download Update Test" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""

$ServerUrl = "http://localhost:8080"
$GamePath = "C:\tera"

# 函数：创建测试文件
function Create-TestFile {
    param(
        [string]$FilePath,
        [string]$Content = "Test file content"
    )
    
    $dir = Split-Path $FilePath -Parent
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    
    Set-Content -Path $FilePath -Value $Content -Encoding UTF8
    Write-Host "   Created: $FilePath" -ForegroundColor Gray
}

# 函数：修改文件内容以触发更新
function Modify-TestFile {
    param(
        [string]$FilePath,
        [string]$NewContent
    )
    
    if (Test-Path $FilePath) {
        Set-Content -Path $FilePath -Value $NewContent -Encoding UTF8
        Write-Host "   Modified: $FilePath" -ForegroundColor Yellow
    }
}

# 函数：删除文件以触发下载
function Remove-TestFile {
    param(
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        Remove-Item $FilePath -Force
        Write-Host "   Removed: $FilePath" -ForegroundColor Red
    }
}

# 1. 检查服务器状态
Write-Host "1. Checking server status..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/health" -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ Server is running" -ForegroundColor Green
    }
} catch {
    Write-Host "   ❌ Server is not running. Please start it first:" -ForegroundColor Red
    Write-Host "      cd test-server && node server.js" -ForegroundColor White
    exit 1
}

# 2. 检查哈希文件 API
Write-Host ""
Write-Host "2. Testing hash file API..." -ForegroundColor Cyan
try {
    $hashResponse = Invoke-WebRequest -Uri "$ServerUrl/tera/launcher/hash-file.json"
    $hashData = $hashResponse.Content | ConvertFrom-Json
    $fileCount = $hashData.files.Count
    Write-Host "   ✅ Hash file API working - $fileCount files listed" -ForegroundColor Green
    
    # 显示前几个文件
    Write-Host "   Files in hash list:" -ForegroundColor Gray
    $hashData.files | Select-Object -First 3 | ForEach-Object {
        Write-Host "     - $($_.path) (Size: $($_.size) bytes)" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ❌ Hash file API failed" -ForegroundColor Red
    exit 1
}

# 3. 创建测试场景
Write-Host ""
Write-Host "3. Setting up test scenarios..." -ForegroundColor Cyan

# 场景 1: 缺失文件 (需要下载)
$missingFile = "$GamePath\S1Game\Config\S1Engine.ini"
Remove-TestFile $missingFile

# 场景 2: 修改文件 (需要更新)
$modifiedFile = "$GamePath\Binaries\Tera.exe"
if (Test-Path $modifiedFile) {
    Modify-TestFile $modifiedFile "Modified content to trigger update"
}

# 场景 3: 正确文件 (无需更新)
$correctFile = "$GamePath\S1Game\Config\S1UI.ini"
Create-TestFile $correctFile "Correct file content"

Write-Host "   Test scenarios created:" -ForegroundColor Green
Write-Host "     - Missing file: $missingFile" -ForegroundColor Yellow
Write-Host "     - Modified file: $modifiedFile" -ForegroundColor Yellow
Write-Host "     - Correct file: $correctFile" -ForegroundColor Green

# 4. 测试文件下载 API
Write-Host ""
Write-Host "4. Testing file download API..." -ForegroundColor Cyan

$testDownloadFile = "S1Game/Config/S1Engine.ini"
$downloadUrl = "$ServerUrl/public/$testDownloadFile"

try {
    $downloadResponse = Invoke-WebRequest -Uri $downloadUrl
    if ($downloadResponse.StatusCode -eq 200) {
        Write-Host "   ✅ File download API working" -ForegroundColor Green
        Write-Host "     Downloaded $($downloadResponse.Content.Length) bytes" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ❌ File download API failed" -ForegroundColor Red
    Write-Host "     URL: $downloadUrl" -ForegroundColor Gray
}

# 5. 模拟启动器更新检查流程
Write-Host ""
Write-Host "5. Simulating launcher update check process..." -ForegroundColor Cyan

Write-Host "   Step 1: Get server hash file ✅" -ForegroundColor Green
Write-Host "   Step 2: Compare local files..." -ForegroundColor Yellow

# 检查每个文件的状态
$hashData.files | ForEach-Object {
    $serverFile = $_
    $localPath = Join-Path $GamePath $serverFile.path
    
    if (!(Test-Path $localPath)) {
        Write-Host "     📥 DOWNLOAD NEEDED: $($serverFile.path) (missing)" -ForegroundColor Red
    } else {
        $localSize = (Get-Item $localPath).Length
        if ($localSize -ne $serverFile.size) {
            Write-Host "     🔄 UPDATE NEEDED: $($serverFile.path) (size mismatch: local=$localSize, server=$($serverFile.size))" -ForegroundColor Yellow
        } else {
            Write-Host "     ✅ UP TO DATE: $($serverFile.path)" -ForegroundColor Green
        }
    }
}

# 6. 提供启动器测试指导
Write-Host ""
Write-Host "6. Launcher testing instructions:" -ForegroundColor Cyan
Write-Host "   1. Start the launcher:" -ForegroundColor White
Write-Host "      .\teralaunch\src-tauri\target\release\teralaunch.exe" -ForegroundColor Gray
Write-Host ""
Write-Host "   2. Login with test credentials:" -ForegroundColor White
Write-Host "      Username: admin" -ForegroundColor Gray
Write-Host "      Password: admin123" -ForegroundColor Gray
Write-Host ""
Write-Host "   3. The launcher should automatically:" -ForegroundColor White
Write-Host "      - Check for file updates" -ForegroundColor Gray
Write-Host "      - Download missing/modified files" -ForegroundColor Gray
Write-Host "      - Show download progress" -ForegroundColor Gray
Write-Host "      - Enable the LAUNCH GAME button when complete" -ForegroundColor Gray

# 7. 监控提示
Write-Host ""
Write-Host "7. Monitoring tips:" -ForegroundColor Cyan
Write-Host "   - Watch the launcher UI for download progress" -ForegroundColor White
Write-Host "   - Check the server console for API requests" -ForegroundColor White
Write-Host "   - Monitor file changes in $GamePath" -ForegroundColor White

# 8. 重置测试环境
Write-Host ""
Write-Host "8. To reset test environment:" -ForegroundColor Cyan
Write-Host "   Remove-Item '$GamePath' -Recurse -Force" -ForegroundColor Gray
Write-Host "   .\test-setup-simple.ps1" -ForegroundColor Gray

Write-Host ""
Write-Host "🎯 Test environment ready for download update testing!" -ForegroundColor Green
Write-Host "   Files needing update: $(($hashData.files | Where-Object { $file = Join-Path $GamePath $_.path; !(Test-Path $file) -or (Get-Item $file -ErrorAction SilentlyContinue).Length -ne $_.size }).Count)" -ForegroundColor Yellow
