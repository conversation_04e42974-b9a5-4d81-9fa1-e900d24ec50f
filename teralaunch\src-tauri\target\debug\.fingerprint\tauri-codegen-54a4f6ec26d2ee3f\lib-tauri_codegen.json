{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 3982420347758336739, "profile": 2225463790103693989, "path": 10616049240982650766, "deps": [[1144798529435568546, "tauri_utils", false, 6005736629602156391], [2455542577989222201, "json_patch", false, 9678803638844142298], [2537567469363538103, "proc_macro2", false, 2633387941319026123], [3100833517036967723, "uuid", false, 3269919355752741088], [3353796506826114049, "thiserror", false, 6358471964182336296], [3851720982022213547, "semver", false, 5988392385646627132], [4704408070981680310, "serde_json", false, 17047230535494805844], [5236433071915784494, "sha2", false, 5428279718264867848], [5503179159497403901, "ico", false, 10705636653593419292], [8800316697867969272, "regex", false, 16000668544008835899], [10828200157800006600, "png", false, 5211389836926670840], [12378581237762097513, "brotli", false, 9502345429090629169], [15622660310229662834, "walkdir", false, 5583928852631741149], [16437840124237027127, "quote", false, 8978455854684825129], [17174345729924723953, "serde", false, 2591028312899638256], [18066890886671768183, "base64", false, 6454561435246751240]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-54a4f6ec26d2ee3f\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}