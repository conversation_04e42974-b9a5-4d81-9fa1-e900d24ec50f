{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 9968487133993622824, "profile": 8731458305071235362, "path": 3107050944943690935, "deps": [[1076501750996383263, "once_cell", false, 11895857675154832601], [1314680128599551313, "reqwest", false, 966697835390546060], [1760623714118191065, "dotenv", false, 15522334084148677918], [2062481783838671931, "parking_lot", false, 11459691188455459850], [4704408070981680310, "serde_json", false, 11199486250301317617], [7016560594308609179, "prost", false, 7242415381470409777], [7141661585461469887, "env_logger", false, 15068807131066420533], [7314894124883917868, "log", false, 7275729046824401138], [8858041990736880586, "tokio", false, 5176513659765035562], [10020888071089587331, "<PERSON>ap<PERSON>", false, 13543834042161756466], [10625088794814739235, "build_script_build", false, 1966943313280074244], [15349051716154188138, "tokio_macros", false, 9941012071544335725], [16601927120328658422, "protobuf", false, 1857238579217575496], [17917672826516349275, "lazy_static", false, 15670739947279810315], [18156568664791550917, "prost_types", false, 11505532254791877435]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\teralib-88930e9fcf806434\\dep-lib-teralib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}