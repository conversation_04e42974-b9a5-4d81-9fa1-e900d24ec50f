/* http://meyerweb.com/eric/tools/css/reset/ 
   v2.0 | 20110126
   License: none (assets domain)
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}


.english-en1,
.region1 {
  text-decoration: none;
  position: relative;
  font-weight: 600;
  color: inherit;
  min-width: 46px;
}
.english-en1 {
  font-weight: 700;
  color: #fff;
  min-width: 86px;
  white-space: nowrap;
}
.region-select1 {
  border-bottom: 1px solid rgba(121, 126, 142, 0.25);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 10px 8px;
  gap: 9px;
  
}

.select-wrapper {
  position: relative;
  user-select: none;
}
.select-wrapper.disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
.select-wrapper select {
  display: none;
}
.select-styled {
  position: relative;
  font-weight: 600;
  color: #fff !important;
  background-color: #272727;
  padding: 8px 19px 8px 8px !important;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease-in;
}
.select-styled:after {
  content: '\25BC';
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
}
.select-options {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  left: 0;
  z-index: 999;
  margin: 0;
  padding: 0;
  list-style: none;
  background-color: #2e2e2e;
  border-radius: 0 0 4px 4px;

  
}
.select-options li {
  font-weight: 600;
  color: #fff !important;
  margin: 0;
  padding: 12px 0;
  text-indent: 15px;
  border-top: 1px solid #3e3e3e;
  transition: all 0.15s ease-in;
}
.select-options li:hover {
  background-color: #3e3e3e;
}



.minimize-icon1 {
  width: 15px;
  height: 2px;
  position: relative;

}
.btn-minimize1 {
  height: 16px;
  width: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transform: scale(1);
  transition: all 0.15s ease-in;

}

.btn-minimize1:hover {
  transform: scale(1.2);
}
.close-icon1 {
  height: 15px;
  width: 15px;
  position: relative;
}
.btn-close1 {
  height: 16px;
  width: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transform: scale(1);
  transition: all 0.15s ease-in;
}
.btn-close1:hover {
  transform: scale(1.2);
}

.app-btn-content,
.header1,
.app-btn {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.app-btn-content {
  flex-direction: row;
  gap: 18px;
}
.header1,
.app-btn {
  flex-direction: column;
  padding: 9.5px 0 0;
}
.header1 {
  align-self: stretch;
  flex-direction: row;
  justify-content: space-between;
  padding: 10.5px 5px 0.5px 51px;
  gap: 20px;
  text-align: left;
  font-size: 13px;
  color: #8c93a9;
  font-family: Raleway;
}
.tera-logo-icon {
  height: 127px;
  flex: 1;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  object-fit: cover;
}
.logo-background {
  width: 393.3px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 30px;
  box-sizing: border-box;
  max-width: 100%;
}
.patch-notes-2024-6-7 {
  position: relative;
}
.bug-fix {
  margin: 0;
}
.ruinous-manor-hard-nightmare {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  padding-left: 21px;
}
.bug-fix-ruinous-container {
  align-self: stretch;
  position: relative;
  font-size: 16px;
}
.learn-more {
  text-decoration: none;
  position: relative;
  font-size: 18px;
  font-weight: 700;
  font-family: Inter;
  color: #fff;
  text-align: left;
  display: inline-block;
  min-width: 100px;
}
.btn-show-more,
.text-conten {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.btn-show-more {
  cursor: pointer;
  border: 0;
  padding: 11px 31px;
  background-color: rgba(255, 255, 255, 0.13);
  backdrop-filter: blur(13px);
  border-radius: 10px;
  flex-direction: row;
  white-space: nowrap;
}
.btn-show-more:hover {
  background-color: rgba(230, 230, 230, 0.13);
}
.text-conten {
  width: 100%;
  flex-direction: column;
  gap: 23px;
  max-width: 100%;
}
.news-block {
  align-self: stretch;
  backdrop-filter: blur(16.5px);
  border-radius: 10px;
  box-sizing: border-box;
  padding: 10px 15px;
  min-height: 226px;
}
.logo-wrapper,
.news-block,
.news-block-parent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}
.logo-wrapper {
  align-self: stretch;
  gap: 45px;
}
.news-block-parent {

  padding: 56px 0 0;
  box-sizing: border-box;
}
.user-icon-one,
.user-icon-two {
  width: 8px;
  height: 8px;
  position: relative;
}
.user-icon-two {
  width: 16px;
  height: 6px;
}
.btn-user-avatar {
  width: 36.5px;
  height: 35px;
  backdrop-filter: blur(13px);
  border-radius: 60px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.02);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px 9px;
  gap: 4px;
  cursor: pointer;
  transform: scale(1);
  transition: all 0.15s ease-in;
}
.btn-user-avatar:hover {
  transform: scale(1.12);
}

.hey,
.username {
  text-decoration: none;
  position: relative;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  min-width: 36px;
}
.username {
  color: #fff;
  min-width: 85px;
}

.user-name-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-bottom: 15px;
}
.menu-divider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.08);
  margin: 10px 0;
}
.user-menu {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.menu-item {
  padding: 10px 15px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: background-color 0.2s ease;
  font-weight: 500;
  font-size: 12px;
}
.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.dropdown-panel {
  width: 200px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(16.5px);
  border-radius: 10px;
  background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.2) 61.5%), rgba(255, 255, 255, 0.01);
  padding: 15px;
}
.dropdown-panel-wrapper {
  position: absolute;
  top: 45px;
  right: 10px;
  display: none;
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
}
.dropdown-panel-wrapper.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}




.user-name-container-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}

.user-name-container-wrapper {
  padding: 0 51px;
}
.menu-divider {
  align-self: stretch;
  height: 1px;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  box-sizing: border-box;
}
.user-info-container {
  width: 229px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 0 12px;
  box-sizing: border-box;
  gap: 28px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.63);
}

.user-panel {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  gap: 9px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.64);
}
.home-content,
.home-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  max-width: 100%;
}
.home-content {
  flex: 1;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}
.home-wrapper {
  align-self: stretch;
  justify-content: flex-end;
  padding: 0 15px 0 0;
  box-sizing: border-box;
}
.dl-status-string {
  position: relative;
  font-weight: 600;
}
.content-parent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 5px 0 0;
  box-sizing: border-box;
  max-width: 100%;
}
.dl-status-percentage {
  position: relative;
  font-size: 24px;
  font-weight: 600;
  color: #ffcc3c;
  display: inline-block;
  min-width: 60px;
}
.content-parent-parent {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 5px;
  max-width: 100%;
}
.inner-progress {
  height: 14px;
  position: relative;
  box-shadow: 0 0 12px rgb(255 204 60 / 44%);
  border-radius: 0 1px 1px 0;
  background-color: #f7b700;
}
span#current-file {
  color: #49231a;
  font-size: 12px;
  display: flex !important;
  padding-left: 4px;
}
.outer-progress {
  align-self: stretch;
  background-color: rgba(247, 183, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.07);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 2px;
}
.game-progress,
.progress-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 100%;
}
.progress-container {
  align-self: stretch;
  justify-content: flex-start;
  gap: 14px;
}
.game-progress {
  flex: 1;
  justify-content: flex-end;
  padding: 0 0 16px;
  box-sizing: border-box;
  min-width: 430px;
}
.pause-icon {
  height: 27.2px;
  width: 23.3px;
  position: relative;
}
.btn-pause {
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0 12px;
  box-sizing: border-box;
}
.vector-icon {
  height: 27.4px;
  width: 21.5px;
  position: relative;
}
.btn-restart {
  height: 60px;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0 10px;
  box-sizing: border-box;
}
.launch-game {
  position: relative;
  font-size: 20px;
  font-family: Raleway;
  color: #49231a;
  text-align: left;
  font-weight: 600;
}
.btn-launch-game {
  cursor: pointer;
  display: flex;
  width: 237px;
  padding: 23px 15px;
  gap: 10px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  background: linear-gradient(0deg, #FFCC3C 0%, #FFCC3C 100%), url(<path-to-image>), lightgray 50% / cover no-repeat;
  box-shadow: inset -17px 0px 37px 0px rgba(116, 70, 14, 0.80), inset 17px 0px 37px 0px rgba(116, 70, 14, 0.80), 0px 0px 35px 0px rgba(255, 171, 46, 0.35);
  border: 2px solid rgba(116, 70, 14, 0.80);
  border-radius: 3px;
  filter: brightness(1);
  transition: all 250ms ease-in-out;

    
}
.btn-launch-game:hover {
  filter: brightness(1.2);
  transform: scale(1.04);
}
.btn-launch-game.disabled{
  opacity: 0.5;
  cursor: not-allowed;
  transition: none;
  transform: none;
  filter: grayscale(1);
}
.launch-game-parent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0 0 12px;
}
.launch-game-parent.disabled {
  opacity: 0.5;
  transition: none;
  cursor: not-allowed;
}

.game-progress-parent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-end;
  justify-content: flex-start;
  gap: 11.3px;
  max-width: 100%;
}
.dl-speed-number,
.dl-speed-string {
  position: relative;
  font-weight: 600;
  display: inline-block;
}

.dl-speed-number {
  min-width: 73px;
}
.speed-value-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 5px 0 0;
  color: #bdc2d3;
}
.tr-string,
.tr-time {
  position: relative;
  font-weight: 600;
  display: inline-block;
}

.tr-time {
  color: #bdc2d3;
  min-width: 64px;
}
.download-speed {
  width: 396px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 5px;
  max-width: 100%;
}
.client-version,
.v-10002 {
  position: relative;
  font-weight: 600;
  display: inline-block;
  min-width: 101px;
}
.v-10002 {
  color: #bdc2d3;
  min-width: 60px;
}
.client-version-container,
.client-version-parent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.client-version-parent {
  flex-direction: row;
  gap: 5px;
}
.client-version-container {
  flex-direction: column;
  padding: 1px 0 0;
  width: 227px;
}
.download-info {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  max-width: 100%;
  gap: 20px;
  margin-top: -2px;
}
.game-status,
.home-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 100%;
}
.game-status {
  width: 100%;
  align-items: flex-start;
  font-size: 15px;
  color: #8c93a9;
  font-family: Raleway;
  position: absolute;
  bottom: -185px;
  left: 0;
}
.home-container {
  flex: 1;
  align-items: flex-end;
  gap: 95px;
  text-align: left;
  font-size: 24px;
  color: #fff;
  font-family: Inter;
  position: relative;
}
.content,
.mainpage {
  display: flex;
  box-sizing: border-box;
}
.content {
  width: 1236px;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0 40px;
  max-width: 100%;
}
.mainpage {
  width: 1282px;
  height: 759px;
  display: flex;
  border-radius: 5px;
  flex-direction: column;
  justify-content: center;
  padding: 0 23px 38px 0;
  gap: 9px;
  background-image: url(./assets/<EMAIL>);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
  letter-spacing: normal;
  line-height: normal;
}




/*MODAL*/

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(16.5px);
  justify-content: center;
  align-items: center;
}



.modal h2 {
  color: rgb(255, 255, 255);
  margin-bottom: 20px;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
}

.modal-content {
  background-color: rgb(189 189 189 / 10%);

  padding: 30px;
  width: 50%;
  max-width: 500px;
  border-radius: 10px;
  box-shadow: 0 0 0px 3px rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  color: rgb(255 255 255 / 51%);
}
.close {
  color: #aaa;
  align-self: flex-end;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 20px;
  display: block;
}
.close:hover {
  color: #fff;
}
input[type="text"] {
  width: 100%;
  padding: 12px;
  margin: 10px 0;
  border: none;
  background-color: rgba(255, 255, 255, 0.089);
  color: rgba(255, 255, 255, 0.637);;
  border-radius: 5px;
  font-size: 16px;
  text-align: center;
}
input[type="text"]::placeholder {
  color: rgba(255, 255, 255, 0.212);
}
.notification {
  padding: 12px;
  margin-top: 15px;
  border-radius: 5px;
  display: none;
  text-align: center;
  width: 100%;
  font-size: 14px;
}
.success {
  background-color: rgba(0, 255, 0, 0.2);
  color: #7af77a;
}
.error {
  background-color: rgba(255, 0, 0, 0.2);
  color: #f76a6a;
}




.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex !important;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-modal.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background-color: #1a1a1a;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  color: white;
  transform: scale(0.8);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.loading-modal.show .loading-content {
  transform: scale(1);
  opacity: 1;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 18px;
  margin-bottom: 15px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.loading-modal.show .loading-text {
  opacity: 1;
  transform: translateY(0);
}

.loading-error {
  color: #ff6b6b;
}

.loading-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.action-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s ease, transform 0.2s ease;
  opacity: 0;
  transform: translateY(20px);
}

.loading-modal.show .action-button {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 0.2s;
}

.action-button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
}

.action-button:active {
  transform: translateY(0);
}

#loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



#log-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.8);
}

.log-modal-content {
  background-color: #1e1e1e;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  box-shadow: 0 0 20px rgba(0,0,0,0.5);
}

.log-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.log-modal-header h2 {
  color: #e0e0e0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
}

.log-modal-close {
  color: #e0e0e0;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.log-modal-close:hover {
  color: #ffffff;
}

#log-console {
  height: 400px;
  overflow-y: auto;
  background-color: #252526;
  padding: 15px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #d4d4d4;
  border-radius: 4px;
}

#log-console::-webkit-scrollbar {
  width: 12px;
}

#log-console::-webkit-scrollbar-track {
  background: #1e1e1e;
}

#log-console::-webkit-scrollbar-thumb {
  background-color: #3e3e3e;
  border-radius: 6px;
  border: 3px solid #1e1e1e;
}

.log-entry {
  margin-bottom: 5px;
}

.log-entry-time {
  color: #569cd6;
  margin-right: 10px;
}

.log-entry-level {
  font-weight: bold;
  margin-right: 5px;
}

.log-entry-level.info {
  color: #4EC9B0;  
}

.log-entry-level.debug {
  color: #DCDCAA;  
}

.log-entry-level.warn {
  color: #CE9178;  
}

.log-entry-level.error {
  color: #F44747;  
}

.log-entry-level.critical {
  color: #FF00FF;  
}

.log-entry-message {
  color: #d4d4d4;  
}




@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.10),
                0 0 0 2px rgba(255, 255, 255, 0.10);

  }
  50% {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.10),
                0 0 0 30px rgba(255, 255, 255, 0.00);

  }
  100% {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.10),
                0 0 0 2px rgba(255, 255, 255, 0);

  }
}

.slider-container {
  width: 400px;
  height: 200px;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.10);
  opacity: 100;
  transition: box-shadow 0.2s ease;
}

.slider-container.pulse {
  animation: pulse 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.news-slider {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.swiper-slide-active img {
  transform: scale(1);
}

.swiper-slide-prev img,
.swiper-slide-next img {
  transform: scale(1.1);
}

.slide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  color: white;
  transform: translateY(100%);
  transition: transform 1.5s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.swiper-slide-active .slide-content {
  transform: translateY(0);
}

.slide-content h3 {
  margin: 0 0 5px;
  font-size: 18px;
}

.slide-content p {
  margin: 0;
  font-size: 12px;
}

.swiper-button-next,
.swiper-button-prev {
  color: white;
}

.swiper-pagination-bullet {
  background: white;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  opacity: 1;
}


#first-launch-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.first-launch-modal-content {
  background-color: #1e1e1e;
  color: #ffffff;
  padding: 30px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  max-width: 400px;
  width: 90%;
}

.first-launch-modal-content h2 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #3498db;
}

.first-launch-modal-content p {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 25px;
}

.first-launch-modal-content button {
  background-color: #3498db;
  color: #ffffff;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.first-launch-modal-content button:hover {
  background-color: #2980b9;
}

.custom-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  color: #fff;
  z-index: 1001;
  font-size: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.custom-notification.show {
  opacity: 1;
  transform: translateY(0);
}

.custom-notification.success {
  background-color: rgba(0, 255, 0, 0.2);
  color: #7af77a;
}

.custom-notification.error {
  background-color: rgba(255, 30, 0, 0.2);
  color: #f77e7a;
}




.hash-progress-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  min-width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.hash-progress-modal-content {
  background-color: #1e1e1e;
  color: #ffffff;
  margin: 15% auto;
  padding: 30px;
  border-radius: 10px;
  width: 90%;
  min-width: 50%;
  max-width: 70%;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  text-align: center;
}


.hash-progress-bar-container {
  width: 100%;
  background-color: #333333;
  padding: 3px;
  border-radius: 5px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, .2);
  margin-top: 20px;
}

.hash-progress-bar {
  display: block;
  height: 24px;
  background-color: #3498db;
  border-radius: 3px;
  transition: width 0.4s ease;
  text-align: center;
  color: white;
  line-height: 24px;
  font-size: 14px;
}

  
#hash-file-current-file, #hash-file-progress-text {
  margin-top: 15px;
  font-size: 16px;
  color: #ffffff;
}

@keyframes hashModalFadeIn {
  from {opacity: 0; transform: translateY(-20px);}
  to {opacity: 1; transform: translateY(0);}
}

.hash-modal-fade-in {
  animation: hashModalFadeIn 0.3s ease-in;
}

#hash-success-message {
  background-color: rgba(0, 255, 0, 0.2);
  color: #7af77a;
  font-weight: bold;
  text-align: center;
  margin-top: 20px;
  padding: 10px;
  border-radius: 5px;
}


.hash-progress-modal-content h2 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #3498db;
}