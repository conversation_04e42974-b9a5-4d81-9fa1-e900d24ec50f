# 全量更新 vs 增量更新系统设计方案

## 🎯 设计目标

基于现有代码架构，设计一个支持全量更新和增量更新的灵活系统，提供更好的用户体验和更高的更新效率。

## 📋 核心概念定义

### 全量更新 (Full Update)
- **定义**: 重新下载所有游戏文件，无论本地文件状态
- **适用场景**: 
  - 首次安装
  - 大版本更新
  - 文件损坏严重
  - 用户主动选择

### 增量更新 (Incremental Update)
- **定义**: 只下载变更的文件
- **适用场景**:
  - 日常更新
  - 小版本更新
  - 热修复
  - 默认更新模式

## 🏗️ 系统架构设计

### 1. 版本管理系统

#### 1.1 版本信息结构
```rust
#[derive(Serialize, Deserialize, Clone, Debug)]
struct VersionInfo {
    major: u32,           // 主版本号
    minor: u32,           // 次版本号
    patch: u32,           // 补丁版本号
    build: u32,           // 构建号
    release_date: String, // 发布日期
    update_type: UpdateType, // 更新类型
    force_full_update: bool, // 是否强制全量更新
}

#[derive(Serialize, Deserialize, Clone, Debug)]
enum UpdateType {
    Major,      // 主版本更新
    Minor,      // 次版本更新
    Patch,      // 补丁更新
    Hotfix,     // 热修复
}
```

#### 1.2 版本比较逻辑
```rust
impl VersionInfo {
    fn compare(&self, other: &VersionInfo) -> UpdateRequirement {
        if self.major != other.major {
            UpdateRequirement::FullUpdate
        } else if self.minor != other.minor {
            UpdateRequirement::MajorIncremental
        } else if self.patch != other.patch {
            UpdateRequirement::MinorIncremental
        } else if self.build != other.build {
            UpdateRequirement::PatchIncremental
        } else {
            UpdateRequirement::None
        }
    }
}

#[derive(Debug, Clone)]
enum UpdateRequirement {
    None,
    PatchIncremental,
    MinorIncremental,
    MajorIncremental,
    FullUpdate,
}
```

### 2. 更新策略系统

#### 2.1 更新策略配置
```rust
#[derive(Serialize, Deserialize, Clone, Debug)]
struct UpdateConfig {
    strategy: UpdateStrategy,
    auto_update: bool,
    bandwidth_limit: Option<u64>, // bytes/sec
    retry_count: u32,
    timeout_seconds: u64,
    verify_integrity: bool,
    backup_enabled: bool,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
enum UpdateStrategy {
    Auto,           // 自动选择最优策略
    ForceIncremental, // 强制增量更新
    ForceFull,      // 强制全量更新
    UserChoice,     // 让用户选择
}
```

#### 2.2 更新决策引擎
```rust
struct UpdateDecisionEngine;

impl UpdateDecisionEngine {
    fn decide_update_method(
        local_version: &VersionInfo,
        server_version: &VersionInfo,
        config: &UpdateConfig,
        local_files_status: &FilesStatus
    ) -> UpdateMethod {
        match config.strategy {
            UpdateStrategy::ForceFull => UpdateMethod::Full,
            UpdateStrategy::ForceIncremental => UpdateMethod::Incremental,
            UpdateStrategy::UserChoice => UpdateMethod::UserChoice,
            UpdateStrategy::Auto => {
                self.auto_decide(local_version, server_version, local_files_status)
            }
        }
    }

    fn auto_decide(
        &self,
        local_version: &VersionInfo,
        server_version: &VersionInfo,
        files_status: &FilesStatus
    ) -> UpdateMethod {
        // 决策逻辑
        if server_version.force_full_update {
            return UpdateMethod::Full;
        }

        let requirement = local_version.compare(server_version);
        let corruption_rate = files_status.corruption_rate();

        match requirement {
            UpdateRequirement::FullUpdate => UpdateMethod::Full,
            UpdateRequirement::MajorIncremental if corruption_rate > 0.3 => UpdateMethod::Full,
            _ if corruption_rate > 0.5 => UpdateMethod::Full,
            _ => UpdateMethod::Incremental,
        }
    }
}
```

### 3. 文件管理系统增强

#### 3.1 文件分类系统
```rust
#[derive(Serialize, Deserialize, Clone, Debug)]
struct FileCategory {
    name: String,
    priority: u32,
    can_skip: bool,
    requires_restart: bool,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
struct EnhancedFileInfo {
    path: String,
    hash: String,
    size: u64,
    url: String,
    category: FileCategory,
    version_added: VersionInfo,
    compression: Option<CompressionInfo>,
    delta_info: Option<DeltaInfo>, // 增量更新信息
}

#[derive(Serialize, Deserialize, Clone, Debug)]
struct DeltaInfo {
    base_hash: String,    // 基础文件哈希
    delta_url: String,    // 增量文件URL
    delta_size: u64,      // 增量文件大小
    patch_algorithm: String, // 补丁算法 (bsdiff, xdelta等)
}
```

#### 3.2 增量文件生成
```rust
// 服务器端增量文件生成
struct DeltaGenerator;

impl DeltaGenerator {
    async fn generate_delta(
        &self,
        old_file: &Path,
        new_file: &Path,
        output: &Path
    ) -> Result<DeltaInfo, String> {
        // 使用 bsdiff 或类似算法生成增量文件
        let old_data = tokio::fs::read(old_file).await?;
        let new_data = tokio::fs::read(new_file).await?;
        
        let delta = self.create_delta(&old_data, &new_data)?;
        tokio::fs::write(output, &delta).await?;
        
        Ok(DeltaInfo {
            base_hash: calculate_hash(&old_data),
            delta_url: format!("/deltas/{}", output.file_name().unwrap().to_str().unwrap()),
            delta_size: delta.len() as u64,
            patch_algorithm: "bsdiff".to_string(),
        })
    }
}
```

### 4. 用户界面增强

#### 4.1 更新模式选择界面
```javascript
// 前端更新模式选择
class UpdateModeSelector {
    async showUpdateOptions(updateInfo) {
        const options = {
            incremental: {
                title: "增量更新 (推荐)",
                description: `只下载 ${updateInfo.changedFiles} 个变更文件`,
                size: formatBytes(updateInfo.incrementalSize),
                time: estimateTime(updateInfo.incrementalSize),
                pros: ["更快", "节省带宽", "保留用户设置"],
                cons: ["可能遇到兼容性问题"]
            },
            full: {
                title: "完整更新",
                description: "重新下载所有游戏文件",
                size: formatBytes(updateInfo.fullSize),
                time: estimateTime(updateInfo.fullSize),
                pros: ["确保完整性", "解决所有问题", "最新版本"],
                cons: ["下载时间长", "占用更多带宽"]
            }
        };

        return await this.showDialog(options);
    }
}
```

#### 4.2 进度显示增强
```javascript
// 增强的进度显示
class EnhancedProgressDisplay {
    updateProgress(progressData) {
        const {
            updateMethod,
            currentPhase,
            fileProgress,
            overallProgress,
            estimatedTime,
            currentSpeed,
            filesProcessed,
            totalFiles
        } = progressData;

        // 根据更新类型显示不同信息
        if (updateMethod === 'incremental') {
            this.showIncrementalProgress(progressData);
        } else {
            this.showFullUpdateProgress(progressData);
        }
    }

    showIncrementalProgress(data) {
        // 显示增量更新特有信息
        // - 跳过的文件数量
        // - 增量文件应用进度
        // - 节省的带宽
    }
}
```

## 🔧 实现建议

### 1. 配置文件扩展

#### 扩展 `config.json`
```json
{
    "LOGIN_ACTION_URL": "http://localhost:8080/tera/LauncherLoginAction",
    "HASH_FILE_URL": "http://localhost:8080/tera/launcher/hash-file.json",
    "FILE_SERVER_URL": "http://localhost:8080/public",
    "SERVER_LIST_URL": "http://localhost:8080/tera/ServerList.json?lang=en&sort=3",
    
    "VERSION_INFO_URL": "http://localhost:8080/tera/version.json",
    "DELTA_SERVER_URL": "http://localhost:8080/deltas",
    "UPDATE_CONFIG": {
        "strategy": "Auto",
        "auto_update": true,
        "bandwidth_limit": null,
        "retry_count": 3,
        "timeout_seconds": 30,
        "verify_integrity": true,
        "backup_enabled": true
    }
}
```

### 2. 新增 API 端点

#### 版本信息 API
```
GET /tera/version.json
Response: {
    "current": VersionInfo,
    "available_updates": [VersionInfo],
    "update_paths": {
        "1.0.0->1.1.0": {
            "method": "incremental",
            "delta_files": [...],
            "full_fallback": true
        }
    }
}
```

#### 增量文件 API
```
GET /deltas/{file_hash}.delta
Response: Binary delta file
```

### 3. 数据库设计 (服务器端)

```sql
-- 版本管理表
CREATE TABLE versions (
    id INTEGER PRIMARY KEY,
    major INTEGER NOT NULL,
    minor INTEGER NOT NULL,
    patch INTEGER NOT NULL,
    build INTEGER NOT NULL,
    release_date DATETIME NOT NULL,
    update_type TEXT NOT NULL,
    force_full_update BOOLEAN DEFAULT FALSE
);

-- 文件版本关联表
CREATE TABLE file_versions (
    id INTEGER PRIMARY KEY,
    version_id INTEGER REFERENCES versions(id),
    file_path TEXT NOT NULL,
    file_hash TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    category TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 增量文件表
CREATE TABLE delta_files (
    id INTEGER PRIMARY KEY,
    from_hash TEXT NOT NULL,
    to_hash TEXT NOT NULL,
    delta_path TEXT NOT NULL,
    delta_size INTEGER NOT NULL,
    algorithm TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🎯 实施优先级

### Phase 1: 基础版本管理
1. 实现版本信息结构
2. 添加版本比较逻辑
3. 扩展配置系统

### Phase 2: 更新策略
1. 实现更新决策引擎
2. 添加用户选择界面
3. 增强进度显示

### Phase 3: 增量更新
1. 实现增量文件生成
2. 添加增量应用逻辑
3. 优化带宽使用

### Phase 4: 高级功能
1. 断点续传
2. 并行下载
3. 智能缓存
4. 回滚机制

## 📊 性能对比

| 特性 | 当前系统 | 增强后系统 |
|------|----------|------------|
| 更新策略 | 固定增量 | 智能选择 |
| 版本管理 | 无 | 完整版本控制 |
| 用户选择 | 无 | 支持用户选择 |
| 带宽优化 | 基础 | 高级优化 |
| 错误恢复 | 基础 | 多层恢复机制 |
| 进度显示 | 基础 | 详细分类显示 |

这个设计在保持现有系统稳定性的基础上，提供了更灵活、更智能的更新机制。
