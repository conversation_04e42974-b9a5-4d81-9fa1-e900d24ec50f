# Tera Rust Launcher 下载更新流程详解

## 📋 流程概述

Tera Rust Launcher 采用基于文件哈希比较的增量更新系统，确保只下载需要更新的文件，提高更新效率。

## 🔄 完整流程步骤

### 1. 初始化阶段
```javascript
// 用户登录成功后触发
async initializeAndCheckUpdates(isFromLogin = false)
```

**触发条件**:
- 用户成功登录
- 用户点击刷新按钮
- 应用启动时检测到已登录状态

### 2. 文件检查阶段

#### 2.1 获取服务器哈希文件
```rust
async fn get_server_hash_file() -> Result<serde_json::Value, String>
```

**API 调用**: `GET /tera/launcher/hash-file.json`

**服务器响应格式**:
```json
{
  "files": [
    {
      "path": "S1Game/Config/S1Engine.ini",
      "hash": "abc123...",
      "size": 1024,
      "url": "http://localhost:8080/public/S1Game/Config/S1Engine.ini"
    }
  ]
}
```

#### 2.2 并行文件检查
```rust
async fn get_files_to_update(window: tauri::Window) -> Result<Vec<FileInfo>, String>
```

**检查逻辑**:
1. **文件存在性检查**: 本地文件是否存在
2. **文件大小检查**: 本地文件大小与服务器是否一致
3. **哈希值检查**: 使用 SHA-256 计算本地文件哈希并与服务器比较

**缓存机制**:
- 使用 `file_cache.json` 缓存文件哈希值
- 基于文件修改时间判断是否需要重新计算哈希
- 提高重复检查的性能

#### 2.3 进度报告
```rust
// 每处理 100 个文件或完成时发送进度事件
window.emit("file_check_progress", FileCheckProgress {
    current_file: String,
    progress: f64,
    current_count: usize,
    total_files: usize,
    elapsed_time: f64,
    files_to_update: usize,
})
```

### 3. 下载阶段

#### 3.1 下载决策
```javascript
if (filesToUpdate.length === 0) {
    // 无需更新，启用游戏按钮
    this.setState({ isUpdateAvailable: false });
} else {
    // 需要更新，开始下载流程
    await this.runPatchSystem(filesToUpdate);
}
```

#### 3.2 批量下载
```rust
async fn download_all_files(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    files_to_update: Vec<FileInfo>
) -> Result<Vec<u64>, String>
```

**下载特性**:
- **顺序下载**: 逐个文件下载，避免服务器压力
- **流式下载**: 使用 `bytes_stream()` 流式处理大文件
- **实时进度**: 每 100ms 更新一次下载进度
- **哈希验证**: 下载完成后验证文件完整性

#### 3.3 单文件下载流程
```rust
async fn update_file(
    app_handle: tauri::AppHandle,
    window: tauri::Window,
    file_info: FileInfo,
    total_files: usize,
    current_file_index: usize,
    total_size: u64,
    downloaded_size: u64,
) -> Result<u64, String>
```

**下载步骤**:
1. **创建目录**: 确保文件目录存在
2. **HTTP 请求**: 创建 GET 请求下载文件
3. **流式写入**: 分块读取并写入本地文件
4. **进度更新**: 实时计算下载速度和进度
5. **哈希验证**: 验证下载文件的 SHA-256 哈希值

### 4. 进度跟踪系统

#### 4.1 下载进度事件
```rust
struct ProgressPayload {
    file_name: String,           // 当前下载文件名
    progress: f64,               // 当前文件下载进度 (0-100)
    speed: f64,                  // 下载速度 (bytes/sec)
    downloaded_bytes: u64,       // 总已下载字节数
    total_bytes: u64,           // 总需下载字节数
    total_files: usize,         // 总文件数
    elapsed_time: f64,          // 已用时间 (秒)
    current_file_index: usize,  // 当前文件索引
}
```

#### 4.2 前端进度处理
```javascript
handleDownloadProgress(event) {
    const {
        file_name,
        progress,
        speed,
        downloaded_bytes,
        total_bytes,
        total_files,
        current_file_index,
    } = event.payload;

    // 计算剩余时间
    const timeRemaining = this.calculateGlobalTimeRemaining(
        downloaded_bytes,
        total_bytes,
        speed
    );

    // 更新 UI 状态
    this.setState({
        currentFileName: file_name,
        currentProgress: Math.min(100, progress),
        currentSpeed: speed,
        downloadedSize: downloaded_bytes,
        totalSize: total_bytes,
        timeRemaining: timeRemaining,
        currentUpdateMode: "download"
    });
}
```

## 🛡️ 错误处理和恢复

### 1. 网络错误
- **重试机制**: 自动重试失败的下载
- **超时处理**: 设置合理的请求超时时间
- **连接检查**: 下载前验证服务器连接

### 2. 文件完整性
- **哈希验证**: 每个文件下载后验证 SHA-256 哈希
- **大小检查**: 验证文件大小是否正确
- **损坏处理**: 哈希不匹配时重新下载

### 3. 磁盘空间
- **空间检查**: 下载前检查可用磁盘空间
- **清理机制**: 下载失败时清理临时文件

## 📊 性能优化

### 1. 并行处理
- **文件检查**: 使用 Rayon 并行处理文件哈希计算
- **进度更新**: 异步更新 UI，避免阻塞下载

### 2. 缓存策略
- **哈希缓存**: 缓存文件哈希值，避免重复计算
- **修改时间**: 基于文件修改时间判断缓存有效性

### 3. 内存管理
- **流式处理**: 大文件使用流式下载，控制内存使用
- **缓冲区**: 使用适当大小的缓冲区平衡性能和内存

## 🔧 配置参数

### 1. 更新间隔
```rust
// 进度更新间隔
Duration::from_millis(100)

// 下载延迟 (避免过度占用带宽)
tokio::time::sleep(Duration::from_millis(1)).await;
```

### 2. 忽略文件列表
```rust
let ignored_paths: HashSet<&str> = [
    "$Patch",
    "Binaries/cookies.dat",
    "S1Game/Logs",
    "S1Game/Screenshots",
    "S1Game/Config/S1Engine.ini",  // 用户配置文件
    "S1Game/Config/S1Game.ini",
    // ... 更多忽略文件
].iter().cloned().collect();
```

## 🎯 关键 API 端点

| 端点 | 方法 | 用途 | 响应格式 |
|------|------|------|----------|
| `/tera/launcher/hash-file.json` | GET | 获取文件哈希列表 | JSON |
| `/public/{file_path}` | GET | 下载游戏文件 | Binary |
| `/health` | GET | 服务器健康检查 | JSON |

## 🚀 使用示例

### 手动触发更新检查
```javascript
// 在浏览器控制台中
await App.initializeAndCheckUpdates(true);
```

### 监听下载进度
```javascript
// 设置进度监听器
listen("download_progress", (event) => {
    console.log(`下载进度: ${event.payload.progress.toFixed(2)}%`);
    console.log(`当前文件: ${event.payload.file_name}`);
    console.log(`下载速度: ${formatBytes(event.payload.speed)}/s`);
});
```

这个更新系统确保了游戏文件的完整性和最新性，同时提供了良好的用户体验和错误恢复能力。
