<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64 加解密工具 - <PERSON><PERSON> Launcher</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .tool-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .tool-section:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .tool-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.8em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 1.2em;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 1.1em;
        }

        .input-group textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #ced4da;
            border-radius: 10px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-group input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ced4da;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .input-group input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 140px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }

        .result-box {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 300px;
            overflow-y: auto;
            border: 2px solid #dee2e6;
        }

        .result-success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .result-error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .result-info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }

        .template-section {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .template-section h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .template-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .template-btn {
            padding: 8px 16px;
            background: #ffc107;
            color: #212529;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }

        .copy-btn {
            position: relative;
            overflow: hidden;
        }

        .copy-btn.copied {
            background: #28a745 !important;
        }

        .copy-btn.copied::after {
            content: '✓ 已复制';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #28a745;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .format-example {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }

        .format-example h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .format-example code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .main-content {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Base64 加解密工具</h1>
            <p>专为 Tera Launcher 设计的 Base64 加密解密工具</p>
        </div>

        <div class="main-content">
            <!-- JSON编辑器部分 -->
            <div class="tool-section">
                <h2><span class="section-icon">📝</span>JSON 数据编辑器</h2>

                <div class="template-section">
                    <h3>📋 快速模板</h3>
                    <div class="template-buttons">
                        <button class="template-btn" onclick="loadTemplate('basic')">基础模板</button>
                        <button class="template-btn" onclick="loadTemplate('full')">完整模板</button>
                        <button class="template-btn" onclick="loadTemplate('simple')">简单模板</button>
                        <button class="template-btn" onclick="loadTemplate('custom')">自定义模板</button>
                    </div>
                </div>

                <div class="input-group">
                    <label for="json-input">JSON 数据 (将被转换为 Base64):</label>
                    <textarea id="json-input" placeholder="在这里输入或编辑 JSON 数据..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="formatJSON()">
                        <span>🎨</span> 格式化 JSON
                    </button>
                    <button class="btn btn-secondary" onclick="validateJSON()">
                        <span>✅</span> 验证 JSON
                    </button>
                    <button class="btn btn-success" onclick="encodeToBase64()">
                        <span>🔐</span> 转换为 Base64
                    </button>
                </div>

                <div id="json-result" class="result-box result-info" style="display: none;">
                    等待操作...
                </div>
            </div>

            <!-- Base64编码部分 -->
            <div class="tool-section">
                <h2><span class="section-icon">🔐</span>Base64 编码</h2>

                <div class="input-group">
                    <label for="encode-input">原始文本 (将被编码为 Base64):</label>
                    <textarea id="encode-input" placeholder="在这里输入要编码的文本..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="encodeText()">
                        <span>🔐</span> 编码为 Base64
                    </button>
                    <button class="btn btn-secondary" onclick="clearEncode()">
                        <span>🗑️</span> 清空
                    </button>
                </div>

                <div class="input-group">
                    <label for="encoded-output">Base64 编码结果:</label>
                    <textarea id="encoded-output" readonly placeholder="编码结果将显示在这里..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-success copy-btn" onclick="copyToClipboard('encoded-output')">
                        <span>📋</span> 复制编码结果
                    </button>
                    <button class="btn btn-warning" onclick="generateGiteeFormat()">
                        <span>🌐</span> 生成 Gitee 格式
                    </button>
                </div>
            </div>

            <!-- Base64解码部分 -->
            <div class="tool-section">
                <h2><span class="section-icon">🔓</span>Base64 解码</h2>

                <div class="input-group">
                    <label for="decode-input">Base64 编码文本 (将被解码):</label>
                    <textarea id="decode-input" placeholder="在这里输入要解码的 Base64 文本..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-primary" onclick="decodeText()">
                        <span>🔓</span> 解码 Base64
                    </button>
                    <button class="btn btn-secondary" onclick="clearDecode()">
                        <span>🗑️</span> 清空
                    </button>
                </div>

                <div class="input-group">
                    <label for="decoded-output">解码结果:</label>
                    <textarea id="decoded-output" readonly placeholder="解码结果将显示在这里..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-success copy-btn" onclick="copyToClipboard('decoded-output')">
                        <span>📋</span> 复制解码结果
                    </button>
                    <button class="btn btn-warning" onclick="formatDecodedJSON()">
                        <span>🎨</span> 格式化为 JSON
                    </button>
                </div>
            </div>

            <!-- Gitee格式生成器 -->
            <div class="tool-section">
                <h2><span class="section-icon">🌐</span>Gitee 格式生成器</h2>

                <div class="format-example">
                    <h4>📝 Gitee 页面格式示例</h4>
                    <p>将生成的内容按以下格式添加到您的 Gitee 页面：</p>
                    <code>[[ 这里是Base64编码内容 ]]</code>
                </div>

                <div class="input-group">
                    <label for="gitee-output">Gitee 页面格式:</label>
                    <textarea id="gitee-output" readonly placeholder="Gitee 格式将显示在这里..."></textarea>
                </div>

                <div class="button-group">
                    <button class="btn btn-success copy-btn" onclick="copyToClipboard('gitee-output')">
                        <span>📋</span> 复制 Gitee 格式
                    </button>
                    <button class="btn btn-warning" onclick="generateFullGiteePage()">
                        <span>📄</span> 生成完整页面
                    </button>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-grid" id="stats-grid" style="display: none;">
                <div class="stat-card">
                    <div class="stat-label">原始长度</div>
                    <div class="stat-value" id="original-length">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">编码长度</div>
                    <div class="stat-value" id="encoded-length">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">压缩比例</div>
                    <div class="stat-value" id="compression-ratio">0%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">字符编码</div>
                    <div class="stat-value" id="encoding-type">UTF-8</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模板数据
        const templates = {
            basic: {
                version: "1.0.0",
                full_download_urls: [
                    "https://gitee.com/your-repo/releases/download/v1.0.0/game.zip",
                    "https://backup1.example.com/game.zip",
                    "https://backup2.example.com/game.zip"
                ],
                incremental_server_url: "https://update.example.com/incremental",
                file_list_url: "https://update.example.com/files.json",
                checksum: "sha256:your-checksum-here"
            },
            full: {
                version: "2.1.5",
                release_date: "2024-06-29",
                full_download_urls: [
                    "https://gitee.com/mango_mu/tera-files/releases/download/v2.1.5/tera-client-full.zip",
                    "https://backup1.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
                    "https://backup2.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
                    "https://backup3.teralauncher.com/downloads/v2.1.5/tera-client-full.zip",
                    "https://backup4.teralauncher.com/downloads/v2.1.5/tera-client-full.zip"
                ],
                incremental_server_url: "https://update.teralauncher.com/incremental/v2.1.5",
                file_list_url: "https://update.teralauncher.com/api/v2.1.5/files.json",
                checksum: "sha256:a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
                download_info: {
                    total_size: 2147483648,
                    compressed_size: 1073741824,
                    file_count: 15420,
                    estimated_download_time: "15-30 minutes"
                },
                changelog: [
                    "修复了游戏启动时的崩溃问题",
                    "优化了下载速度和稳定性",
                    "增加了新的角色创建选项",
                    "修复了部分UI显示问题",
                    "提升了整体游戏性能"
                ],
                system_requirements: {
                    min_os: "Windows 10",
                    min_ram: "4GB",
                    min_storage: "50GB",
                    recommended_ram: "8GB",
                    recommended_storage: "100GB"
                }
            },
            simple: {
                version: "1.0.0",
                download_url: "https://example.com/game.zip",
                checksum: "abc123"
            },
            custom: {
                // 用户可以自定义的模板
                version: "x.x.x",
                description: "在这里添加您的自定义字段",
                custom_field: "自定义值"
            }
        };

        // 加载模板
        function loadTemplate(templateName) {
            const template = templates[templateName];
            if (template) {
                document.getElementById('json-input').value = JSON.stringify(template, null, 2);
                showResult('json-result', `✅ 已加载 ${templateName} 模板`, 'success');
            }
        }

        // 格式化JSON
        function formatJSON() {
            const input = document.getElementById('json-input').value.trim();
            if (!input) {
                showResult('json-result', '❌ 请输入JSON数据', 'error');
                return;
            }

            try {
                const parsed = JSON.parse(input);
                const formatted = JSON.stringify(parsed, null, 2);
                document.getElementById('json-input').value = formatted;
                showResult('json-result', '✅ JSON格式化成功', 'success');
            } catch (error) {
                showResult('json-result', `❌ JSON格式错误: ${error.message}`, 'error');
            }
        }

        // 验证JSON
        function validateJSON() {
            const input = document.getElementById('json-input').value.trim();
            if (!input) {
                showResult('json-result', '❌ 请输入JSON数据', 'error');
                return;
            }

            try {
                const parsed = JSON.parse(input);
                const keys = Object.keys(parsed);
                const size = new Blob([input]).size;

                showResult('json-result',
                    `✅ JSON验证通过\n` +
                    `📊 字段数量: ${keys.length}\n` +
                    `📏 数据大小: ${size} 字节\n` +
                    `🔑 主要字段: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`,
                    'success'
                );
            } catch (error) {
                showResult('json-result', `❌ JSON验证失败: ${error.message}`, 'error');
            }
        }

        // 从JSON编码为Base64
        function encodeToBase64() {
            const input = document.getElementById('json-input').value.trim();
            if (!input) {
                showResult('json-result', '❌ 请输入JSON数据', 'error');
                return;
            }

            try {
                // 验证JSON格式
                JSON.parse(input);

                // 编码为Base64
                const encoded = btoa(unescape(encodeURIComponent(input)));
                document.getElementById('encoded-output').value = encoded;

                // 更新统计信息
                updateStats(input, encoded);

                showResult('json-result',
                    `✅ JSON已成功编码为Base64\n` +
                    `📏 原始长度: ${input.length} 字符\n` +
                    `📏 编码长度: ${encoded.length} 字符`,
                    'success'
                );

                // 自动生成Gitee格式
                generateGiteeFormat();

            } catch (error) {
                showResult('json-result', `❌ 编码失败: ${error.message}`, 'error');
            }
        }

        // 编码文本为Base64
        function encodeText() {
            const input = document.getElementById('encode-input').value;
            if (!input) {
                alert('请输入要编码的文本');
                return;
            }

            try {
                const encoded = btoa(unescape(encodeURIComponent(input)));
                document.getElementById('encoded-output').value = encoded;

                updateStats(input, encoded);
                generateGiteeFormat();

            } catch (error) {
                alert(`编码失败: ${error.message}`);
            }
        }

        // 解码Base64文本
        function decodeText() {
            const input = document.getElementById('decode-input').value.trim();
            if (!input) {
                alert('请输入要解码的Base64文本');
                return;
            }

            try {
                const decoded = decodeURIComponent(escape(atob(input)));
                document.getElementById('decoded-output').value = decoded;

                // 尝试检测是否为JSON
                try {
                    JSON.parse(decoded);
                    document.getElementById('decoded-output').style.borderColor = '#28a745';
                } catch {
                    document.getElementById('decoded-output').style.borderColor = '#ced4da';
                }

            } catch (error) {
                alert(`解码失败: ${error.message}`);
            }
        }

        // 格式化解码后的JSON
        function formatDecodedJSON() {
            const decoded = document.getElementById('decoded-output').value;
            if (!decoded) {
                alert('请先解码Base64文本');
                return;
            }

            try {
                const parsed = JSON.parse(decoded);
                const formatted = JSON.stringify(parsed, null, 2);
                document.getElementById('decoded-output').value = formatted;
            } catch (error) {
                alert(`不是有效的JSON格式: ${error.message}`);
            }
        }

        // 生成Gitee格式
        function generateGiteeFormat() {
            const encoded = document.getElementById('encoded-output').value;
            if (!encoded) {
                return;
            }

            const giteeFormat = `[[ ${encoded} ]]`;
            document.getElementById('gitee-output').value = giteeFormat;
        }

        // 生成完整的Gitee页面
        function generateFullGiteePage() {
            const encoded = document.getElementById('encoded-output').value;
            if (!encoded) {
                alert('请先生成Base64编码内容');
                return;
            }

            const currentDate = new Date().toLocaleString('zh-CN');
            const fullPage = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>Tera Launcher 更新信息 - Gitee</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .update-info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .hidden-data { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Tera Launcher</h1>
            <h2>官方更新页面</h2>
            <p>最后更新时间: ${currentDate}</p>
        </div>

        <div class="update-info">
            <h3>📋 更新信息</h3>
            <p>这个页面包含Tera Launcher的最新更新信息。</p>
            <p>请使用官方启动器进行下载和更新。</p>
        </div>

        <!-- 隐藏的更新数据 -->
        <div class="hidden-data">
            [[ ${encoded} ]]
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>© 2024 Tera Launcher Team</p>
        </div>
    </div>
</body>
</html>`;

            // 创建下载链接
            const blob = new Blob([fullPage], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gitee_page.html';
            a.click();
            URL.revokeObjectURL(url);

            alert('✅ 完整的Gitee页面已生成并下载！');
        }

        // 复制到剪贴板
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.value;

            if (!text) {
                alert('没有内容可复制');
                return;
            }

            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功效果
                const btn = event.target.closest('.copy-btn');
                btn.classList.add('copied');
                setTimeout(() => {
                    btn.classList.remove('copied');
                }, 2000);
            }).catch(() => {
                // 备用复制方法
                element.select();
                document.execCommand('copy');
                alert('已复制到剪贴板');
            });
        }

        // 清空编码区域
        function clearEncode() {
            document.getElementById('encode-input').value = '';
            document.getElementById('encoded-output').value = '';
            document.getElementById('gitee-output').value = '';
            hideStats();
        }

        // 清空解码区域
        function clearDecode() {
            document.getElementById('decode-input').value = '';
            document.getElementById('decoded-output').value = '';
        }

        // 显示结果
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result-box result-${type}`;
            element.style.display = 'block';
        }

        // 更新统计信息
        function updateStats(original, encoded) {
            document.getElementById('original-length').textContent = original.length;
            document.getElementById('encoded-length').textContent = encoded.length;

            const ratio = ((encoded.length / original.length) * 100).toFixed(1);
            document.getElementById('compression-ratio').textContent = ratio + '%';

            document.getElementById('stats-grid').style.display = 'grid';
        }

        // 隐藏统计信息
        function hideStats() {
            document.getElementById('stats-grid').style.display = 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 Base64加解密工具已加载');

            // 加载默认模板
            loadTemplate('basic');

            // 添加拖拽上传功能
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    textarea.style.borderColor = '#667eea';
                });

                textarea.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    textarea.style.borderColor = '#ced4da';
                });

                textarea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    textarea.style.borderColor = '#ced4da';

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            textarea.value = e.target.result;
                        };
                        reader.readAsText(file);
                    }
                });
            });
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+Enter 快速编码
            if (e.ctrlKey && e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'json-input') {
                    encodeToBase64();
                } else if (activeElement.id === 'encode-input') {
                    encodeText();
                } else if (activeElement.id === 'decode-input') {
                    decodeText();
                }
            }

            // Ctrl+Shift+F 格式化JSON
            if (e.ctrlKey && e.shiftKey && e.key === 'F') {
                e.preventDefault();
                formatJSON();
            }
        });
    </script>
</body>
</html>