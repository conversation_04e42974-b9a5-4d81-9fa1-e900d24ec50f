// 集成测试 - 测试整个系统的端到端功能
// 验证三个Agent协作产生的代码的完整工作流程

#[cfg(test)]
mod integration_tests {
    use super::*;
    use crate::optimal_download_system::*;
    use crate::tauri_integration::*;
    use mockito::{mock, Matcher};
    use tempfile::TempDir;
    use std::fs::File;
    use std::io::Write;
    use std::time::{Duration, Instant};
    use tokio::time::sleep;

    // ============================================================================
    // 1. 完整的全量下载工作流程测试
    // ============================================================================

    #[tokio::test]
    async fn test_complete_full_download_workflow() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("client.zip");

        // 步骤1: 准备测试数据
        let test_zip_content = create_mock_zip_content();
        let expected_checksum = calculate_test_checksum(&test_zip_content);

        // 步骤2: 模拟Gitee响应（包含加密的下载地址）
        let update_info = create_mock_update_info(&mockito::server_url(), &expected_checksum);
        let encrypted_data = base64::encode(&update_info);

        let _gitee_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <html>
                    <head><title>Tera Update Info</title></head>
                    <body>
                        <div>Some page content</div>
                        <!-- UPDATE_INFO_START -->
                        {}
                        <!-- UPDATE_INFO_END -->
                        <div>More content</div>
                    </body>
                </html>
            "#, encrypted_data))
            .create();

        // 步骤3: 模拟多个下载源
        let _download_mock1 = mock("GET", "/download1/client.zip")
            .with_status(200)
            .with_header("content-length", &test_zip_content.len().to_string())
            .with_header("content-type", "application/zip")
            .with_body(&test_zip_content)
            .create();

        let _download_mock2 = mock("GET", "/download2/client.zip")
            .with_status(200)
            .with_header("content-length", &test_zip_content.len().to_string())
            .with_body(&test_zip_content)
            .create();

        // 步骤4: 初始化下载系统
        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            weibo_url: "".to_string(),
            address_switch_interval_seconds: 1, // 快速切换用于测试
            retry_interval_seconds: 2,
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);

        // 步骤5: 执行下载
        let mut progress_updates = Vec::new();
        let progress_callback = |progress: DownloadProgress| {
            progress_updates.push(progress);
        };

        let start_time = Instant::now();
        let result = manager.download_full_client(output_path.clone(), progress_callback).await;
        let elapsed = start_time.elapsed();

        // 步骤6: 验证结果
        assert!(result.is_ok(), "Download should succeed");
        assert!(output_path.exists(), "Downloaded file should exist");
        
        let downloaded_content = std::fs::read(&output_path).unwrap();
        assert_eq!(downloaded_content, test_zip_content.as_bytes());
        
        // 验证进度回调
        assert!(!progress_updates.is_empty(), "Progress callbacks should be called");
        let final_progress = progress_updates.last().unwrap();
        assert_eq!(final_progress.downloaded_bytes, final_progress.total_bytes);
        
        // 性能验证
        assert!(elapsed.as_secs() < 30, "Download should complete within 30 seconds");
        
        println!("✅ Complete full download workflow test passed in {:?}", elapsed);
    }

    // ============================================================================
    // 2. 多源故障切换测试
    // ============================================================================

    #[tokio::test]
    async fn test_multi_source_failover_workflow() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("failover_test.zip");

        let test_content = "Failover test content";
        let expected_checksum = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3";

        // 创建包含多个下载源的更新信息
        let update_info = format!(r#"{{
            "version": "1.0.0",
            "full_download_urls": [
                "{}/fail1/client.zip",
                "{}/fail2/client.zip", 
                "{}/success/client.zip"
            ],
            "incremental_server_url": "http://example.com",
            "file_list_url": "http://example.com/files",
            "checksum": "{}"
        }}"#, &mockito::server_url(), &mockito::server_url(), &mockito::server_url(), expected_checksum);

        let encrypted_data = base64::encode(&update_info);

        // 模拟Gitee响应
        let _gitee_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 第一个源失败 (404)
        let _fail_mock1 = mock("GET", "/fail1/client.zip")
            .with_status(404)
            .create();

        // 第二个源失败 (500)
        let _fail_mock2 = mock("GET", "/fail2/client.zip")
            .with_status(500)
            .create();

        // 第三个源成功
        let _success_mock = mock("GET", "/success/client.zip")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        // 配置快速切换间隔
        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            address_switch_interval_seconds: 1,
            retry_interval_seconds: 2,
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);

        // 跟踪尝试的URL
        let mut attempted_urls = Vec::new();
        let progress_callback = |progress: DownloadProgress| {
            if !attempted_urls.contains(&progress.current_url) {
                attempted_urls.push(progress.current_url.clone());
            }
        };

        let start_time = Instant::now();
        let result = manager.download_full_client(output_path.clone(), progress_callback).await;
        let elapsed = start_time.elapsed();

        // 验证故障切换成功
        assert!(result.is_ok(), "Download should succeed after failover");
        assert!(output_path.exists(), "File should be downloaded");
        
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
        
        // 验证尝试了多个URL
        assert!(attempted_urls.len() >= 2, "Should attempt multiple URLs");
        
        println!("✅ Multi-source failover test passed in {:?}", elapsed);
        println!("   Attempted URLs: {:?}", attempted_urls);
    }

    // ============================================================================
    // 3. 增量下载工作流程测试
    // ============================================================================

    #[tokio::test]
    async fn test_incremental_download_workflow() {
        let temp_dir = TempDir::new().unwrap();

        // 准备增量更新文件信息
        let files_to_update = vec![
            FileUpdateInfo {
                path: "config/game.ini".to_string(),
                hash: "config_hash_123".to_string(),
                size: 256,
                local_path: temp_dir.path().join("config").join("game.ini"),
            },
            FileUpdateInfo {
                path: "data/textures.pak".to_string(),
                hash: "texture_hash_456".to_string(),
                size: 1024,
                local_path: temp_dir.path().join("data").join("textures.pak"),
            },
            FileUpdateInfo {
                path: "scripts/main.lua".to_string(),
                hash: "script_hash_789".to_string(),
                size: 512,
                local_path: temp_dir.path().join("scripts").join("main.lua"),
            },
        ];

        // 模拟增量服务器响应
        let update_info = format!(r#"{{
            "version": "1.1.0",
            "full_download_urls": [],
            "incremental_server_url": "{}/incremental",
            "file_list_url": "{}/files",
            "checksum": ""
        }}"#, &mockito::server_url(), &mockito::server_url());

        let encrypted_data = base64::encode(&update_info);

        let _gitee_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 模拟各个文件的下载
        let _config_mock = mock("GET", "/incremental/config/game.ini")
            .with_status(200)
            .with_body("# Game Configuration\nresolution=1920x1080\nfullscreen=true")
            .create();

        let _texture_mock = mock("GET", "/incremental/data/textures.pak")
            .with_status(200)
            .with_body("TEXTURE_PACK_DATA_MOCK")
            .create();

        let _script_mock = mock("GET", "/incremental/scripts/main.lua")
            .with_status(200)
            .with_body("-- Main game script\nprint('Game started')")
            .create();

        // 执行增量下载
        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);

        let mut downloaded_files = Vec::new();
        let progress_callback = |progress: DownloadProgress| {
            if progress.downloaded_bytes == progress.total_bytes {
                downloaded_files.push(progress.current_url.clone());
            }
        };

        let start_time = Instant::now();
        let result = manager.download_incremental_updates(files_to_update.clone(), progress_callback).await;
        let elapsed = start_time.elapsed();

        // 验证增量下载结果
        assert!(result.is_ok(), "Incremental download should succeed");
        
        // 验证所有文件都被下载
        for file_info in &files_to_update {
            assert!(file_info.local_path.exists(), "File should be downloaded: {:?}", file_info.local_path);
        }
        
        // 验证文件内容
        let config_content = std::fs::read_to_string(&files_to_update[0].local_path).unwrap();
        assert!(config_content.contains("resolution=1920x1080"));
        
        let script_content = std::fs::read_to_string(&files_to_update[2].local_path).unwrap();
        assert!(script_content.contains("Game started"));
        
        println!("✅ Incremental download workflow test passed in {:?}", elapsed);
        println!("   Downloaded {} files", files_to_update.len());
    }

    // ============================================================================
    // 4. Tauri集成端到端测试
    // ============================================================================

    #[tokio::test]
    async fn test_tauri_integration_end_to_end() {
        let temp_dir = TempDir::new().unwrap();
        let state = DownloadState::new();

        // 步骤1: 初始化下载系统
        let init_result = initialize_download_system(
            tauri::State::from(&state),
            Some(format!("{}/mango_mu/test", &mockito::server_url())),
            Some("".to_string()),
        ).await;

        assert!(init_result.is_ok());

        // 步骤2: 模拟更新信息
        let update_info = r#"{"version":"2.0.0","full_download_urls":["http://example.com/client.zip"],"incremental_server_url":"http://example.com","file_list_url":"","checksum":"test_checksum"}"#;
        let encrypted_data = base64::encode(update_info);

        let _gitee_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 步骤3: 获取更新信息
        let fetch_result = fetch_update_info(tauri::State::from(&state)).await;
        
        assert!(fetch_result.is_ok());
        let update_response = fetch_result.unwrap();
        assert_eq!(update_response["success"], true);
        assert_eq!(update_response["version"], "2.0.0");

        // 步骤4: 测试直连下载URL
        let test_url = format!("{}/test_direct.zip", &mockito::server_url());
        
        let _direct_mock = mock("HEAD", "/test_direct.zip")
            .with_status(200)
            .with_header("content-length", "2048")
            .create();

        let test_result = test_direct_download_url(test_url).await;
        
        assert!(test_result.is_ok());
        let test_response = test_result.unwrap();
        assert_eq!(test_response["success"], true);
        assert_eq!(test_response["status_code"], 200);

        println!("✅ Tauri integration end-to-end test passed");
    }

    // ============================================================================
    // 5. 性能和并发测试
    // ============================================================================

    #[tokio::test]
    async fn test_concurrent_downloads_performance() {
        let temp_dir = TempDir::new().unwrap();
        let file_count = 10;
        let file_size = 1024; // 1KB per file

        // 创建多个模拟文件
        let mut mocks = Vec::new();
        let test_content = "x".repeat(file_size);

        for i in 0..file_count {
            let mock = mock("GET", &format!("/perf_file_{}.dat", i))
                .with_status(200)
                .with_header("content-length", &file_size.to_string())
                .with_body(&test_content)
                .create();
            mocks.push(mock);
        }

        // 创建文件更新信息
        let mut files_to_update = Vec::new();
        for i in 0..file_count {
            files_to_update.push(FileUpdateInfo {
                path: format!("perf_file_{}.dat", i),
                hash: format!("hash_{}", i),
                size: file_size as u64,
                local_path: temp_dir.path().join(format!("perf_file_{}.dat", i)),
            });
        }

        // 模拟更新信息
        let update_info = format!(r#"{{
            "version": "1.0.0",
            "full_download_urls": [],
            "incremental_server_url": "{}",
            "file_list_url": "",
            "checksum": ""
        }}"#, &mockito::server_url());

        let encrypted_data = base64::encode(&update_info);

        let _gitee_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        // 执行并发下载
        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);

        let mut completed_files = 0;
        let progress_callback = |progress: DownloadProgress| {
            if progress.downloaded_bytes == progress.total_bytes {
                completed_files += 1;
            }
        };

        let start_time = Instant::now();
        let result = manager.download_incremental_updates(files_to_update.clone(), progress_callback).await;
        let elapsed = start_time.elapsed();

        // 验证性能结果
        assert!(result.is_ok(), "Concurrent downloads should succeed");
        
        // 验证所有文件都被下载
        for file_info in &files_to_update {
            assert!(file_info.local_path.exists(), "File should exist: {:?}", file_info.local_path);
        }

        // 性能断言
        let total_bytes = file_count * file_size;
        let throughput = total_bytes as f64 / elapsed.as_secs_f64();
        
        println!("✅ Performance test completed:");
        println!("   Files: {}", file_count);
        println!("   Total size: {} bytes", total_bytes);
        println!("   Time: {:?}", elapsed);
        println!("   Throughput: {:.2} bytes/sec", throughput);
        
        assert!(elapsed.as_secs() < 30, "Should complete within 30 seconds");
        assert!(throughput > 1000.0, "Should achieve reasonable throughput");
    }

    // ============================================================================
    // 6. 错误恢复和重试测试
    // ============================================================================

    #[tokio::test]
    async fn test_error_recovery_and_retry() {
        let temp_dir = TempDir::new().unwrap();
        let output_path = temp_dir.path().join("retry_test.zip");

        let test_content = "Retry test content";
        let expected_checksum = "test_checksum_retry";

        // 创建会失败然后成功的更新信息
        let update_info = format!(r#"{{
            "version": "1.0.0",
            "full_download_urls": [
                "{}/retry_fail/client.zip",
                "{}/retry_success/client.zip"
            ],
            "incremental_server_url": "http://example.com",
            "file_list_url": "",
            "checksum": "{}"
        }}"#, &mockito::server_url(), &mockito::server_url(), expected_checksum);

        let encrypted_data = base64::encode(&update_info);

        // 第一次Gitee请求失败
        let _gitee_fail_mock = mock("GET", "/mango_mu/test")
            .with_status(500)
            .expect(1)
            .create();

        // 第二次Gitee请求成功
        let _gitee_success_mock = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .expect(1)
            .create();

        // 第一个下载源失败
        let _download_fail_mock = mock("GET", "/retry_fail/client.zip")
            .with_status(503)
            .create();

        // 第二个下载源成功
        let _download_success_mock = mock("GET", "/retry_success/client.zip")
            .with_status(200)
            .with_header("content-length", &test_content.len().to_string())
            .with_body(test_content)
            .create();

        // 配置快速重试
        let config = UpdateConfig {
            gitee_url: format!("{}/mango_mu/test", &mockito::server_url()),
            address_switch_interval_seconds: 1,
            retry_interval_seconds: 2,
            max_retry_attempts: 3,
            ..Default::default()
        };

        let mut manager = SmartDownloadManager::new(config);

        let mut retry_count = 0;
        let progress_callback = |_progress: DownloadProgress| {
            retry_count += 1;
        };

        let start_time = Instant::now();
        let result = manager.download_full_client(output_path.clone(), progress_callback).await;
        let elapsed = start_time.elapsed();

        // 验证错误恢复
        assert!(result.is_ok(), "Should recover from errors and succeed");
        assert!(output_path.exists(), "File should be downloaded after retry");
        
        let downloaded_content = std::fs::read_to_string(&output_path).unwrap();
        assert_eq!(downloaded_content, test_content);
        
        println!("✅ Error recovery and retry test passed in {:?}", elapsed);
        println!("   Retry attempts: {}", retry_count);
    }

    // ============================================================================
    // 辅助函数
    // ============================================================================

    fn create_mock_zip_content() -> String {
        // 创建模拟的ZIP文件内容
        "PK\x03\x04MOCK_ZIP_CONTENT_FOR_TESTING".to_string()
    }

    fn calculate_test_checksum(content: &str) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    fn create_mock_update_info(server_url: &str, checksum: &str) -> String {
        format!(r#"{{
            "version": "1.0.0",
            "full_download_urls": [
                "{}/download1/client.zip",
                "{}/download2/client.zip"
            ],
            "incremental_server_url": "{}/incremental",
            "file_list_url": "{}/files",
            "checksum": "{}"
        }}"#, server_url, server_url, server_url, server_url, checksum)
    }

    // ============================================================================
    // 7. 压力测试
    // ============================================================================

    #[tokio::test]
    async fn test_system_under_load() {
        let temp_dir = TempDir::new().unwrap();
        let concurrent_downloads = 5;
        let files_per_download = 3;

        // 为每个并发下载创建模拟数据
        let mut all_mocks = Vec::new();
        let mut all_tasks = Vec::new();

        for download_id in 0..concurrent_downloads {
            // 创建更新信息
            let update_info = format!(r#"{{
                "version": "1.0.{}",
                "full_download_urls": [],
                "incremental_server_url": "{}/load_test_{}",
                "file_list_url": "",
                "checksum": ""
            }}"#, download_id, &mockito::server_url(), download_id);

            let encrypted_data = base64::encode(&update_info);

            // Gitee模拟
            let gitee_mock = mock("GET", &format!("/load_test_{}/info", download_id))
                .with_status(200)
                .with_body(format!(r#"
                    <!-- UPDATE_INFO_START -->
                    {}
                    <!-- UPDATE_INFO_END -->
                "#, encrypted_data))
                .create();
            all_mocks.push(gitee_mock);

            // 文件下载模拟
            let mut files_to_update = Vec::new();
            for file_id in 0..files_per_download {
                let file_content = format!("Load test content {} - {}", download_id, file_id);
                
                let file_mock = mock("GET", &format!("/load_test_{}/file_{}.txt", download_id, file_id))
                    .with_status(200)
                    .with_body(&file_content)
                    .create();
                all_mocks.push(file_mock);

                files_to_update.push(FileUpdateInfo {
                    path: format!("file_{}_{}.txt", download_id, file_id),
                    hash: format!("hash_{}_{}", download_id, file_id),
                    size: file_content.len() as u64,
                    local_path: temp_dir.path().join(format!("file_{}_{}.txt", download_id, file_id)),
                });
            }

            // 创建下载任务
            let config = UpdateConfig {
                gitee_url: format!("{}/load_test_{}/info", &mockito::server_url(), download_id),
                ..Default::default()
            };

            let mut manager = SmartDownloadManager::new(config);
            let progress_callback = |_progress: DownloadProgress| {};

            let task = tokio::spawn(async move {
                manager.download_incremental_updates(files_to_update, progress_callback).await
            });

            all_tasks.push(task);
        }

        // 执行所有并发下载
        let start_time = Instant::now();
        let results = futures::future::join_all(all_tasks).await;
        let elapsed = start_time.elapsed();

        // 验证所有下载都成功
        for (i, result) in results.into_iter().enumerate() {
            assert!(result.is_ok(), "Task {} should complete successfully", i);
            assert!(result.unwrap().is_ok(), "Download {} should succeed", i);
        }

        // 验证所有文件都被创建
        let mut total_files = 0;
        for download_id in 0..concurrent_downloads {
            for file_id in 0..files_per_download {
                let file_path = temp_dir.path().join(format!("file_{}_{}.txt", download_id, file_id));
                assert!(file_path.exists(), "File should exist: {:?}", file_path);
                total_files += 1;
            }
        }

        println!("✅ System under load test completed:");
        println!("   Concurrent downloads: {}", concurrent_downloads);
        println!("   Files per download: {}", files_per_download);
        println!("   Total files: {}", total_files);
        println!("   Total time: {:?}", elapsed);
        println!("   Average time per download: {:?}", elapsed / concurrent_downloads as u32);

        assert!(elapsed.as_secs() < 60, "Load test should complete within 60 seconds");
    }
}
