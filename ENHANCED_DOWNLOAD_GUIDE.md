# 🚀 Tera Launcher 增强下载系统使用指南

## 📋 概述

增强下载系统是为Tera Launcher设计的高性能、高可靠性下载解决方案，具备以下核心特性：

### ✨ 核心特性

1. **🔄 智能故障切换**
   - 5个URL轮询机制
   - 30秒自动切换间隔
   - 60秒地址刷新周期

2. **📥 双模式下载**
   - 全量下载（ZIP压缩包）
   - 增量下载（单文件更新）

3. **🔐 加密内容获取**
   - Gitee/微博双源获取
   - Base64加密解密
   - 自动内容解析

4. **⚡ 高性能优化**
   - 多线程并发下载
   - 断点续传支持
   - 智能分块下载

## 🛠️ 安装与配置

### 1. 依赖要求

确保Cargo.toml包含以下依赖：

```toml
[dependencies]
# 现有依赖...
async-trait = "0.1.74"
bytes = "1.5.0"
uuid = { version = "1.6.1", features = ["v4"] }
base64 = "0.21"
chrono = { version = "0.4", features = ["serde"] }
zip = "0.6"
```

### 2. 模块集成

在main.rs中添加：

```rust
// 增强下载系统模块
mod enhanced_download;
mod enhanced_tauri_commands;

use enhanced_tauri_commands::EnhancedDownloadState;
```

### 3. 状态管理

在Tauri Builder中添加状态管理：

```rust
// 初始化增强下载系统状态
let enhanced_download_state = EnhancedDownloadState::new();

tauri::Builder::default()
    .manage(enhanced_download_state)
    // ... 其他配置
```

## 🎯 API 使用指南

### 1. 初始化系统

```javascript
// 前端调用
await invoke('initialize_enhanced_download_system', {
    giteeUrl: 'https://gitee.com/mango_mu/test',
    weiboUrl: '',
    maxConcurrent: 4,
    enableResume: true
});
```

### 2. 获取更新信息

```javascript
const updateInfo = await invoke('fetch_enhanced_update_info');
console.log('更新信息:', updateInfo);
```

### 3. 批量文件下载

```javascript
// 监听下载进度
listen('enhanced_download_progress', (event) => {
    const progress = event.payload;
    console.log(`下载进度: ${progress.progress_percentage}%`);
    console.log(`下载速度: ${progress.speed_bytes_per_sec} bytes/s`);
});

// 监听下载完成
listen('enhanced_download_complete', (event) => {
    const result = event.payload;
    if (result.success) {
        console.log('下载完成！');
    } else {
        console.error('下载失败:', result.error);
    }
});

// 开始下载
await invoke('enhanced_download_all_files', {
    filesToUpdate: [
        {
            path: 'game/data/file1.dat',
            url: 'https://example.com/file1.dat',
            hash: 'abc123...',
            size: 1024000
        }
        // ... 更多文件
    ]
});
```

### 4. 单文件下载

```javascript
const result = await invoke('enhanced_update_single_file', {
    fileInfo: {
        path: 'game/config.ini',
        url: 'https://example.com/config.ini',
        hash: 'def456...',
        size: 2048
    }
});
```

### 5. 配置管理

```javascript
// 获取当前配置
const config = await invoke('get_enhanced_download_config');

// 更新配置
await invoke('update_enhanced_download_config', {
    newConfig: {
        retry_interval_seconds: 45,
        max_concurrent_downloads: 6,
        enable_resume: true
    }
});
```

## 🔧 配置选项

### EnhancedDownloadConfig

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `gitee_url` | String | "https://gitee.com/mango_mu/test" | Gitee源地址 |
| `weibo_url` | String | "" | 微博源地址 |
| `retry_interval_seconds` | u64 | 30 | 重试间隔（秒） |
| `address_switch_interval_seconds` | u64 | 30 | 地址切换间隔（秒） |
| `max_retry_attempts` | u32 | 5 | 最大重试次数 |
| `download_timeout_seconds` | u64 | 300 | 下载超时时间（秒） |
| `chunk_size` | usize | 1048576 | 分块大小（字节） |
| `max_concurrent_downloads` | usize | 4 | 最大并发下载数 |
| `enable_resume` | bool | true | 启用断点续传 |
| `enable_chunked_download` | bool | true | 启用分块下载 |
| `speed_limit` | Option<u64> | None | 速度限制（bytes/s） |

## 📊 事件系统

### 下载进度事件

```javascript
listen('enhanced_download_progress', (event) => {
    const {
        task_id,
        downloaded_bytes,
        total_bytes,
        speed_bytes_per_sec,
        eta_seconds,
        current_url,
        progress_percentage
    } = event.payload;
    
    // 更新UI进度条
    updateProgressBar(progress_percentage);
    updateSpeedDisplay(speed_bytes_per_sec);
    updateETADisplay(eta_seconds);
});
```

### 下载完成事件

```javascript
listen('enhanced_download_complete', (event) => {
    const {
        success,
        total_files,
        successful_downloads,
        failed_downloads,
        total_downloaded_bytes
    } = event.payload;
    
    if (success) {
        showSuccessMessage(`成功下载 ${successful_downloads} 个文件`);
    } else {
        showErrorMessage(`${failed_downloads} 个文件下载失败`);
    }
});
```

## 🧪 测试与验证

### 1. 运行代码验证

```bash
python verify_code.py
```

### 2. 运行Web测试

打开 `test_enhanced_download.html` 在浏览器中运行完整测试套件。

### 3. 单元测试

```bash
cargo test
```

## 🔍 故障排除

### 常见问题

1. **网络连接失败**
   - 检查防火墙设置
   - 验证URL可访问性
   - 确认代理配置

2. **下载速度慢**
   - 调整 `max_concurrent_downloads`
   - 检查 `speed_limit` 设置
   - 优化 `chunk_size`

3. **文件校验失败**
   - 确认SHA256哈希值正确
   - 检查文件完整性
   - 重新下载文件

### 调试模式

启用详细日志：

```rust
env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
```

## 📈 性能优化建议

### 1. 网络优化
- 根据网络带宽调整并发数
- 使用CDN加速下载
- 启用HTTP/2支持

### 2. 存储优化
- 使用SSD存储提升写入速度
- 预分配磁盘空间
- 定期清理临时文件

### 3. 内存优化
- 调整缓冲区大小
- 使用流式处理大文件
- 及时释放资源

## 🔒 安全考虑

1. **文件完整性**
   - 强制SHA256校验
   - 数字签名验证
   - 恶意文件检测

2. **网络安全**
   - HTTPS强制加密
   - 证书验证
   - 防中间人攻击

3. **权限控制**
   - 最小权限原则
   - 沙箱执行
   - 路径遍历防护

## 📝 更新日志

### v1.0.0 (2024-06-29)
- ✨ 初始版本发布
- 🔄 智能故障切换机制
- 📥 双模式下载支持
- 🔐 加密内容获取
- ⚡ 高性能优化
- 🧪 完整测试套件

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请：

1. 查看本指南的故障排除部分
2. 运行测试验证系统状态
3. 提交 Issue 描述问题
4. 联系开发团队

---

**🎉 恭喜！您已成功集成Tera Launcher增强下载系统！**
