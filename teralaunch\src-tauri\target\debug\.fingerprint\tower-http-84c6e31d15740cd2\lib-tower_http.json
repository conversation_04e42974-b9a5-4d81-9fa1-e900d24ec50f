{"rustc": 1842507548689473721, "features": "[\"cors\", \"default\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 15657897354478470176, "path": 14757345246374513126, "deps": [[1133100163585637996, "tower_service", false, 11194521150169456342], [3664886486575944113, "tower_layer", false, 3323670639348905146], [4405182208873388884, "http", false, 7948823251931111416], [4800206021143169329, "pin_project_lite", false, 4852793665951962081], [6765707116078418611, "bitflags", false, 15290881032493030910], [8915503303801890683, "http_body", false, 8847447128091011592], [11913130400938634928, "futures_util", false, 1357397610844759036], [12588177665552295757, "futures_core", false, 166498837917928535], [16227728351758841112, "bytes", false, 13084351430820299866], [18403471711646444616, "http_range_header", false, 15064527423032449899]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-84c6e31d15740cd2\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}