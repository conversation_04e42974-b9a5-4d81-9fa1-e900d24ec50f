#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本
测试从Gitee获取内容到网盘直链解析的完整流程
"""

import base64
import requests
import time

def test_base64_decryption():
    """测试Base64解密"""
    print("🔓 测试Base64解密")
    print("=" * 50)
    
    # 从Gitee获取的Base64内容
    base64_content = "aHR0cHM6Ly9wYXJzZS5seG93bi5jb20vTmV0ZGlza19kaXJlY3RsaW5rP0NhcmQ9NDZCNUYwMUM4NUZDOEFNSzBESFNHSzlYUklVSSZOZXRkaXNrPWxhbnpvdVUmU2hhcmVMaW5rPWh0dHBzOi8vd3d3LmlsYW56b3UuY29tL3Mvb1Z2WjNaMDc="
    
    try:
        # Base64解码
        decoded_bytes = base64.b64decode(base64_content)
        decoded_url = decoded_bytes.decode('utf-8').strip()
        
        print(f"✅ Base64解密成功")
        print(f"📄 解密后URL: {decoded_url}")
        
        return decoded_url
        
    except Exception as e:
        print(f"❌ Base64解密失败: {e}")
        return None

def test_netdisk_parser(parser_url):
    """测试网盘解析服务"""
    print("\n🌐 测试网盘解析服务")
    print("=" * 50)
    
    try:
        print(f"📡 请求解析服务: {parser_url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        start_time = time.time()
        response = requests.get(parser_url, headers=headers, timeout=30)
        end_time = time.time()
        
        print(f"📊 HTTP状态码: {response.status_code}")
        print(f"⏱️ 响应时间: {(end_time - start_time):.2f}秒")
        print(f"📏 响应长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            direct_url = response.text.strip()
            print(f"📄 响应内容: {direct_url}")
            
            # 检查是否是有效的下载链接
            if direct_url.startswith('http'):
                print("✅ 获取到有效的直接下载链接")
                return direct_url
            else:
                print("⚠️ 响应内容不是有效的下载链接")
                return None
        else:
            print(f"❌ 服务响应异常: {response.status_code}")
            return None
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_direct_download_link(direct_url):
    """测试直接下载链接"""
    print("\n📥 测试直接下载链接")
    print("=" * 50)
    
    try:
        print(f"🔗 测试下载链接: {direct_url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # 使用HEAD请求测试链接有效性
        start_time = time.time()
        response = requests.head(direct_url, headers=headers, timeout=15, allow_redirects=True)
        end_time = time.time()
        
        print(f"📊 HTTP状态码: {response.status_code}")
        print(f"⏱️ 响应时间: {(end_time - start_time):.2f}秒")
        
        if response.status_code == 200:
            # 获取文件信息
            content_length = response.headers.get('Content-Length')
            content_type = response.headers.get('Content-Type')
            last_modified = response.headers.get('Last-Modified')
            
            print("✅ 下载链接有效")
            print(f"📦 文件大小: {format_bytes(int(content_length)) if content_length else '未知'}")
            print(f"📄 文件类型: {content_type or '未知'}")
            print(f"📅 最后修改: {last_modified or '未知'}")
            
            return True
        else:
            print(f"❌ 下载链接无效: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_gitee_page_parsing():
    """测试Gitee页面解析"""
    print("\n📄 测试Gitee页面解析")
    print("=" * 50)
    
    # 读取测试页面
    try:
        with open("test_gitee_netdisk_page.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        
        print("✅ 成功读取测试页面")
        
        # 查找 [[ ]] 标志
        start_marker = "[["
        end_marker = "]]"
        
        start_pos = html_content.find(start_marker)
        if start_pos == -1:
            print("❌ 未找到开始标志 [[")
            return None
        
        end_pos = html_content.find(end_marker, start_pos + len(start_marker))
        if end_pos == -1:
            print("❌ 未找到结束标志 ]]")
            return None
        
        # 提取内容
        extracted_content = html_content[start_pos + len(start_marker):end_pos].strip()
        
        print(f"✅ 成功提取Base64内容")
        print(f"📏 内容长度: {len(extracted_content)} 字符")
        print(f"📄 内容预览: {extracted_content[:50]}...")
        
        return extracted_content
        
    except FileNotFoundError:
        print("❌ 未找到测试页面文件")
        return None
    except Exception as e:
        print(f"❌ 页面解析失败: {e}")
        return None

def format_bytes(bytes_value):
    """格式化字节数"""
    if bytes_value == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    
    while bytes_value >= 1024 and unit_index < len(units) - 1:
        bytes_value /= 1024
        unit_index += 1
    
    return f"{bytes_value:.2f} {units[unit_index]}"

def main():
    print("🧪 Tera Launcher 集成测试套件")
    print("=" * 60)
    print("测试从Gitee获取内容到网盘直链解析的完整流程")
    print()
    
    # 测试1: Gitee页面解析
    extracted_base64 = test_gitee_page_parsing()
    if not extracted_base64:
        print("\n❌ Gitee页面解析失败，无法继续测试")
        return
    
    # 测试2: Base64解密
    parser_url = test_base64_decryption()
    if not parser_url:
        print("\n❌ Base64解密失败，无法继续测试")
        return
    
    # 测试3: 网盘解析服务
    direct_url = test_netdisk_parser(parser_url)
    if not direct_url:
        print("\n❌ 网盘解析失败，无法继续测试")
        return
    
    # 测试4: 直接下载链接
    link_valid = test_direct_download_link(direct_url)
    
    # 测试总结
    print("\n🎯 测试总结")
    print("=" * 50)
    
    tests = [
        ("Gitee页面解析", extracted_base64 is not None),
        ("Base64解密", parser_url is not None),
        ("网盘解析服务", direct_url is not None),
        ("直接下载链接", link_valid)
    ]
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 100:
        print("🎉 所有测试通过！完整流程验证成功")
        print("\n🚀 可以在启动器中使用此完整流程:")
        print("  1. 从Gitee页面获取Base64内容")
        print("  2. 解密Base64获取网盘解析URL")
        print("  3. 调用解析服务获取直接下载链接")
        print("  4. 使用直接链接进行文件下载")
    elif success_rate >= 75:
        print("⚠️ 大部分测试通过，但有一些问题需要修复")
    else:
        print("❌ 多个测试失败，需要检查实现")

if __name__ == "__main__":
    main()
