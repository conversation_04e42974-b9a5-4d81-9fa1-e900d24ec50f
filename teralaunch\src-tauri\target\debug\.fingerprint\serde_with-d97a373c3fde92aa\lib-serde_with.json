{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 7701392951120055508, "profile": 1714560523252611944, "path": 14667371089872086555, "deps": [[1513370559913933046, "serde_with_macros", false, 6599658084274707660], [3956770235326413004, "serde_derive", false, 7072864312040672970], [17174345729924723953, "serde", false, 2591028312899638256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-d97a373c3fde92aa\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}