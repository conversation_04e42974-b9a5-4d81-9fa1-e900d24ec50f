const express = require('express');
const cors = require('cors');
const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

const app = express();
const PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/public', express.static(path.join(__dirname, 'public')));

// 模拟用户数据
const users = {
    'testuser': {
        password: 'testpass',
        characters_count: '3',
        ticket: 'test-ticket-12345',
        permission_level: '1'
    },
    'admin': {
        password: 'admin123',
        characters_count: '5',
        ticket: 'admin-ticket-67890',
        permission_level: '9'
    }
};

// 登录 API
app.post('/tera/LauncherLoginAction', (req, res) => {
    console.log('Login request:', req.body);

    const { login, password } = req.body;

    if (!login || !password) {
        return res.status(400).json({
            Return: false,
            ReturnCode: 400,
            Msg: 'Missing login or password'
        });
    }

    const user = users[login];
    if (!user || user.password !== password) {
        return res.status(401).json({
            Return: false,
            ReturnCode: 401,
            Msg: 'Invalid credentials'
        });
    }

    // 返回成功响应（启动器期望的 JSON 格式）
    const response = {
        Return: true,
        ReturnCode: 200,
        Msg: 'success',
        CharacterCount: user.characters_count,
        Permission: parseInt(user.permission_level),
        Privilege: parseInt(user.permission_level),
        UserNo: Math.floor(Math.random() * 100000) + 1000, // 随机用户ID
        UserName: login,
        AuthKey: user.ticket
    };

    console.log('Login response:', response);
    res.json(response);
});

// 服务器列表 API
app.get('/tera/ServerList.json', (req, res) => {
    const serverList = {
        servers: [
            {
                id: 1,
                name: "Test Server",
                status: "online",
                population: "medium",
                ip: "127.0.0.1",
                port: 10001
            }
        ],
        maintenance: false,
        message: "Welcome to Test Server!"
    };
    
    res.json(serverList);
});

// 哈希文件 API
app.get('/tera/launcher/hash-file.json', async (req, res) => {
    try {
        const hashFilePath = path.join(__dirname, 'data', 'hash-file.json');
        
        if (!await fs.pathExists(hashFilePath)) {
            // 如果哈希文件不存在，生成一个示例
            await generateSampleHashFile();
        }
        
        const hashFile = await fs.readJson(hashFilePath);
        res.json(hashFile);
    } catch (error) {
        console.error('Error serving hash file:', error);
        res.status(500).send('Error loading hash file');
    }
});

// 生成示例哈希文件
async function generateSampleHashFile() {
    const dataDir = path.join(__dirname, 'data');
    const publicDir = path.join(__dirname, 'public');
    
    await fs.ensureDir(dataDir);
    await fs.ensureDir(publicDir);
    
    // 创建一些示例文件
    const sampleFiles = [
        'Tera.exe',
        'TL.exe', 
        'ReleaseRevision.txt',
        'DataCenter_Final_EUR.dat',
        'CookedPC/Art_Data/Packages/CH/CH_CastanArmor.gpk'
    ];
    
    const hashData = {
        files: [],
        version: "1.0.0",
        generated: new Date().toISOString()
    };
    
    for (const fileName of sampleFiles) {
        const filePath = path.join(publicDir, fileName);
        
        // 确保目录存在
        await fs.ensureDir(path.dirname(filePath));
        
        // 创建示例文件内容
        let content = `Sample content for ${fileName}\nGenerated at: ${new Date().toISOString()}`;
        if (fileName === 'Tera.exe') {
            content = 'MZ' + 'A'.repeat(1000); // 模拟可执行文件
        }
        
        await fs.writeFile(filePath, content);
        
        // 计算哈希
        const hash = crypto.createHash('sha256').update(content).digest('hex');
        const stats = await fs.stat(filePath);
        
        hashData.files.push({
            path: fileName.replace(/\\/g, '/'),
            hash: hash,
            size: stats.size
        });
    }
    
    await fs.writeJson(path.join(dataDir, 'hash-file.json'), hashData, { spaces: 2 });
    console.log('Generated sample hash file with', hashData.files.length, 'files');
}

// 健康检查
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        server: 'Tera Test Server'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 Tera Test Server running on http://localhost:${PORT}`);
    console.log('📋 Available endpoints:');
    console.log('  POST /tera/LauncherLoginAction - User login');
    console.log('  GET  /tera/ServerList.json - Server list');
    console.log('  GET  /tera/launcher/hash-file.json - File hashes');
    console.log('  GET  /public/* - Static files');
    console.log('  GET  /health - Health check');
    console.log('');
    console.log('👤 Test users:');
    console.log('  Username: testuser, Password: testpass');
    console.log('  Username: admin, Password: admin123');
});
