{"name": "teralaunch", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "teralaunch", "version": "0.0.0", "devDependencies": {"@tauri-apps/cli": "^1.6.0"}}, "node_modules/@tauri-apps/cli": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-1.6.0.tgz", "integrity": "sha512-DBBpBl6GhTzm8ImMbKkfaZ4fDTykWrC7Q5OXP4XqD91recmDEn2LExuvuiiS3HYe7uP8Eb5B9NPHhqJb+Zo7qQ==", "dev": true, "bin": {"tauri": "tauri.js"}, "engines": {"node": ">= 10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "optionalDependencies": {"@tauri-apps/cli-darwin-arm64": "1.6.0", "@tauri-apps/cli-darwin-x64": "1.6.0", "@tauri-apps/cli-linux-arm-gnueabihf": "1.6.0", "@tauri-apps/cli-linux-arm64-gnu": "1.6.0", "@tauri-apps/cli-linux-arm64-musl": "1.6.0", "@tauri-apps/cli-linux-x64-gnu": "1.6.0", "@tauri-apps/cli-linux-x64-musl": "1.6.0", "@tauri-apps/cli-win32-arm64-msvc": "1.6.0", "@tauri-apps/cli-win32-ia32-msvc": "1.6.0", "@tauri-apps/cli-win32-x64-msvc": "1.6.0"}}, "node_modules/@tauri-apps/cli-darwin-arm64": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-1.6.0.tgz", "integrity": "sha512-SNRwUD9nqGxY47mbY1CGTt/jqyQOU7Ps7Mx/mpgahL0FVUDiCEY/5L9QfEPPhEgccgcelEVn7i6aQHIkHyUtCA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-darwin-x64": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-1.6.0.tgz", "integrity": "sha512-g2/uDR/eeH2arvuawA4WwaEOqv/7jDO/ZLNI3JlBjP5Pk8GGb3Kdy0ro1xQzF94mtk2mOnOXa4dMgAet4sUJ1A==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm-gnueabihf": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-1.6.0.tgz", "integrity": "sha512-EVwf4oRkQyG8BpSrk0gqO7oA0sDM2MdNDtJpMfleYFEgCxLIOGZKNqaOW3M7U+0Y4qikmG3TtRK+ngc8Ymtrjg==", "cpu": ["arm"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-gnu": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-1.6.0.tgz", "integrity": "sha512-YdpY17cAySrhK9dX4BUVEmhAxE2o+6skIEFg8iN/xrDwRxhaNPI9I80YXPatUTX54Kx55T5++25VJG9+3iw83A==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-arm64-musl": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-1.6.0.tgz", "integrity": "sha512-4U628tuf2U8pMr4tIBJhEkrFwt+46dwhXrDlpdyWSZtnop5RJAVKHODm0KbWns4xGKfTW1F3r6sSv+2ZxLcISA==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-gnu": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-1.6.0.tgz", "integrity": "sha512-AKRzp76fVUaJyXj5KRJT9bJyhwZyUnRQU0RqIRqOtZCT5yr6qGP8rjtQ7YhCIzWrseBlOllc3Qvbgw3Yl0VQcA==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-linux-x64-musl": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-1.6.0.tgz", "integrity": "sha512-0edIdq6aMBTaRMIXddHfyAFL361JqulLLd2Wi2aoOie7DkQ2MYh6gv3hA7NB9gqFwNIGE+xtJ4BkXIP2tSGPlg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["linux"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-arm64-msvc": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-arm64-msvc/-/cli-win32-arm64-msvc-1.6.0.tgz", "integrity": "sha512-QwWpWk4ubcwJ1rljsRAmINgB2AwkyzZhpYbalA+MmzyYMREcdXWGkyixWbRZgqc6fEWEBmq5UG73qz5eBJiIKg==", "cpu": ["arm64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-ia32-msvc": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-1.6.0.tgz", "integrity": "sha512-Vtw0yxO9+aEFuhuxQ57ALG43tjECopRimRuKGbtZYDCriB/ty5TrT3QWMdy0dxBkpDTu3Rqsz30sbDzw6tlP3Q==", "cpu": ["ia32"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@tauri-apps/cli-win32-x64-msvc": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-1.6.0.tgz", "integrity": "sha512-h54FHOvGi7+LIfRchzgZYSCHB1HDlP599vWXQQJ/XnwJY+6Rwr2E5bOe/EhqoG8rbGkfK0xX3KPAvXPbUlmggg==", "cpu": ["x64"], "dev": true, "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}}}