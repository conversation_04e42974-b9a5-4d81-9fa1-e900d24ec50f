# 第三方下载库集成方案

## 🎯 方案概述

基于当前 Tera Rust Launcher 的架构，集成第三方下载库可以显著提升下载性能、稳定性和功能丰富度。

## 📊 下载库对比分析

### 1. Aria2 集成方案

#### 优势 ✅
- **多连接下载**: 单文件多线程下载，速度提升 3-5 倍
- **断点续传**: 网络中断自动恢复
- **多协议支持**: HTTP/HTTPS/FTP/BitTorrent
- **智能分片**: 自动优化下载块大小
- **成熟稳定**: 久经考验的下载引擎
- **丰富配置**: 带宽限制、连接数控制等

#### 劣势 ❌
- **外部依赖**: 需要 aria2c 可执行文件
- **进程管理**: 需要管理子进程生命周期
- **通信复杂**: 通过 RPC 或文件接口通信
- **部署复杂**: 需要打包 aria2c 二进制文件

#### 技术实现
```rust
// Aria2 集成示例
use std::process::{Command, Stdio};
use serde_json::Value;

pub struct Aria2Client {
    process: Option<std::process::Child>,
    rpc_url: String,
    secret: String,
}

impl Aria2Client {
    pub async fn new() -> Result<Self, String> {
        // 启动 aria2c RPC 服务
        let mut child = Command::new("aria2c")
            .args(&[
                "--enable-rpc",
                "--rpc-listen-all=false",
                "--rpc-listen-port=6800",
                "--rpc-secret=tera-launcher-secret",
                "--max-connection-per-server=16",
                "--split=16",
                "--min-split-size=1M",
                "--max-concurrent-downloads=8",
                "--continue=true",
                "--auto-file-renaming=false",
            ])
            .stdout(Stdio::null())
            .stderr(Stdio::null())
            .spawn()
            .map_err(|e| format!("Failed to start aria2c: {}", e))?;

        // 等待 RPC 服务启动
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        Ok(Self {
            process: Some(child),
            rpc_url: "http://localhost:6800/jsonrpc".to_string(),
            secret: "tera-launcher-secret".to_string(),
        })
    }

    pub async fn download_file(
        &self,
        url: &str,
        output_path: &str,
        options: DownloadOptions,
    ) -> Result<String, String> {
        let client = reqwest::Client::new();
        
        let request_body = serde_json::json!({
            "jsonrpc": "2.0",
            "id": "tera-launcher",
            "method": "aria2.addUri",
            "params": [
                format!("token:{}", self.secret),
                [url],
                {
                    "dir": std::path::Path::new(output_path).parent().unwrap().to_str().unwrap(),
                    "out": std::path::Path::new(output_path).file_name().unwrap().to_str().unwrap(),
                    "max-connection-per-server": options.max_connections,
                    "split": options.split_count,
                    "max-download-limit": options.speed_limit.map(|s| s.to_string()),
                    "checksum": options.checksum.as_ref().map(|c| format!("sha-256={}", c)),
                }
            ]
        });

        let response = client
            .post(&self.rpc_url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| e.to_string())?;

        let result: Value = response.json().await.map_err(|e| e.to_string())?;
        
        if let Some(gid) = result["result"].as_str() {
            Ok(gid.to_string())
        } else {
            Err("Failed to start download".to_string())
        }
    }

    pub async fn get_download_status(&self, gid: &str) -> Result<DownloadStatus, String> {
        let client = reqwest::Client::new();
        
        let request_body = serde_json::json!({
            "jsonrpc": "2.0",
            "id": "tera-launcher",
            "method": "aria2.tellStatus",
            "params": [
                format!("token:{}", self.secret),
                gid,
                ["status", "totalLength", "completedLength", "downloadSpeed", "errorMessage"]
            ]
        });

        let response = client
            .post(&self.rpc_url)
            .json(&request_body)
            .send()
            .await
            .map_err(|e| e.to_string())?;

        let result: Value = response.json().await.map_err(|e| e.to_string())?;
        
        if let Some(status_obj) = result["result"].as_object() {
            Ok(DownloadStatus {
                status: status_obj["status"].as_str().unwrap_or("unknown").to_string(),
                total_length: status_obj["totalLength"].as_str().unwrap_or("0").parse().unwrap_or(0),
                completed_length: status_obj["completedLength"].as_str().unwrap_or("0").parse().unwrap_or(0),
                download_speed: status_obj["downloadSpeed"].as_str().unwrap_or("0").parse().unwrap_or(0),
                error_message: status_obj["errorMessage"].as_str().map(|s| s.to_string()),
            })
        } else {
            Err("Failed to get download status".to_string())
        }
    }
}

#[derive(Debug)]
pub struct DownloadOptions {
    pub max_connections: u32,
    pub split_count: u32,
    pub speed_limit: Option<u64>, // bytes/sec
    pub checksum: Option<String>,
}

#[derive(Debug)]
pub struct DownloadStatus {
    pub status: String, // "active", "waiting", "paused", "error", "complete", "removed"
    pub total_length: u64,
    pub completed_length: u64,
    pub download_speed: u64,
    pub error_message: Option<String>,
}
```

### 2. libcurl (curl-rust) 集成方案

#### 优势 ✅
- **原生集成**: 直接编译到二进制文件中
- **高性能**: C 库性能优异
- **功能丰富**: 支持各种协议和选项
- **内存控制**: 精确的内存管理
- **无外部依赖**: 不需要额外的可执行文件

#### 劣势 ❌
- **编译复杂**: 需要 C 编译环境
- **API 复杂**: 底层 API 使用复杂
- **平台差异**: 不同平台编译配置不同
- **调试困难**: C 库错误调试困难

#### 技术实现
```rust
// libcurl 集成示例
use curl::easy::{Easy, WriteError};
use curl::multi::{Easy2Handle, Multi};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;

pub struct CurlDownloadManager {
    multi: Multi,
    active_downloads: HashMap<usize, DownloadTask>,
    next_id: usize,
}

pub struct DownloadTask {
    pub id: usize,
    pub url: String,
    pub output_path: String,
    pub progress_callback: Box<dyn Fn(u64, u64) + Send + Sync>,
    pub completed: bool,
    pub error: Option<String>,
}

impl CurlDownloadManager {
    pub fn new() -> Result<Self, String> {
        let multi = Multi::new();
        
        Ok(Self {
            multi,
            active_downloads: HashMap::new(),
            next_id: 0,
        })
    }

    pub fn add_download<F>(
        &mut self,
        url: String,
        output_path: String,
        progress_callback: F,
    ) -> Result<usize, String>
    where
        F: Fn(u64, u64) + Send + Sync + 'static,
    {
        let id = self.next_id;
        self.next_id += 1;

        let mut easy = Easy::new();
        easy.url(&url).map_err(|e| e.to_string())?;
        easy.follow_location(true).map_err(|e| e.to_string())?;
        easy.max_redirections(10).map_err(|e| e.to_string())?;
        
        // 设置连接选项
        easy.connect_timeout(std::time::Duration::from_secs(30)).map_err(|e| e.to_string())?;
        easy.timeout(std::time::Duration::from_secs(300)).map_err(|e| e.to_string())?;
        
        // 设置并发连接
        easy.pipewait(true).map_err(|e| e.to_string())?;
        
        // 创建输出文件
        let output_file = Arc::new(Mutex::new(
            std::fs::File::create(&output_path).map_err(|e| e.to_string())?
        ));
        
        let output_file_clone = output_file.clone();
        easy.write_function(move |data| {
            match output_file_clone.lock() {
                Ok(mut file) => {
                    use std::io::Write;
                    match file.write_all(data) {
                        Ok(_) => Ok(data.len()),
                        Err(_) => Err(WriteError::Pause),
                    }
                }
                Err(_) => Err(WriteError::Pause),
            }
        }).map_err(|e| e.to_string())?;

        // 设置进度回调
        let progress_cb = Arc::new(progress_callback);
        easy.progress(true).map_err(|e| e.to_string())?;
        easy.progress_function(move |total, current, _, _| {
            progress_cb(current as u64, total as u64);
            true
        }).map_err(|e| e.to_string())?;

        // 添加到多句柄管理器
        let easy2 = Easy2::new(easy);
        self.multi.add(easy2).map_err(|e| e.to_string())?;

        let task = DownloadTask {
            id,
            url,
            output_path,
            progress_callback: Box::new(|_, _| {}), // 占位符
            completed: false,
            error: None,
        };

        self.active_downloads.insert(id, task);
        Ok(id)
    }

    pub fn process_downloads(&mut self) -> Result<Vec<usize>, String> {
        let mut completed_ids = Vec::new();
        
        // 处理活跃的下载
        self.multi.perform().map_err(|e| e.to_string())?;
        
        // 检查完成的下载
        self.multi.messages(|msg| {
            if let Some(result) = msg.result() {
                if let Ok(easy2) = msg.token() {
                    // 处理完成的下载
                    // 这里需要根据 easy2 找到对应的下载任务
                    // 实际实现会更复杂
                }
            }
        });

        Ok(completed_ids)
    }

    pub fn get_download_status(&self, id: usize) -> Option<&DownloadTask> {
        self.active_downloads.get(&id)
    }
}
```

### 3. 混合方案：reqwest + 自定义优化

#### 优势 ✅
- **保持现有架构**: 最小化代码变更
- **渐进式优化**: 逐步添加高级功能
- **纯 Rust**: 无外部依赖
- **易于调试**: 全 Rust 代码栈

#### 技术实现
```rust
// 增强的 reqwest 下载器
use reqwest::Client;
use tokio::io::AsyncWriteExt;
use futures_util::StreamExt;
use std::sync::Arc;
use tokio::sync::Semaphore;

pub struct EnhancedDownloader {
    client: Client,
    concurrent_limit: Arc<Semaphore>,
    chunk_size: usize,
}

impl EnhancedDownloader {
    pub fn new(max_concurrent: usize) -> Self {
        let client = Client::builder()
            .pool_max_idle_per_host(max_concurrent)
            .pool_idle_timeout(std::time::Duration::from_secs(30))
            .timeout(std::time::Duration::from_secs(300))
            .tcp_keepalive(std::time::Duration::from_secs(60))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            concurrent_limit: Arc::new(Semaphore::new(max_concurrent)),
            chunk_size: 8192, // 8KB chunks
        }
    }

    pub async fn download_with_resume(
        &self,
        url: &str,
        output_path: &std::path::Path,
        progress_callback: impl Fn(u64, u64) + Send + Sync + 'static,
    ) -> Result<(), String> {
        let _permit = self.concurrent_limit.acquire().await.map_err(|e| e.to_string())?;

        // 检查已存在的文件大小（断点续传）
        let existing_size = if output_path.exists() {
            std::fs::metadata(output_path)
                .map(|m| m.len())
                .unwrap_or(0)
        } else {
            0
        };

        // 创建带 Range 头的请求
        let mut request = self.client.get(url);
        if existing_size > 0 {
            request = request.header("Range", format!("bytes={}-", existing_size));
        }

        let response = request.send().await.map_err(|e| e.to_string())?;
        
        if !response.status().is_success() && response.status().as_u16() != 206 {
            return Err(format!("HTTP error: {}", response.status()));
        }

        let total_size = response.content_length().unwrap_or(0) + existing_size;
        
        // 打开文件进行追加写入
        let mut file = tokio::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(output_path)
            .await
            .map_err(|e| e.to_string())?;

        let mut downloaded = existing_size;
        let mut stream = response.bytes_stream();

        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result.map_err(|e| e.to_string())?;
            file.write_all(&chunk).await.map_err(|e| e.to_string())?;
            
            downloaded += chunk.len() as u64;
            progress_callback(downloaded, total_size);
            
            // 小延迟避免过度占用 CPU
            tokio::task::yield_now().await;
        }

        file.flush().await.map_err(|e| e.to_string())?;
        Ok(())
    }

    // 分片下载（类似 aria2 的多连接下载）
    pub async fn download_with_chunks(
        &self,
        url: &str,
        output_path: &std::path::Path,
        chunk_count: usize,
        progress_callback: impl Fn(u64, u64) + Send + Sync + 'static,
    ) -> Result<(), String> {
        // 首先获取文件大小
        let head_response = self.client.head(url).send().await.map_err(|e| e.to_string())?;
        let total_size = head_response.content_length().ok_or("Cannot get file size")?;
        
        if total_size < 1024 * 1024 { // 小于 1MB 的文件不分片
            return self.download_with_resume(url, output_path, progress_callback).await;
        }

        let chunk_size = total_size / chunk_count as u64;
        let mut tasks = Vec::new();
        let progress_callback = Arc::new(progress_callback);

        // 创建临时文件用于存储分片
        for i in 0..chunk_count {
            let start = i as u64 * chunk_size;
            let end = if i == chunk_count - 1 {
                total_size - 1
            } else {
                (i as u64 + 1) * chunk_size - 1
            };

            let client = self.client.clone();
            let url = url.to_string();
            let temp_path = format!("{}.part{}", output_path.to_string_lossy(), i);
            let progress_cb = progress_callback.clone();

            let task = tokio::spawn(async move {
                let response = client
                    .get(&url)
                    .header("Range", format!("bytes={}-{}", start, end))
                    .send()
                    .await?;

                let mut file = tokio::fs::File::create(&temp_path).await?;
                let mut stream = response.bytes_stream();
                let mut downloaded = 0u64;

                while let Some(chunk_result) = stream.next().await {
                    let chunk = chunk_result?;
                    file.write_all(&chunk).await?;
                    downloaded += chunk.len() as u64;
                    
                    // 这里需要更复杂的进度聚合逻辑
                    progress_cb(downloaded, end - start + 1);
                }

                Ok::<String, Box<dyn std::error::Error + Send + Sync>>(temp_path)
            });

            tasks.push(task);
        }

        // 等待所有分片下载完成
        let temp_files: Result<Vec<_>, _> = futures_util::future::try_join_all(tasks).await
            .map_err(|e| e.to_string())?
            .into_iter()
            .collect();

        let temp_files = temp_files.map_err(|e| e.to_string())?;

        // 合并分片文件
        self.merge_chunks(&temp_files, output_path).await?;

        // 清理临时文件
        for temp_file in temp_files {
            let _ = tokio::fs::remove_file(temp_file).await;
        }

        Ok(())
    }

    async fn merge_chunks(
        &self,
        temp_files: &[String],
        output_path: &std::path::Path,
    ) -> Result<(), String> {
        let mut output_file = tokio::fs::File::create(output_path).await.map_err(|e| e.to_string())?;

        for temp_file in temp_files {
            let mut input_file = tokio::fs::File::open(temp_file).await.map_err(|e| e.to_string())?;
            tokio::io::copy(&mut input_file, &mut output_file).await.map_err(|e| e.to_string())?;
        }

        output_file.flush().await.map_err(|e| e.to_string())?;
        Ok(())
    }
}
```

## 🎯 推荐方案

### 阶段性实施策略

#### Phase 1: 增强现有 reqwest (推荐优先实施)
- **优势**: 最小化风险，保持架构稳定
- **实施**: 添加断点续传、并发控制、分片下载
- **时间**: 1-2 周

#### Phase 2: 可选 Aria2 集成
- **优势**: 最大化性能提升
- **实施**: 作为高级选项提供给用户选择
- **时间**: 2-3 周

#### Phase 3: libcurl 作为备选
- **优势**: 无外部依赖的高性能方案
- **实施**: 在特定场景下使用
- **时间**: 3-4 周

### 配置驱动的实现
```rust
#[derive(Serialize, Deserialize)]
pub enum DownloadEngine {
    Reqwest,
    Aria2,
    Curl,
}

pub struct DownloadConfig {
    pub engine: DownloadEngine,
    pub max_concurrent: usize,
    pub chunk_size: usize,
    pub enable_resume: bool,
    pub enable_chunked: bool,
    pub speed_limit: Option<u64>,
}
```

这样的设计允许用户根据需要选择不同的下载引擎，同时保持代码的灵活性和可维护性。
