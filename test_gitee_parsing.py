#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gitee内容解析测试脚本
测试 [[ ]] 标志格式的内容提取功能
"""

import re
import base64
import json
from typing import Optional, Dict, Any

def print_header(title):
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def print_warning(message):
    print(f"⚠️  {message}")

def print_info(message):
    print(f"ℹ️  {message}")

def parse_gitee_content(content: str) -> Optional[str]:
    """
    解析Gitee内容，提取 [[ ]] 标志之间的内容
    """
    # 查找 [[ ]] 标志
    start_marker = "[["
    end_marker = "]]"
    
    start_pos = content.find(start_marker)
    if start_pos == -1:
        return None
    
    end_pos = content.find(end_marker, start_pos + len(start_marker))
    if end_pos == -1:
        return None
    
    # 提取内容
    extracted = content[start_pos + len(start_marker):end_pos].strip()
    return extracted if extracted else None

def parse_html_comment_content(content: str) -> Optional[str]:
    """
    解析HTML注释格式的内容
    """
    start_marker = "<!-- UPDATE_INFO_START -->"
    end_marker = "<!-- UPDATE_INFO_END -->"
    
    start_pos = content.find(start_marker)
    if start_pos == -1:
        return None
    
    end_pos = content.find(end_marker, start_pos + len(start_marker))
    if end_pos == -1:
        return None
    
    # 提取内容
    extracted = content[start_pos + len(start_marker):end_pos].strip()
    return extracted if extracted else None

def test_content_parsing():
    """测试内容解析功能"""
    print_header("内容解析测试")
    
    # 测试用例
    test_cases = [
        {
            "name": "Gitee格式 - 基本测试",
            "content": """
            <html>
            <body>
                <p>一些内容</p>
                [[ eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9leGFtcGxlLmNvbS9maWxlMS56aXAiXX0= ]]
                <p>更多内容</p>
            </body>
            </html>
            """,
            "expected_type": "gitee_brackets"
        },
        {
            "name": "HTML注释格式",
            "content": """
            <html>
            <body>
                <p>一些内容</p>
                <!-- UPDATE_INFO_START -->
                eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9leGFtcGxlLmNvbS9maWxlMS56aXAiXX0=
                <!-- UPDATE_INFO_END -->
                <p>更多内容</p>
            </body>
            </html>
            """,
            "expected_type": "html_comments"
        },
        {
            "name": "Gitee格式 - 多行内容",
            "content": """
            <div>
            [[ 
            eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9leGFtcGxlLmNvbS9maWxlMS56aXAiLCJodHRwczovL2V4YW1wbGUuY29tL2ZpbGUyLnppcCJdLCJpbmNyZW1lbnRhbF9zZXJ2ZXJfdXJsIjoiaHR0cHM6Ly9leGFtcGxlLmNvbS9pbmNyZW1lbnRhbCIsImZpbGVfbGlzdF91cmwiOiJodHRwczovL2V4YW1wbGUuY29tL2ZpbGVzLmpzb24iLCJjaGVja3N1bSI6ImFiYzEyMyJ9
            ]]
            </div>
            """,
            "expected_type": "gitee_brackets"
        },
        {
            "name": "无标志内容",
            "content": """
            <html>
            <body>
                <p>普通的HTML内容</p>
                <p>没有任何特殊标志</p>
            </body>
            </html>
            """,
            "expected_type": "none"
        },
        {
            "name": "空的标志",
            "content": """
            <html>
            <body>
                <p>一些内容</p>
                [[ ]]
                <p>更多内容</p>
            </body>
            </html>
            """,
            "expected_type": "none"
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print_info(f"测试 {i}: {test_case['name']}")
        
        # 尝试Gitee格式解析
        gitee_content = parse_gitee_content(test_case['content'])
        html_content = parse_html_comment_content(test_case['content'])
        
        extracted_content = gitee_content or html_content
        content_type = "none"
        
        if gitee_content:
            content_type = "gitee_brackets"
        elif html_content:
            content_type = "html_comments"
        
        # 验证结果
        if content_type == test_case['expected_type']:
            print_success(f"  解析结果正确: {content_type}")
            if extracted_content:
                print_info(f"  提取内容长度: {len(extracted_content)} 字符")
                print_info(f"  内容预览: {extracted_content[:50]}...")
            passed_tests += 1
        else:
            print_error(f"  解析结果错误: 期望 {test_case['expected_type']}, 实际 {content_type}")
    
    print_info(f"测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    return passed_tests == total_tests

def test_base64_decoding():
    """测试Base64解码功能"""
    print_header("Base64解码测试")
    
    # 创建测试数据
    test_data = {
        "version": "1.0.0",
        "full_download_urls": [
            "https://example.com/file1.zip",
            "https://example.com/file2.zip"
        ],
        "incremental_server_url": "https://example.com/incremental",
        "file_list_url": "https://example.com/files.json",
        "checksum": "abc123"
    }
    
    # 编码为Base64
    json_str = json.dumps(test_data, ensure_ascii=False)
    encoded = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
    
    print_info(f"原始JSON: {json_str}")
    print_info(f"Base64编码: {encoded}")
    
    # 测试解码
    try:
        decoded_bytes = base64.b64decode(encoded)
        decoded_str = decoded_bytes.decode('utf-8')
        decoded_data = json.loads(decoded_str)
        
        print_success("Base64解码成功")
        print_info(f"解码JSON: {json.dumps(decoded_data, ensure_ascii=False, indent=2)}")
        
        # 验证数据完整性
        if decoded_data == test_data:
            print_success("数据完整性验证通过")
            return True
        else:
            print_error("数据完整性验证失败")
            return False
            
    except Exception as e:
        print_error(f"Base64解码失败: {e}")
        return False

def test_real_world_scenarios():
    """测试真实世界场景"""
    print_header("真实场景测试")
    
    # 模拟真实的Gitee页面内容
    real_gitee_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <title>测试页面 - Gitee</title>
    </head>
    <body>
        <div class="header">
            <h1>项目标题</h1>
        </div>
        <div class="content">
            <p>这是一个测试页面，包含更新信息。</p>
            <div class="update-info">
                <!-- 这里是加密的更新信息 -->
                [[ eyJ2ZXJzaW9uIjoiMS4yLjMiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9naXRlZS5jb20vZmlsZXMvdjEuMi4zL2dhbWUuemlwIiwiaHR0cHM6Ly9iYWNrdXAuZXhhbXBsZS5jb20vZ2FtZS56aXAiLCJodHRwczovL2Nkbi5leGFtcGxlLmNvbS9nYW1lLnppcCJdLCJpbmNyZW1lbnRhbF9zZXJ2ZXJfdXJsIjoiaHR0cHM6Ly91cGRhdGUuZXhhbXBsZS5jb20vaW5jcmVtZW50YWwiLCJmaWxlX2xpc3RfdXJsIjoiaHR0cHM6Ly91cGRhdGUuZXhhbXBsZS5jb20vZmlsZXMuanNvbiIsImNoZWNrc3VtIjoic2hhMjU2OmFiY2RlZjEyMzQ1NiJ9 ]]
            </div>
            <p>更多页面内容...</p>
        </div>
        <div class="footer">
            <p>页面底部信息</p>
        </div>
    </body>
    </html>
    """
    
    print_info("测试真实Gitee页面内容解析...")
    
    # 解析内容
    extracted = parse_gitee_content(real_gitee_content)
    
    if extracted:
        print_success(f"成功提取内容，长度: {len(extracted)} 字符")
        
        # 尝试解码
        try:
            decoded_bytes = base64.b64decode(extracted)
            decoded_str = decoded_bytes.decode('utf-8')
            decoded_data = json.loads(decoded_str)
            
            print_success("成功解码更新信息:")
            print_info(f"  版本: {decoded_data.get('version')}")
            print_info(f"  下载URL数量: {len(decoded_data.get('full_download_urls', []))}")
            print_info(f"  增量服务器: {decoded_data.get('incremental_server_url')}")
            print_info(f"  文件列表URL: {decoded_data.get('file_list_url')}")
            print_info(f"  校验和: {decoded_data.get('checksum')}")
            
            return True
            
        except Exception as e:
            print_error(f"解码失败: {e}")
            return False
    else:
        print_error("未能提取到内容")
        return False

def generate_test_content():
    """生成测试用的Gitee页面内容"""
    print_header("生成测试内容")
    
    # 创建测试更新信息
    update_info = {
        "version": "1.0.0",
        "full_download_urls": [
            "https://gitee.com/files/v1.0.0/tera-client.zip",
            "https://backup1.example.com/tera-client.zip",
            "https://backup2.example.com/tera-client.zip",
            "https://backup3.example.com/tera-client.zip",
            "https://backup4.example.com/tera-client.zip"
        ],
        "incremental_server_url": "https://update.example.com/incremental",
        "file_list_url": "https://update.example.com/files.json",
        "checksum": "sha256:1234567890abcdef"
    }
    
    # 编码为Base64
    json_str = json.dumps(update_info, ensure_ascii=False)
    encoded = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
    
    # 生成完整的HTML页面
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>Tera Launcher 更新信息 - Gitee</title>
</head>
<body>
    <div class="header">
        <h1>Tera Launcher 更新信息</h1>
        <p>最后更新: 2024-06-29</p>
    </div>
    <div class="content">
        <p>这个页面包含Tera Launcher的最新更新信息。</p>
        <div class="update-section">
            <h2>更新信息</h2>
            <p>当前版本: {update_info['version']}</p>
            <!-- 加密的更新信息开始 -->
            [[ {encoded} ]]
            <!-- 加密的更新信息结束 -->
        </div>
        <div class="changelog">
            <h2>更新日志</h2>
            <ul>
                <li>修复了一些已知问题</li>
                <li>优化了下载性能</li>
                <li>增加了新的功能</li>
            </ul>
        </div>
    </div>
    <div class="footer">
        <p>© 2024 Tera Launcher Team</p>
    </div>
</body>
</html>"""
    
    # 保存到文件
    try:
        with open("test_gitee_page.html", "w", encoding="utf-8") as f:
            f.write(html_content)
        print_success("测试页面已生成: test_gitee_page.html")
        print_info(f"Base64编码内容: {encoded}")
        return True
    except Exception as e:
        print_error(f"生成测试页面失败: {e}")
        return False

def main():
    print_header("Gitee内容解析测试套件")
    
    tests = [
        ("内容解析功能", test_content_parsing),
        ("Base64解码功能", test_base64_decoding),
        ("真实场景测试", test_real_world_scenarios),
        ("生成测试内容", generate_test_content),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print_info(f"运行测试: {test_name}")
        try:
            if test_func():
                passed_tests += 1
                print_success(f"测试通过: {test_name}")
            else:
                print_error(f"测试失败: {test_name}")
        except Exception as e:
            print_error(f"测试异常: {test_name} - {e}")
    
    print_header("测试总结")
    success_rate = (passed_tests / total_tests) * 100
    print_info(f"测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 100:
        print_success("🎉 所有测试通过！Gitee内容解析功能准备就绪")
    elif success_rate >= 75:
        print_warning("⚠️ 大部分测试通过，但有一些问题需要修复")
    else:
        print_error("❌ 多个测试失败，需要检查实现")

if __name__ == "__main__":
    main()
