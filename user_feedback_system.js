// Tera Launcher 增强下载系统用户反馈收集
// 收集用户使用体验和性能数据，持续优化系统

class UserFeedbackSystem {
    constructor() {
        this.feedbackData = {
            sessionId: this.generateSessionId(),
            startTime: Date.now(),
            userAgent: navigator.userAgent,
            systemInfo: {},
            downloadEvents: [],
            performanceMetrics: [],
            userActions: [],
            errors: [],
            satisfaction: null
        };
        
        this.isCollectingFeedback = true;
        this.setupEventListeners();
        this.collectSystemInfo();
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 收集系统信息
    async collectSystemInfo() {
        try {
            this.feedbackData.systemInfo = {
                platform: navigator.platform,
                language: navigator.language,
                screenResolution: `${screen.width}x${screen.height}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                connectionType: navigator.connection ? navigator.connection.effectiveType : 'unknown',
                memoryInfo: navigator.deviceMemory || 'unknown',
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown'
            };
        } catch (error) {
            console.warn('Failed to collect system info:', error);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 监听增强下载事件
        window.addEventListener('enhanced-download-progress', (event) => {
            this.recordDownloadEvent('progress', event.detail);
        });

        window.addEventListener('enhanced-download-complete', (event) => {
            this.recordDownloadEvent('complete', event.detail);
        });

        window.addEventListener('enhanced-download-error', (event) => {
            this.recordError('download_error', event.detail);
        });

        // 监听用户操作
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-feedback-action]')) {
                this.recordUserAction(event.target.dataset.feedbackAction, {
                    element: event.target.tagName,
                    text: event.target.textContent.trim()
                });
            }
        });

        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.submitFeedback();
        });

        // 定期收集性能指标
        setInterval(() => {
            this.collectPerformanceMetrics();
        }, 30000); // 每30秒收集一次
    }

    // 记录下载事件
    recordDownloadEvent(type, data) {
        if (!this.isCollectingFeedback) return;

        const event = {
            type: 'download_event',
            subtype: type,
            timestamp: Date.now(),
            data: {
                taskId: data.taskId || data.task_id,
                downloadedBytes: data.downloadedBytes || data.downloaded_bytes,
                totalBytes: data.totalBytes || data.total_bytes,
                speedBytesPerSec: data.speedBytesPerSec || data.speed_bytes_per_sec,
                progressPercentage: data.progressPercentage || data.progress_percentage,
                currentUrl: data.currentUrl || data.current_url,
                success: data.success,
                error: data.error
            }
        };

        this.feedbackData.downloadEvents.push(event);
        
        // 限制事件数量
        if (this.feedbackData.downloadEvents.length > 1000) {
            this.feedbackData.downloadEvents = this.feedbackData.downloadEvents.slice(-500);
        }
    }

    // 记录用户操作
    recordUserAction(action, details = {}) {
        if (!this.isCollectingFeedback) return;

        const userAction = {
            type: 'user_action',
            action: action,
            timestamp: Date.now(),
            details: details
        };

        this.feedbackData.userActions.push(userAction);
        
        // 限制操作记录数量
        if (this.feedbackData.userActions.length > 500) {
            this.feedbackData.userActions = this.feedbackData.userActions.slice(-250);
        }
    }

    // 记录错误
    recordError(type, error) {
        if (!this.isCollectingFeedback) return;

        const errorRecord = {
            type: 'error',
            errorType: type,
            timestamp: Date.now(),
            message: error.message || error.toString(),
            stack: error.stack || 'No stack trace',
            url: window.location.href
        };

        this.feedbackData.errors.push(errorRecord);
        
        // 限制错误记录数量
        if (this.feedbackData.errors.length > 100) {
            this.feedbackData.errors = this.feedbackData.errors.slice(-50);
        }
    }

    // 收集性能指标
    collectPerformanceMetrics() {
        if (!this.isCollectingFeedback) return;

        try {
            const metrics = {
                timestamp: Date.now(),
                memory: performance.memory ? {
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                } : null,
                timing: performance.timing ? {
                    loadEventEnd: performance.timing.loadEventEnd,
                    navigationStart: performance.timing.navigationStart,
                    domContentLoadedEventEnd: performance.timing.domContentLoadedEventEnd
                } : null,
                connection: navigator.connection ? {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt
                } : null
            };

            this.feedbackData.performanceMetrics.push(metrics);
            
            // 限制性能指标数量
            if (this.feedbackData.performanceMetrics.length > 100) {
                this.feedbackData.performanceMetrics = this.feedbackData.performanceMetrics.slice(-50);
            }
        } catch (error) {
            console.warn('Failed to collect performance metrics:', error);
        }
    }

    // 记录用户满意度
    recordSatisfaction(rating, comments = '') {
        this.feedbackData.satisfaction = {
            rating: rating, // 1-5 星级评分
            comments: comments,
            timestamp: Date.now()
        };
        
        this.recordUserAction('satisfaction_rating', { rating, comments });
    }

    // 记录功能使用情况
    recordFeatureUsage(feature, action, success = true, details = {}) {
        this.recordUserAction('feature_usage', {
            feature: feature,
            action: action,
            success: success,
            details: details
        });
    }

    // 记录性能基准
    recordPerformanceBenchmark(benchmarkType, metrics) {
        const benchmark = {
            type: 'performance_benchmark',
            benchmarkType: benchmarkType,
            timestamp: Date.now(),
            metrics: metrics
        };

        this.feedbackData.performanceMetrics.push(benchmark);
    }

    // 生成反馈摘要
    generateFeedbackSummary() {
        const sessionDuration = Date.now() - this.feedbackData.startTime;
        const downloadEvents = this.feedbackData.downloadEvents;
        const errors = this.feedbackData.errors;

        // 计算下载统计
        const completedDownloads = downloadEvents.filter(e => e.subtype === 'complete' && e.data.success);
        const failedDownloads = downloadEvents.filter(e => e.subtype === 'complete' && !e.data.success);
        const totalDownloadedBytes = completedDownloads.reduce((sum, e) => sum + (e.data.downloadedBytes || 0), 0);
        
        // 计算平均下载速度
        const speedMeasurements = downloadEvents
            .filter(e => e.subtype === 'progress' && e.data.speedBytesPerSec > 0)
            .map(e => e.data.speedBytesPerSec);
        const averageSpeed = speedMeasurements.length > 0 
            ? speedMeasurements.reduce((sum, speed) => sum + speed, 0) / speedMeasurements.length 
            : 0;

        return {
            sessionInfo: {
                sessionId: this.feedbackData.sessionId,
                duration: sessionDuration,
                startTime: this.feedbackData.startTime,
                endTime: Date.now()
            },
            downloadStatistics: {
                totalDownloads: completedDownloads.length + failedDownloads.length,
                successfulDownloads: completedDownloads.length,
                failedDownloads: failedDownloads.length,
                successRate: completedDownloads.length + failedDownloads.length > 0 
                    ? (completedDownloads.length / (completedDownloads.length + failedDownloads.length)) * 100 
                    : 0,
                totalDownloadedBytes: totalDownloadedBytes,
                averageSpeed: averageSpeed
            },
            errorStatistics: {
                totalErrors: errors.length,
                errorTypes: [...new Set(errors.map(e => e.errorType))],
                mostCommonError: this.getMostCommonError(errors)
            },
            userEngagement: {
                totalActions: this.feedbackData.userActions.length,
                uniqueFeatures: [...new Set(this.feedbackData.userActions
                    .filter(a => a.action === 'feature_usage')
                    .map(a => a.details.feature))],
                satisfaction: this.feedbackData.satisfaction
            },
            systemPerformance: {
                averageMemoryUsage: this.getAverageMemoryUsage(),
                performanceIssues: this.detectPerformanceIssues()
            }
        };
    }

    // 获取最常见的错误
    getMostCommonError(errors) {
        if (errors.length === 0) return null;
        
        const errorCounts = {};
        errors.forEach(error => {
            errorCounts[error.errorType] = (errorCounts[error.errorType] || 0) + 1;
        });
        
        return Object.keys(errorCounts).reduce((a, b) => 
            errorCounts[a] > errorCounts[b] ? a : b
        );
    }

    // 获取平均内存使用
    getAverageMemoryUsage() {
        const memoryMetrics = this.feedbackData.performanceMetrics
            .filter(m => m.memory && m.memory.usedJSHeapSize)
            .map(m => m.memory.usedJSHeapSize);
        
        return memoryMetrics.length > 0 
            ? memoryMetrics.reduce((sum, usage) => sum + usage, 0) / memoryMetrics.length 
            : 0;
    }

    // 检测性能问题
    detectPerformanceIssues() {
        const issues = [];
        
        // 检查内存泄漏
        const memoryMetrics = this.feedbackData.performanceMetrics
            .filter(m => m.memory && m.memory.usedJSHeapSize)
            .map(m => m.memory.usedJSHeapSize);
        
        if (memoryMetrics.length > 2) {
            const firstHalf = memoryMetrics.slice(0, Math.floor(memoryMetrics.length / 2));
            const secondHalf = memoryMetrics.slice(Math.floor(memoryMetrics.length / 2));
            const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
            const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
            
            if (secondAvg > firstAvg * 1.5) {
                issues.push('potential_memory_leak');
            }
        }
        
        // 检查下载速度问题
        const speedMeasurements = this.feedbackData.downloadEvents
            .filter(e => e.subtype === 'progress' && e.data.speedBytesPerSec > 0)
            .map(e => e.data.speedBytesPerSec);
        
        if (speedMeasurements.length > 0) {
            const averageSpeed = speedMeasurements.reduce((sum, speed) => sum + speed, 0) / speedMeasurements.length;
            if (averageSpeed < 1024 * 1024) { // 小于1MB/s
                issues.push('slow_download_speed');
            }
        }
        
        // 检查错误率
        const totalDownloads = this.feedbackData.downloadEvents.filter(e => e.subtype === 'complete').length;
        const failedDownloads = this.feedbackData.downloadEvents.filter(e => e.subtype === 'complete' && !e.data.success).length;
        
        if (totalDownloads > 0 && (failedDownloads / totalDownloads) > 0.1) {
            issues.push('high_failure_rate');
        }
        
        return issues;
    }

    // 提交反馈数据
    async submitFeedback() {
        if (!this.isCollectingFeedback) return;

        try {
            const summary = this.generateFeedbackSummary();
            const feedbackPayload = {
                summary: summary,
                rawData: this.feedbackData,
                version: '1.0.0',
                submittedAt: Date.now()
            };

            // 在实际环境中，这里会发送到服务器
            console.log('用户反馈数据:', feedbackPayload);
            
            // 保存到本地存储作为备份
            localStorage.setItem(`feedback_${this.feedbackData.sessionId}`, JSON.stringify(feedbackPayload));
            
            // 可以调用Tauri命令保存到文件
            // await invoke('save_user_feedback', { feedback: feedbackPayload });
            
            console.log('用户反馈已提交');
            
        } catch (error) {
            console.error('提交用户反馈失败:', error);
        }
    }

    // 显示反馈收集界面
    showFeedbackDialog() {
        const dialog = document.createElement('div');
        dialog.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 30px; border-radius: 10px; max-width: 500px; width: 90%;">
                    <h3 style="margin-bottom: 20px; color: #333;">📝 用户体验反馈</h3>
                    <p style="margin-bottom: 20px; color: #666;">您对增强下载系统的使用体验如何？</p>
                    
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">满意度评分：</label>
                        <div id="rating-stars" style="font-size: 24px; margin-bottom: 10px;">
                            <span data-rating="1">⭐</span>
                            <span data-rating="2">⭐</span>
                            <span data-rating="3">⭐</span>
                            <span data-rating="4">⭐</span>
                            <span data-rating="5">⭐</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 10px; font-weight: bold;">意见建议：</label>
                        <textarea id="feedback-comments" style="width: 100%; height: 100px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;" placeholder="请分享您的使用体验和改进建议..."></textarea>
                    </div>
                    
                    <div style="text-align: right;">
                        <button id="feedback-cancel" style="margin-right: 10px; padding: 10px 20px; border: 1px solid #ddd; background: white; border-radius: 5px; cursor: pointer;">取消</button>
                        <button id="feedback-submit" style="padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">提交反馈</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        let selectedRating = 0;
        
        // 星级评分交互
        dialog.querySelectorAll('#rating-stars span').forEach(star => {
            star.addEventListener('click', (e) => {
                selectedRating = parseInt(e.target.dataset.rating);
                dialog.querySelectorAll('#rating-stars span').forEach((s, index) => {
                    s.style.opacity = index < selectedRating ? '1' : '0.3';
                });
            });
        });
        
        // 提交反馈
        dialog.querySelector('#feedback-submit').addEventListener('click', () => {
            const comments = dialog.querySelector('#feedback-comments').value;
            this.recordSatisfaction(selectedRating, comments);
            this.submitFeedback();
            document.body.removeChild(dialog);
            
            // 显示感谢消息
            this.showThankYouMessage();
        });
        
        // 取消反馈
        dialog.querySelector('#feedback-cancel').addEventListener('click', () => {
            document.body.removeChild(dialog);
        });
    }

    // 显示感谢消息
    showThankYouMessage() {
        const message = document.createElement('div');
        message.innerHTML = `
            <div style="position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 15px 20px; border-radius: 5px; z-index: 10001;">
                ✅ 感谢您的反馈！我们会持续改进系统。
            </div>
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            document.body.removeChild(message);
        }, 3000);
    }

    // 停止收集反馈
    stopFeedbackCollection() {
        this.isCollectingFeedback = false;
        this.submitFeedback();
    }

    // 获取反馈统计
    getFeedbackStats() {
        return this.generateFeedbackSummary();
    }
}

// 全局反馈系统实例
const userFeedbackSystem = new UserFeedbackSystem();

// 导出反馈系统
window.UserFeedbackSystem = UserFeedbackSystem;
window.userFeedbackSystem = userFeedbackSystem;

// 添加全局错误处理
window.addEventListener('error', (event) => {
    userFeedbackSystem.recordError('javascript_error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error ? event.error.stack : 'No stack trace'
    });
});

// 添加未处理的Promise拒绝处理
window.addEventListener('unhandledrejection', (event) => {
    userFeedbackSystem.recordError('unhandled_promise_rejection', {
        message: event.reason.toString(),
        stack: event.reason.stack || 'No stack trace'
    });
});

console.log('📊 用户反馈收集系统已启动');
console.log('使用 userFeedbackSystem.showFeedbackDialog() 显示反馈界面');
console.log('使用 userFeedbackSystem.getFeedbackStats() 查看反馈统计');
