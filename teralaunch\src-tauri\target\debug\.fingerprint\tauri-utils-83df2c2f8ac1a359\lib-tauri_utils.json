{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 14523740934585808576, "profile": 15657897354478470176, "path": 907919114797877544, "deps": [[561782849581144631, "html5ever", false, 9923367392353928517], [2455542577989222201, "json_patch", false, 13433943567703623575], [3129130049864710036, "memchr", false, 17740640694557240316], [3353796506826114049, "thiserror", false, 6358471964182336296], [3354820211247072069, "serde_with", false, 5933088073183779466], [3851720982022213547, "semver", false, 829930568886643676], [4704408070981680310, "serde_json", false, 11199486250301317617], [6262254372177975231, "kuchiki", false, 11591261650010128215], [6594257425096240790, "windows_version", false, 15256861835829667730], [6997837210367702832, "infer", false, 9172654972221002519], [7314894124883917868, "log", false, 7275729046824401138], [8145704335312028338, "glob", false, 8611815146833412150], [10774583742866405328, "phf", false, 9202305492285903731], [11269248247346606853, "url", false, 12915646722162745668], [12378581237762097513, "brotli", false, 9502345429090629169], [14097227645922246194, "ctor", false, 17173613905339480332], [15622660310229662834, "walkdir", false, 17457494381336860622], [17174345729924723953, "serde", false, 2591028312899638256], [18053436020821374870, "dunce", false, 8109531313742568891]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-83df2c2f8ac1a359\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}