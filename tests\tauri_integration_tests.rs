// Tauri 集成模块单元测试
// 测试 Tauri 命令和状态管理功能

#[cfg(test)]
mod tauri_integration_tests {
    use super::*;
    use crate::tauri_integration::*;
    use crate::optimal_download_system::*;
    use mockito::{mock, Matcher};
    use tempfile::TempDir;
    use tauri::test::{MockRuntime, mock_context};
    use serde_json::json;

    // ============================================================================
    // 1. DownloadState 测试
    // ============================================================================

    #[test]
    fn test_download_state_new() {
        let state = DownloadState::new();
        
        // 验证初始状态
        assert!(state.manager.try_lock().is_ok());
        assert!(state.config.try_lock().is_ok());
        
        // 验证初始值
        let manager_guard = state.manager.try_lock().unwrap();
        assert!(manager_guard.is_none());
        
        let config_guard = state.config.try_lock().unwrap();
        assert_eq!(config_guard.gitee_url, "https://gitee.com/mango_mu/test");
    }

    // ============================================================================
    // 2. initialize_download_system 命令测试
    // ============================================================================

    #[tokio::test]
    async fn test_initialize_download_system_success() {
        let state = DownloadState::new();
        
        let result = initialize_download_system(
            tauri::State::from(&state),
            Some("https://custom-gitee.com/test".to_string()),
            Some("https://custom-weibo.com/test".to_string()),
        ).await;
        
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Download system initialized successfully");
        
        // 验证配置被更新
        let config_guard = state.config.lock().await;
        assert_eq!(config_guard.gitee_url, "https://custom-gitee.com/test");
        assert_eq!(config_guard.weibo_url, "https://custom-weibo.com/test");
        
        // 验证管理器被创建
        let manager_guard = state.manager.lock().await;
        assert!(manager_guard.is_some());
    }

    #[tokio::test]
    async fn test_initialize_download_system_partial_config() {
        let state = DownloadState::new();
        
        let result = initialize_download_system(
            tauri::State::from(&state),
            Some("https://new-gitee.com/test".to_string()),
            None, // 不更新微博URL
        ).await;
        
        assert!(result.is_ok());
        
        // 验证只有Gitee URL被更新
        let config_guard = state.config.lock().await;
        assert_eq!(config_guard.gitee_url, "https://new-gitee.com/test");
        assert_eq!(config_guard.weibo_url, ""); // 保持默认值
    }

    #[tokio::test]
    async fn test_initialize_download_system_no_config() {
        let state = DownloadState::new();
        
        let result = initialize_download_system(
            tauri::State::from(&state),
            None,
            None,
        ).await;
        
        assert!(result.is_ok());
        
        // 验证使用默认配置
        let config_guard = state.config.lock().await;
        assert_eq!(config_guard.gitee_url, "https://gitee.com/mango_mu/test");
    }

    // ============================================================================
    // 3. fetch_update_info 命令测试
    // ============================================================================

    #[tokio::test]
    async fn test_fetch_update_info_success() {
        let state = DownloadState::new();
        
        // 先初始化下载系统
        let _ = initialize_download_system(
            tauri::State::from(&state),
            Some(format!("{}/mango_mu/test", &mockito::server_url())),
            None,
        ).await;

        // 模拟成功的响应
        let update_info = r#"{"version":"1.0.0","full_download_urls":["http://example.com/file1.zip","http://example.com/file2.zip"],"incremental_server_url":"http://example.com/incremental","file_list_url":"http://example.com/files","checksum":"abc123def456"}"#;
        let encrypted_data = base64::encode(update_info);

        let _m = mock("GET", "/mango_mu/test")
            .with_status(200)
            .with_body(format!(r#"
                <!-- UPDATE_INFO_START -->
                {}
                <!-- UPDATE_INFO_END -->
            "#, encrypted_data))
            .create();

        let result = fetch_update_info(tauri::State::from(&state)).await;
        
        assert!(result.is_ok());
        let response = result.unwrap();
        
        assert_eq!(response["success"], true);
        assert_eq!(response["version"], "1.0.0");
        assert_eq!(response["full_download_urls_count"], 2);
        assert_eq!(response["incremental_server_url"], "http://example.com/incremental");
        assert_eq!(response["checksum"], "abc123def456");
    }

    #[tokio::test]
    async fn test_fetch_update_info_not_initialized() {
        let state = DownloadState::new();
        
        let result = fetch_update_info(tauri::State::from(&state)).await;
        
        assert!(result.is_err());
        assert!(result.unwrap_err().contains("Download system not initialized"));
    }

    #[tokio::test]
    async fn test_fetch_update_info_network_error() {
        let state = DownloadState::new();
        
        // 初始化系统但使用无效URL
        let _ = initialize_download_system(
            tauri::State::from(&state),
            Some("http://invalid-domain-12345.com/test".to_string()),
            None,
        ).await;

        let result = fetch_update_info(tauri::State::from(&state)).await;
        
        assert!(result.is_ok());
        let response = result.unwrap();
        
        assert_eq!(response["success"], false);
        assert!(response["error"].as_str().unwrap().contains("Failed to fetch update info"));
    }

    // ============================================================================
    // 4. test_direct_download_url 命令测试
    // ============================================================================

    #[tokio::test]
    async fn test_test_direct_download_url_success() {
        let test_url = format!("{}/test_file.zip", &mockito::server_url());
        
        let _m = mock("HEAD", "/test_file.zip")
            .with_status(200)
            .with_header("content-length", "1024")
            .with_header("content-type", "application/zip")
            .with_header("last-modified", "Wed, 21 Oct 2015 07:28:00 GMT")
            .create();

        let result = test_direct_download_url(test_url).await;
        
        assert!(result.is_ok());
        let response = result.unwrap();
        
        assert_eq!(response["success"], true);
        assert_eq!(response["status_code"], 200);
        assert_eq!(response["content_length"], 1024);
        assert_eq!(response["headers"]["content_type"], "application/zip");
        assert_eq!(response["headers"]["last_modified"], "Wed, 21 Oct 2015 07:28:00 GMT");
    }

    #[tokio::test]
    async fn test_test_direct_download_url_not_found() {
        let test_url = format!("{}/not_found.zip", &mockito::server_url());
        
        let _m = mock("HEAD", "/not_found.zip")
            .with_status(404)
            .create();

        let result = test_direct_download_url(test_url).await;
        
        assert!(result.is_ok());
        let response = result.unwrap();
        
        assert_eq!(response["success"], true); // HEAD请求成功，但状态码是404
        assert_eq!(response["status_code"], 404);
    }

    #[tokio::test]
    async fn test_test_direct_download_url_network_error() {
        let test_url = "http://invalid-domain-xyz123.com/file.zip";

        let result = test_direct_download_url(test_url.to_string()).await;
        
        assert!(result.is_ok());
        let response = result.unwrap();
        
        assert_eq!(response["success"], false);
        assert!(response["error"].as_str().unwrap().len() > 0);
    }

    // ============================================================================
    // 5. parse_netdisk_direct_link 命令测试
    // ============================================================================

    #[tokio::test]
    async fn test_parse_netdisk_direct_link_success() {
        let direct_url = "https://direct-download.example.com/file.zip";
        
        let _m = mock("GET", "/Netdisk_directlink")
            .match_query(Matcher::AllOf(vec![
                Matcher::UrlEncoded("Card".into(), "46B5F01C85FC8AMK0DHSGK9XRIUI".into()),
                Matcher::UrlEncoded("Netdisk".into(), "lanzouU".into()),
                Matcher::UrlEncoded("ShareLink".into(), "https://www.ilanzou.com/s/oVvZ3Z07".into()),
            ]))
            .with_status(200)
            .with_body(direct_url)
            .create();

        // 临时修改解析URL为mockito服务器
        let result = parse_netdisk_direct_link(
            "https://www.ilanzou.com/s/oVvZ3Z07".to_string(),
            "46B5F01C85FC8AMK0DHSGK9XRIUI".to_string(),
            "lanzouU".to_string(),
        ).await;
        
        // 注意：这个测试需要修改实际的解析URL，或者使用依赖注入
        // 在实际实现中，应该将解析服务URL作为配置参数
    }

    #[tokio::test]
    async fn test_parse_netdisk_direct_link_invalid_response() {
        let _m = mock("GET", "/Netdisk_directlink")
            .with_status(200)
            .with_body("Error: Invalid share link")
            .create();

        // 这个测试需要修改实现以支持可配置的解析服务URL
        // 暂时跳过具体实现
    }

    // ============================================================================
    // 6. extract_zip_file 命令测试
    // ============================================================================

    #[tokio::test]
    async fn test_extract_zip_file_success() {
        use std::fs::File;
        use std::io::Write;
        use zip::write::{FileOptions, ZipWriter};

        let temp_dir = TempDir::new().unwrap();
        let zip_path = temp_dir.path().join("test.zip");
        let extract_path = temp_dir.path().join("extracted");

        // 创建测试ZIP文件
        let zip_file = File::create(&zip_path).unwrap();
        let mut zip_writer = ZipWriter::new(zip_file);
        
        zip_writer.start_file("test1.txt", FileOptions::default()).unwrap();
        zip_writer.write_all(b"Content of test1").unwrap();
        
        zip_writer.start_file("folder/test2.txt", FileOptions::default()).unwrap();
        zip_writer.write_all(b"Content of test2").unwrap();
        
        zip_writer.finish().unwrap();

        // 创建模拟窗口（这需要更复杂的设置）
        // 在实际测试中，可能需要使用tauri的测试工具
        
        // 暂时跳过需要Window参数的测试
        // let result = extract_zip_file(
        //     zip_path.to_string_lossy().to_string(),
        //     extract_path.to_string_lossy().to_string(),
        //     mock_window,
        // ).await;
        
        // assert!(result.is_ok());
        // assert!(extract_path.join("test1.txt").exists());
        // assert!(extract_path.join("folder/test2.txt").exists());
    }

    // ============================================================================
    // 7. 数据结构和序列化测试
    // ============================================================================

    #[test]
    fn test_json_serialization() {
        // 测试进度数据的JSON序列化
        let progress_payload = json!({
            "type": "full_download_progress",
            "downloaded_bytes": 1024,
            "total_bytes": 2048,
            "speed_bytes_per_sec": 512,
            "eta_seconds": 2,
            "current_url": "http://example.com/file.zip",
            "progress_percentage": 50.0
        });

        assert_eq!(progress_payload["type"], "full_download_progress");
        assert_eq!(progress_payload["downloaded_bytes"], 1024);
        assert_eq!(progress_payload["progress_percentage"], 50.0);
    }

    #[test]
    fn test_file_update_info_json_parsing() {
        let json_data = json!([
            {
                "path": "game/config.xml",
                "hash": "abc123",
                "size": 1024,
                "local_path": "/game/config.xml"
            },
            {
                "path": "game/data.bin",
                "hash": "def456",
                "size": 2048,
                "local_path": "/game/data.bin"
            }
        ]);

        // 验证JSON结构
        assert!(json_data.is_array());
        let array = json_data.as_array().unwrap();
        assert_eq!(array.len(), 2);
        
        let first_file = &array[0];
        assert_eq!(first_file["path"], "game/config.xml");
        assert_eq!(first_file["hash"], "abc123");
        assert_eq!(first_file["size"], 1024);
    }

    // ============================================================================
    // 8. 错误处理测试
    // ============================================================================

    #[test]
    fn test_error_response_format() {
        let error_payload = json!({
            "type": "full_download_error",
            "success": false,
            "error": "Network connection failed"
        });

        assert_eq!(error_payload["success"], false);
        assert_eq!(error_payload["error"], "Network connection failed");
    }

    #[test]
    fn test_completion_response_format() {
        let completion_payload = json!({
            "type": "full_download_complete",
            "success": true
        });

        assert_eq!(completion_payload["success"], true);
        assert_eq!(completion_payload["type"], "full_download_complete");
    }

    // ============================================================================
    // 9. 配置验证测试
    // ============================================================================

    #[test]
    fn test_update_config_validation() {
        let config = UpdateConfig {
            gitee_url: "https://gitee.com/test".to_string(),
            weibo_url: "https://weibo.com/test".to_string(),
            retry_interval_seconds: 30,
            address_switch_interval_seconds: 30,
            max_retry_attempts: 5,
            download_timeout_seconds: 300,
            chunk_size: 1024 * 1024,
        };

        // 验证URL格式
        assert!(config.gitee_url.starts_with("https://"));
        assert!(config.weibo_url.starts_with("https://"));
        
        // 验证数值范围
        assert!(config.retry_interval_seconds > 0);
        assert!(config.max_retry_attempts > 0);
        assert!(config.download_timeout_seconds > 0);
        assert!(config.chunk_size > 0);
    }

    // ============================================================================
    // 10. 集成测试辅助函数
    // ============================================================================

    fn create_mock_file_update_info(count: usize, temp_dir: &TempDir) -> Vec<serde_json::Value> {
        let mut files = Vec::new();
        
        for i in 0..count {
            let file_json = json!({
                "path": format!("file{}.txt", i),
                "hash": format!("hash{}", i),
                "size": 1024 * (i + 1),
                "local_path": temp_dir.path().join(format!("file{}.txt", i)).to_string_lossy()
            });
            files.push(file_json);
        }
        
        files
    }

    #[test]
    fn test_create_mock_file_update_info() {
        let temp_dir = TempDir::new().unwrap();
        let files = create_mock_file_update_info(3, &temp_dir);
        
        assert_eq!(files.len(), 3);
        assert_eq!(files[0]["path"], "file0.txt");
        assert_eq!(files[1]["size"], 2048);
        assert_eq!(files[2]["hash"], "hash2");
    }

    // ============================================================================
    // 11. 性能和并发测试
    // ============================================================================

    #[tokio::test]
    async fn test_concurrent_state_access() {
        let state = DownloadState::new();
        
        // 创建多个并发任务来访问状态
        let mut tasks = Vec::new();
        
        for i in 0..10 {
            let state_clone = &state;
            let task = tokio::spawn(async move {
                let result = initialize_download_system(
                    tauri::State::from(state_clone),
                    Some(format!("https://gitee.com/test{}", i)),
                    None,
                ).await;
                result.is_ok()
            });
            tasks.push(task);
        }
        
        let results = futures::future::join_all(tasks).await;
        
        // 验证所有任务都成功完成
        for result in results {
            assert!(result.is_ok());
            assert!(result.unwrap());
        }
    }

    #[tokio::test]
    async fn test_state_consistency_under_load() {
        let state = DownloadState::new();
        
        // 初始化系统
        let _ = initialize_download_system(
            tauri::State::from(&state),
            Some("https://gitee.com/test".to_string()),
            Some("https://weibo.com/test".to_string()),
        ).await;
        
        // 并发读取配置
        let mut read_tasks = Vec::new();
        
        for _ in 0..20 {
            let state_clone = &state;
            let task = tokio::spawn(async move {
                let config_guard = state_clone.config.lock().await;
                config_guard.gitee_url.clone()
            });
            read_tasks.push(task);
        }
        
        let results = futures::future::join_all(read_tasks).await;
        
        // 验证所有读取都返回相同的值
        for result in results {
            assert!(result.is_ok());
            assert_eq!(result.unwrap(), "https://gitee.com/test");
        }
    }
}
