{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2831225513493081318, "build_script_build", false, 1597570457423125683]], "local": [{"RerunIfChanged": {"output": "release\\build\\teralaunch-d35116a2db8d0682\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}