<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gitee内容获取测试 - [[ ]] 标志格式</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .result-box {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .format-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }

        .format-example h4 {
            margin-top: 0;
            color: #495057;
        }

        .format-example code {
            background: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Gitee内容获取测试</h1>
        <p style="text-align: center; color: #6c757d;">测试从Gitee获取 [[ ]] 标志格式的加密内容</p>
        
        <div class="test-section">
            <h2>📋 测试配置</h2>
            
            <div class="input-group">
                <label for="gitee-url">Gitee URL:</label>
                <input type="text" id="gitee-url" value="https://gitee.com/mango_mu/test" 
                       placeholder="输入Gitee页面URL">
            </div>
            
            <div class="format-example">
                <h4>📝 支持的内容格式</h4>
                <p><strong>主要格式（Gitee）:</strong></p>
                <code>[[ 这里是加密的内容数据 ]]</code>
                
                <p style="margin-top: 10px;"><strong>备用格式（HTML注释）:</strong></p>
                <code>&lt;!-- UPDATE_INFO_START --&gt; 加密内容 &lt;!-- UPDATE_INFO_END --&gt;</code>
            </div>
            
            <div class="controls">
                <button onclick="testGiteeContent()">🔍 测试Gitee内容获取</button>
                <button onclick="testWithCustomUrl()">🌐 使用自定义URL测试</button>
                <button onclick="clearResults()">🗑️ 清空结果</button>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results" class="result-box info">等待测试...</div>
            
            <div class="stats-grid" id="stats-grid" style="display: none;">
                <div class="stat-item">
                    <div class="stat-label">HTTP状态码</div>
                    <div class="stat-value" id="status-code">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">内容长度</div>
                    <div class="stat-value" id="content-length">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">提取内容长度</div>
                    <div class="stat-value" id="extracted-length">-</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">内容格式</div>
                    <div class="stat-value" id="content-type">-</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📄 提取的内容</h2>
            <div id="extracted-content" class="result-box info">等待内容提取...</div>
        </div>

        <div class="test-section">
            <h2>🔍 原始内容预览</h2>
            <div id="raw-content" class="result-box info">等待原始内容...</div>
        </div>
    </div>

    <script>
        // 模拟Tauri invoke函数（用于测试）
        function mockInvoke(command, args) {
            console.log('Mock invoke:', command, args);
            
            if (command === 'test_gitee_content_fetch') {
                // 模拟不同的响应
                const url = args.giteeUrl;
                
                if (url.includes('gitee.com')) {
                    return Promise.resolve({
                        success: true,
                        status_code: 200,
                        content_length: 1024,
                        has_gitee_markers: true,
                        has_html_markers: false,
                        content_type: "gitee_brackets",
                        extracted_content: "eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9leGFtcGxlLmNvbS9maWxlMS56aXAiXX0=",
                        extracted_length: 88,
                        content_preview: "<!DOCTYPE html>\n<html>\n<head><title>Test Page</title></head>\n<body>\n<p>Some content before</p>\n[[ eyJ2ZXJzaW9uIjoiMS4wLjAiLCJmdWxsX2Rvd25sb2FkX3VybHMiOlsiaHR0cHM6Ly9leGFtcGxlLmNvbS9maWxlMS56aXAiXX0= ]]\n<p>Some content after</p>\n</body>\n</html>",
                        headers: {
                            content_type: "text/html; charset=utf-8",
                            content_encoding: null
                        }
                    });
                } else {
                    return Promise.resolve({
                        success: false,
                        error: "Network error: Connection timeout"
                    });
                }
            }
            
            return Promise.reject(new Error('Unknown command'));
        }

        // 使用真实的Tauri invoke或模拟函数
        const invoke = window.__TAURI__ ? window.__TAURI__.invoke : mockInvoke;

        async function testGiteeContent() {
            const url = document.getElementById('gitee-url').value;
            const resultsDiv = document.getElementById('test-results');
            const statsGrid = document.getElementById('stats-grid');
            const extractedDiv = document.getElementById('extracted-content');
            const rawDiv = document.getElementById('raw-content');
            
            if (!url.trim()) {
                showResult('❌ 请输入Gitee URL', 'error');
                return;
            }
            
            showResult('🔄 正在获取Gitee内容...', 'info');
            
            try {
                const result = await invoke('test_gitee_content_fetch', {
                    giteeUrl: url
                });
                
                console.log('Gitee test result:', result);
                
                if (result.success) {
                    // 显示成功结果
                    let resultText = `✅ Gitee内容获取成功！\n\n`;
                    resultText += `📊 基本信息:\n`;
                    resultText += `- HTTP状态码: ${result.status_code}\n`;
                    resultText += `- 内容长度: ${result.content_length} bytes\n`;
                    resultText += `- 包含 [[ ]] 标志: ${result.has_gitee_markers ? '是' : '否'}\n`;
                    resultText += `- 包含HTML注释: ${result.has_html_markers ? '是' : '否'}\n`;
                    resultText += `- 内容格式: ${result.content_type}\n`;
                    
                    if (result.extracted_content) {
                        resultText += `\n🎯 内容提取:\n`;
                        resultText += `- 提取成功: 是\n`;
                        resultText += `- 提取长度: ${result.extracted_length} bytes\n`;
                    } else {
                        resultText += `\n⚠️ 未找到可提取的内容\n`;
                    }
                    
                    showResult(resultText, 'success');
                    
                    // 更新统计信息
                    updateStats(result);
                    
                    // 显示提取的内容
                    if (result.extracted_content) {
                        showExtractedContent(result.extracted_content);
                        
                        // 尝试解码Base64内容
                        try {
                            const decoded = atob(result.extracted_content);
                            extractedDiv.innerHTML = `<div class="success">
                                <strong>📄 提取的加密内容:</strong><br>
                                ${result.extracted_content}<br><br>
                                <strong>🔓 Base64解码结果:</strong><br>
                                ${decoded}
                            </div>`;
                        } catch (e) {
                            extractedDiv.innerHTML = `<div class="warning">
                                <strong>📄 提取的内容:</strong><br>
                                ${result.extracted_content}<br><br>
                                <strong>⚠️ Base64解码失败:</strong> ${e.message}
                            </div>`;
                        }
                    } else {
                        extractedDiv.innerHTML = `<div class="warning">❌ 未找到 [[ ]] 或 HTML注释格式的内容</div>`;
                    }
                    
                    // 显示原始内容预览
                    if (result.content_preview) {
                        rawDiv.innerHTML = `<div class="info">
                            <strong>📄 原始内容预览 (前500字符):</strong><br>
                            ${escapeHtml(result.content_preview)}
                        </div>`;
                    }
                    
                } else {
                    showResult(`❌ Gitee内容获取失败:\n${result.error}`, 'error');
                    statsGrid.style.display = 'none';
                    extractedDiv.innerHTML = `<div class="error">获取失败，无法提取内容</div>`;
                    rawDiv.innerHTML = `<div class="error">获取失败，无原始内容</div>`;
                }
                
            } catch (error) {
                console.error('Test error:', error);
                showResult(`❌ 测试失败: ${error.message}`, 'error');
                statsGrid.style.display = 'none';
            }
        }

        async function testWithCustomUrl() {
            const customUrl = prompt('请输入自定义URL:', 'https://gitee.com/your-repo/your-page');
            if (customUrl) {
                document.getElementById('gitee-url').value = customUrl;
                await testGiteeContent();
            }
        }

        function showResult(message, type) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.textContent = message;
            resultsDiv.className = `result-box ${type}`;
        }

        function updateStats(result) {
            const statsGrid = document.getElementById('stats-grid');
            
            document.getElementById('status-code').textContent = result.status_code || '-';
            document.getElementById('content-length').textContent = result.content_length ? 
                `${result.content_length} bytes` : '-';
            document.getElementById('extracted-length').textContent = result.extracted_length ? 
                `${result.extracted_length} bytes` : '-';
            document.getElementById('content-type').textContent = result.content_type || '-';
            
            statsGrid.style.display = 'grid';
        }

        function showExtractedContent(content) {
            const extractedDiv = document.getElementById('extracted-content');
            extractedDiv.innerHTML = `<div class="success">
                <strong>🎯 成功提取的内容:</strong><br>
                ${content}
            </div>`;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '<div class="info">等待测试...</div>';
            document.getElementById('extracted-content').innerHTML = '<div class="info">等待内容提取...</div>';
            document.getElementById('raw-content').innerHTML = '<div class="info">等待原始内容...</div>';
            document.getElementById('stats-grid').style.display = 'none';
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Gitee内容测试页面已加载');
            
            // 检查是否在Tauri环境中
            if (window.__TAURI__) {
                console.log('✅ 检测到Tauri环境，将使用真实API');
            } else {
                console.log('⚠️ 未检测到Tauri环境，将使用模拟数据');
                showResult('⚠️ 当前在浏览器环境中运行，使用模拟数据进行测试', 'warning');
            }
        });
    </script>
</body>
</html>
