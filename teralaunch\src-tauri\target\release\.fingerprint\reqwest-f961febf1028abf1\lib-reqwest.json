{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"stream\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 3029795730754268960, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [442316063022428917, "rustls_pemfile", false, 5622977421871217603], [1043006064470677735, "h2", false, 15091259614074383993], [1076501750996383263, "once_cell", false, 3317331631347182004], [1133100163585637996, "tower_service", false, 2974626735743812269], [4704408070981680310, "serde_json", false, 7911045850117905746], [4800206021143169329, "pin_project_lite", false, 17094343566241858788], [7314894124883917868, "log", false, 17595646964979077647], [8739336841983680076, "http_body", false, 18076514348252398151], [8858041990736880586, "tokio", false, 15737707881525506531], [9351479248917069247, "encoding_rs", false, 3662838284785351967], [9648403166091088614, "native_tls_crate", false, 4956322636387755523], [10036721834787556336, "http_body_util", false, 17904233413888387543], [10229185211513642314, "mime", false, 9651018063964816897], [11269248247346606853, "url", false, 15232645414707203371], [11899474743205156606, "hyper_util", false, 10255875846058341751], [11913130400938634928, "futures_util", false, 7705237049435048172], [12186126227181294540, "tokio_native_tls", false, 10247613043657730443], [12588177665552295757, "futures_core", false, 2275492903305144867], [13077212702700853852, "base64", false, 16878550808635762142], [13410946177877314675, "sync_wrapper", false, 12753798093823770594], [14807769047053305096, "hyper", false, 2933167877641407987], [15966749864389115859, "tokio_util", false, 1319105293102712446], [16227728351758841112, "bytes", false, 15932244784508788198], [16253538096806714255, "ipnet", false, 18393770253228454667], [16542808166767769916, "serde_urlencoded", false, 991891918213886282], [17174345729924723953, "serde", false, 10483066152181423585], [17860019243264344128, "http", false, 3332389641451044583], [18222702447897302889, "windows_registry", false, 468023176945759096], [18273243456331255970, "hyper_tls", false, 8526965307894848451]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-f961febf1028abf1\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}