{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 15657897354478470176, "path": 565762688068940322, "deps": [[40386456601120721, "percent_encoding", false, 4129156245155979692], [264090853244900308, "sync_wrapper", false, 700086306850985125], [1133100163585637996, "tower_service", false, 11194521150169456342], [3129130049864710036, "memchr", false, 17740640694557240316], [3601586811267292532, "tower", false, 7803599073873590924], [3664886486575944113, "tower_layer", false, 3323670639348905146], [4405182208873388884, "http", false, 7948823251931111416], [4800206021143169329, "pin_project_lite", false, 4852793665951962081], [5459299554234637951, "async_trait", false, 11988379071944273682], [8915503303801890683, "http_body", false, 8847447128091011592], [9293824762099617471, "axum_core", false, 619479351877335150], [9678799920983747518, "matchit", false, 9166599925584472642], [10229185211513642314, "mime", false, 12149313404929737718], [10435729446543529114, "bitflags", false, 11820146153564172148], [11356286270989087318, "itoa", false, 7059142318484861074], [11510052217253957479, "hyper", false, 14035793278543346265], [11913130400938634928, "futures_util", false, 1357397610844759036], [16227728351758841112, "bytes", false, 13084351430820299866], [16244562316228021087, "build_script_build", false, 1666497226428475132], [17174345729924723953, "serde", false, 2591028312899638256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-5557fc01a01b4dcb\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}