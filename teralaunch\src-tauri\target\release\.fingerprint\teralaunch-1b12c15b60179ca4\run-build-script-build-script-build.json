{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2831225513493081318, "build_script_build", false, 11481496780526688740]], "local": [{"RerunIfChanged": {"output": "release\\build\\teralaunch-1b12c15b60179ca4\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}